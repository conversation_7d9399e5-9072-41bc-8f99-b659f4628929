package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.manager.RedisManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WorkspaceSyncSseService 测试类
 * 测试多节点部署下的SSE实现
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkspaceSyncSseServiceTest {

    @Mock
    private RedisManager redisManager;

    @InjectMocks
    private WorkspaceSyncSseServiceImpl workspaceSyncSseService;

    @Test
    public void testAddEmitter() {
        // 准备测试数据
        String workspaceId = "test-workspace-001";
        SseEmitter emitter = new SseEmitter(60000L);

        // 执行测试
        workspaceSyncSseService.addEmitter(workspaceId, emitter);

        // 验证结果
        // 这里主要验证没有抛出异常，实际的连接管理在内存中
        assert true; // 简单验证
    }

    @Test
    public void testSendSyncStatus() {
        // 准备测试数据
        String workspaceId = "test-workspace-001";
        String status = "SYNCING";

        // Mock Redis发布操作
        when(redisManager.publish(anyString(), anyString())).thenReturn(1L);

        // 执行测试
        workspaceSyncSseService.sendSyncStatus(workspaceId, status);

        // 验证Redis发布被调用
        verify(redisManager, times(1)).publish(
                eq("workspace_sync_status:" + workspaceId), 
                contains(status)
        );
    }

    @Test
    public void testMultiNodeScenario() {
        // 模拟多节点场景
        String workspaceId = "test-workspace-002";
        String status = "COMPLETED";

        // 添加一个SSE连接
        SseEmitter emitter = new SseEmitter(60000L);
        workspaceSyncSseService.addEmitter(workspaceId, emitter);

        // Mock Redis发布
        when(redisManager.publish(anyString(), anyString())).thenReturn(2L); // 模拟2个节点收到消息

        // 发送状态更新
        workspaceSyncSseService.sendSyncStatus(workspaceId, status);

        // 验证Redis发布被调用（确保跨节点通信）
        verify(redisManager, times(1)).publish(
                eq("workspace_sync_status:" + workspaceId),
                contains(status)
        );
    }
}
