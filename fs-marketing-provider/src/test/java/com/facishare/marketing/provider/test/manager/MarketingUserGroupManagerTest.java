/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.AddMarketingUserToGroupArg;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.test.BaseTest;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/1/30.
 */
public class MarketingUserGroupManagerTest extends BaseTest {
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;

    @Test
    public void getDataByRuleGroup() throws IOException {
        String ea = "7";
        Integer fsUserId = 1001;
        String ruleGroupArg = "{\n" + "    \"ruleGroup\":[\n" + " {\n" + "  \"objectAPIName\": \"ScheduleObj\",\n" + "  \"query\": {\n" + "    \"limit\": 0,\n" + "    \"filters\": [\n" + "      {\n"
            + "        \"field_name\": \"activated\",\n" + "        \"operator\": \"EQ\",\n" + "        \"field_values\": [\n" + "          \"true\"\n" + "        ]\n" + "      },\n" + "      {\n"
            + "        \"field_name\": \"begin_time\",\n" + "        \"operator\": \"LTE\",\n" + "        \"field_values\": [\n" + "          \"1538409599999\"\n" + "        ]\n" + "      },\n"
            + "      {\n" + "        \"field_name\": \"end_time\",\n" + "        \"operator\": \"GTE\",\n" + "        \"field_values\": [\n" + "          \"1537027200000\"\n" + "        ]\n"
            + "      }\n" + "    ],\n" + "    \"offset\": 0\n" + "  }\n" + "} " + "    ]\n" + "}\n" + "\n";
    }

    @Test
    public void testListWxOpenIdByUserGroupIds(){
        List list  = marketingUserGroupManager.listWxOpenIdByMarketingUserGroupIds("74164", ImmutableList.of("e88e08b5f37b40719ac0810dbfb0cc8b"), "wxc94eb91eeee3d13c");
        System.out.println(list);
    }

    @Test
    public void testListEmailByMarketingUserGroupIds(){
        Set set  = marketingUserGroupManager.listEmailByMarketingUserGroupIds("74164", ImmutableSet.of("fe64af41814f4b61a1cf55d0ffa7581c"));
        System.out.println(set);
    }
    
    @Test
    public void testAsyncCalculateUserMarketingAccountData() throws Exception{
        marketingUserGroupManager.asyncCalculateUserMarketingAccountData("88146", 1000, "abb2ec22a1164f0bb6f111ce825fbb33");
        //marketingUserGroupManager.asyncCalculateUserMarketingAccountData("fsceshi038", "a9b5f94a7c2146609c9d6cdc6b2b9e1e");
        
        //List<String> marketingUserGroupIds = marketingUserGroupDao.listByEa("74164").stream().map(MarketingUserGroupEntity::getId).collect(Collectors.toList());
        //for (String marketingUserGroupId : marketingUserGroupIds) {
        //    marketingUserGroupManager.asyncCalculateUserMarketingAccountData("74164", marketingUserGroupId);
        //}
      //  Thread.sleep(**********);
    }

    @Test
    public void batchGetMarketingUserIdByMarketingUserGroupIdsTest() {

        long t1 = System.currentTimeMillis();
        Set<String> strings = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds("74164",
                Lists.newArrayList("c960c2daaaf140f1b3ebef20ef3e13a8", "3586b7050fc54850ac56456841db6bed","4cb5134221b24830b47aaefac9442243","ac9e5ea3e5a0456189eb360769ebf4e0",
                        "25e97ddda9a54d5596a222026aeced5c","25e97ddda9a54d5596a222026aeced5c", "25e97ddda9a54d5596a222026aeced5c","96f4d76e34ea4f319e940612d769af3f"));
        long t2 = System.currentTimeMillis();

        System.out.println("v1 结果 耗时：" + (t2 - t1));
        System.out.println("v1 结果: size：" + strings.size() );
    }

    @Test
    public void addMarketingUsersToStatisticMarketingUserGroup() {
        String a = "{\"marketingUserGroupId\":\"89b3751267884de7b6142d71e7ce4783\",\"addType\":5,\"ruleGroupJson\":[{\"objectAPIName\":\"WechatWorkExternalUserObj\",\"query\":{\"filters\":[{\"field_name\":\"create_time\",\"operator\":\"BETWEEN\",\"field_values\":[\"7\"],\"value_type\":3,\"fieldType\":\"4\"}]}}],\"tagNames\":[],\"description\":\"企业微信客户.创建时间时间段7\",\"dataRange\":[]}";
        AddMarketingUserToGroupArg copy = JSONObject.parseObject(a, AddMarketingUserToGroupArg.class);
        marketingUserGroupManager.addMarketingUsersToStatisticMarketingUserGroup("88146",1000,copy);

    }

}
