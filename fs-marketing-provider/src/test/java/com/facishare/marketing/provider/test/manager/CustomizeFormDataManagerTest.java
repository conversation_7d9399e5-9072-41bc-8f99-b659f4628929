/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.customizeFormData.DeleteEnrollDataArg;
import com.facishare.marketing.api.arg.customizeFormData.FormDataUserArgContainer;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.QueryFormUserDataResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.enums.CustomizeFormDataUserSourceTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.QueryFormUserDataTypeEnum;
import com.facishare.marketing.common.enums.SaveCrmStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager.CustomizeFormQrCodeContainer;
import com.facishare.marketing.provider.test.BaseTest;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created  By zhoux 2019/05/21
 **/
@Slf4j
public class CustomizeFormDataManagerTest extends BaseTest {

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Test
    public void checkAddLeadsObjectAuth() {
        boolean result = customizeFormDataManager.checkAddLeadsObjectAuth("2", 1000);
        Assert.assertEquals(result, true);
    }

    @Test
    public void getBindFormDataByObject() {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject("55487", "f6e9268fcaf046b9b26a3068ee047066", ObjectTypeEnum.ACTIVITY.getType());
        Assert.assertNotEquals(customizeFormDataEntity, null);
    }

    @Test
    public void getLatestEnrollDataForm() {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getLatestEnrollDataForm(QueryFormUserDataTypeEnum.OBJECT.getType(), "7a96c6a4dd3f4c07a93643184ec62550", ObjectTypeEnum.ACTIVITY.getType(),null, null,  "2");
        Assert.assertNotEquals(customizeFormDataEntity, null);
    }


    @Test
    public void getCustomizeFormDataUser() {
        /*Page page = new Page(1, 10, true);
        PageResult<QueryFormUserDataResult> queryFormUserDataResultPageResult = customizeFormDataManager.getCustomizeFormDataUser("74164", "eaab7e54e79c4e5aa9b5b9c946f72ab2", ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, null, QueryFormUserDataTypeEnum.ENROLL_SOURCE_TYPE.getType(),1,
            CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType(),  page);
        Assert.assertNotEquals(queryFormUserDataResultPageResult, null);*/
    }

    @Test
    public void buildExportEnrollsData() {
        ExportEnrollsDataResult exportEnrollsDataResult = customizeFormDataManager.buildExportEnrollsData("88146", "bf8c273383db449b833aabe67da3fd1e", 16, null,  null, QueryFormUserDataTypeEnum.MARKETING_EVENT.getType(), 1, null);
        log.info("exportEnrollsDataResult result:{}", exportEnrollsDataResult);
        Assert.assertNotEquals(exportEnrollsDataResult, null);
    }

    @Test
    public void bindObjectStatistics() {
        Map<String, Map<Integer, Integer>> result = customizeFormDataManager.bindObjectStatistics(Lists.newArrayList("a6d63a4b0abc44e3a21821929a05631c","ffe1f70f6dba463e82a121dbbcbbacd7","4f2e757469b54544b31b52c49cd24980","3201d319a7934f68a7089160b5f1c1cf"));
        Assert.assertNotEquals(result, null);
    }

    @Test
    public void createCustomizeFormDataQRCode() {
        CustomizeFormQrCodeContainer customizeFormQrCodeContainer = customizeFormDataManager.createCustomizeFormDataQRCode("1b1d6a851cca47be97f9f0177679f74b", "5da6ba6964b6b5000186486a","2");
        Assert.assertNotEquals(customizeFormQrCodeContainer, null);
    }


    @Test
    public void saveEnrollDataToCrm() {
        customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataDAO.getCustomizeFormDataById("d75ebeb0585e455fb153e175b21aa2f1"), customizeFormDataUserDAO.getCustomizeFormDataUserById("5afc8ab60dfc4d6ebaf80ae1a7e17f3e"));
    }

    @Test
    public void saveFromDataByEnrollids() {
        customizeFormDataManager.saveFromDataByEnrollids(Lists.newArrayList("5f02724c5aef40719f966d315a3f718a","8067493c72864cc797dadc0b88f09fd0","0a33cbc2c23f4fe5b207484d2047a162","e9e3e8104e48479d99e9c648484fcbf2","7f06e201fa274b97aa02e0232eb34065","07b89a1e12874ea8be3aa2b4cf1669ba","e2e8632de22440cb9ab16d39c547128f","21431b5c20a8438888e3de52e613e573","fe832923d3414c3a9c491d7c29aeb868","16d86d02b143490582f6a105881e2b9d"));
    }

    @Test
    public void getSystemPromotionChannelType() {
        CustomizeFormDataUserEntity entity = new CustomizeFormDataUserEntity();
        entity.setId("7303be2535714358a2175a61e30c315a");
        entity.setUid("");
        entity.setFormId("038215a6583142ea8e73cbf4fdda026e");
        entity.setObjectId("038215a6583142ea8e73cbf4fdda026e");
        entity.setObjectType(16);
        CustomizeFormDataEnroll enroll = new CustomizeFormDataEnroll();
        enroll.setName("zzl");
        enroll.setPhone("13530259090");
        entity.setSubmitContent(enroll);
        entity.setMarketingEventId("60ffa8961cbe610001f310f4");
        entity.setSourceType(4);
        entity.setMarketingActivityId("60ffa9f365cf130001cd6e2e");
        String channelType = customizeFormDataManager.getSystemPromotionChannelType(entity);
        System.out.println("channelType = " + channelType);
    }

    @Test
    public void testUpdateDao() {
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessageWithIdList(Lists.newArrayList("ebc5f1629535476a8995d78aba16739d"
                    ,"51c6475b22af43a59c0e9a090f719e3b"), SaveCrmStatusEnum.ERROR.getValue(), "存入失败", null);
    }

    @Test
    public void fillDataOwnOrganizationTest() {
        Map<String, Object> data = new HashMap<>();
        data.put(CrmV2LeadFieldEnum.MarketingEventID.getNewFieldName(), "643fa0cc44ede6000104cbbe");
//        customizeFormDataManager.fillDataOwnOrganization("88146", data);
    }

    @Test
    public void getCustomizeFormDataUserTest() {
        String json = "{\"ea\":\"88146\",\"objectId\":\"a576d53c2b814974b43cd79612fbd85b\",\"objectType\":16,\"type\":3,\"usage\":1,\"pageNum\":1,\"pageSize\":5}";
        FormDataUserArgContainer formDataUserArgContainer = JSONObject.parseObject(json, FormDataUserArgContainer.class);

        Page page = new Page(1, 5, true);
        PageResult<QueryFormUserDataResult> result = customizeFormDataManager.getCustomizeFormDataUser(formDataUserArgContainer, page);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    public void countClueByEaWithMarketingActivityV2Test() {
        String ea = "83668";
        Date now = new Date();
        Date beginTime = DateUtil.plusDay(now, -300);
//        int count = customizeFormDataUserDAO.countClueByEaWithMarketingActivity(ea, beginTime, now, true);
//        log.info("coount = {}", count);
        CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
        customizeFormDataEnroll.setUtmMedium("信息流");
        customizeFormDataEnroll.setUtmSource("知乎");
        customizeFormDataManager.handleLeadsSpecialField("83668", customizeFormDataEnroll, new HashMap<>());

    }

    @Test
    public void saveConferenceParticipantsToCrmTest(){
        String ea = "89196";
        List<String> campaignIds = Lists.newArrayList("74418c1945ce4dcb925667f22af6cde9");
        customizeFormDataManager.saveConferenceParticipantsToCrm(ea, campaignIds);
    }

    @Test
    public void testDeleteEnrollData(){
        DeleteEnrollDataArg arg = new DeleteEnrollDataArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setEnrollUserIds(Lists.newArrayList("f4421560dfbe400dbdb9483af9271525"));
        Result<Void> result = customizeFormDataService.deleteEnrollData(arg);
        System.out.println("yes:" + result);
    }
}
