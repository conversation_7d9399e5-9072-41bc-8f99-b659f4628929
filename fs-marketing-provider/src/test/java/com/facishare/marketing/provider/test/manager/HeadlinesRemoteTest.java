/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.advertiser.BaiduAuthCallBackArg;
import com.facishare.marketing.api.service.advertiser.headlines.AuthCallbackService;
import com.facishare.marketing.api.service.baidu.AdAccountService;
import com.facishare.marketing.api.vo.advertiser.headlines.GetAccessTokenVo;
import com.facishare.marketing.api.vo.baidu.RefreshDataVO;
import com.facishare.marketing.common.enums.advertiser.headlines.CustomReportArg;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.advertiser.adAccount.AdvertiserPublicInfoResult;
import com.facishare.marketing.provider.advertiser.adAccount.GetMajordomoAdvertiser;
import com.facishare.marketing.provider.advertiser.adAccount.GetOAuthAdvertiser;
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult;
import com.facishare.marketing.provider.advertiser.headlines.ad.HeadlinesUserInfoResultData;
import com.facishare.marketing.provider.advertiser.headlines.report.CustomReportResult;
import com.facishare.marketing.provider.baidu.GetAccountInfoResultData;
import com.facishare.marketing.provider.manager.baidu.CampaignApiManager;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.AccountApiManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.remote.paas.crm.CrmJsonUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by wangzhenyi on 2021/8/13 5:22 下午
 */
@Slf4j
public class HeadlinesRemoteTest extends BaseTest {

    private final String accessToken = "39056e7f00d475b3c5f993ee95c23880ea22bfbf";
    private final String advertiserId = "************";
    private final String refreshToken = "e35f9b82874b9c6cd6fe453f141f4d47ade3dcf6";
    // auth_code使用一次后作废 需要重新授权
    private final String authCode = "7da6d3250af786c74229de6eea6d85c70a23afd5";
    private Long APP_ID = 1707692945627160L;
    private String SECRET = "9ae0dde7ce5f310a337cb8c80496a7fe87a1af8a";
    private String AUTH_CODE = "auth_code";
    private String GRANT_TYPE_REFRESH = "refresh_token";
    private Long WEILAIROBERT_ACCOUNT_ID = 1664472932493326L;

    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private CampaignApiManager campaignApiManager;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private AdTokenManager adTokenManager;
    @Autowired
    private AuthCallbackService authCallbackService;
    @Autowired
    private BaiduAccountDAO accountDAO;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;
    @Autowired
    private AdAccountService adAccountService;
    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;


    /**
     * 获取accountId和username
     */
    @Test
    public void getUserInfoTest() {
        HeadlinesRequestResult<HeadlinesUserInfoResultData> headlinesUserInfo = accountApiManager.getHeadlinesUserInfo("fc148d87c40775cc4b393539b3d5dc7bdbd2ebe4");
        System.out.println(headlinesUserInfo);
    }


    @Test
    public void authCallBackTest() {
        GetAccessTokenVo vo = new GetAccessTokenVo();
        vo.setAuthCode(authCode);
        vo.setEa("88146");
        vo.setSource(AdSourceEnum.getSourceByValue("3"));
        Result<String> authInfo = authCallbackService.getAuthInfo(vo);
        log.info("res:{}", authInfo);
    }

    @Test
    public void refreshToken() {
      //  String accessToken = adTokenManager.refreshAccessToken(refreshToken, advertiserId);
        log.info("new accessToken:{}", accessToken);
    }


    /**
     * 刷新token使用
     */
    @Test
    public void adTokenManagerTest() {
//        AdAccountEntity adAccountEntity = new AdAccountEntity();
//        adAccountEntity.setAccountId(Long.valueOf(advertiserId));
//        adAccountEntity.setRefreshToken(refreshToken);
//        redisManager.setValueWithExpiredTime(RedisManager.HEADLINES_ACCESS_TOKEN, accessToken, RedisManager.HEADLINES_ACCESS_TOKEN_EXPIRED_TIME);
//        String newToken = redisManager.get(RedisManager.HEADLINES_ACCESS_TOKEN);
//        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
//        log.info("accessToken:{}" + newToken);
    }


    /**
     * 绑定账户
     */
    @Test
    public void bindAccountTest() {
//        AdAccountEntity adAccountEntity = accountDAO.queryEnableAccountById("");
//        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
        HeadlinesRequestResult<GetAccountInfoResultData> headlinesAccountInfo = accountApiManager.getHeadlinesAccountInfo(************L, "240ba413e4c12bab313ae139221fb1287b18e5c5");
        log.info("bind result:{}", headlinesAccountInfo);

    }



    @Test
    public void getCampaignList() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setAccountId(Long.valueOf(advertiserId));
        adAccountEntity.setSource(AdSourceEnum.getSourceByValue("3"));
        adAccountEntity.setToken(accessToken);
        adAccountEntity.setEa("74164");
        boolean result = campaignDataManager.refreshCampaignInfo(adAccountEntity);
        log.info(String.valueOf(result));
    }

    /**
     * 刷新头条账户信息
     */
    @Test
    public void refreshHeadlinesAccountInfo() {
        AdAccountEntity accountEntity = accountDAO.queryEnableAccountById("915b8542d577434b8936268f9a56687f");
        boolean result = campaignDataManager.refreshAccountInfo(accountEntity);
        log.info("accountInfo refresh result:{}", result);
    }

    /**
     * 刷新广告组信息
     */
    @Test
    public void refreshCampaignInfoTest() {
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("5e45f266ceec4841b10b08cb8e1f767b");
        headlinesAdMarketingManager.refreshCampaignInfo(adAccountEntity);
    }

    /**
     * 刷新广告计划
     */
    @Test
    public void refreshAdTest() {
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("5e45f266ceec4841b10b08cb8e1f767b");
        headlinesAdMarketingManager.refreshHeadlinesAd(adAccountEntity, 300);
    }

    /**
     * 刷新广告计划数据
     */
    @Test
    public void refreshAdDataTest() {
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("5e45f266ceec4841b10b08cb8e1f767b");
        headlinesAdMarketingManager.refreshHeadlinesAdData(adAccountEntity, 30);
    }

    @Test
    public void refreshCampaignDataTest() {
        AdAccountEntity adAccountEntity = accountDAO.queryAccountById("5e45f266ceec4841b10b08cb8e1f767b");
        headlinesAdMarketingManager.refreshCampaignData(adAccountEntity, 30);
    }


    /**
     * 更新市场活动线索数
     */
    @Test
    public void refreshMarketingLeads() {
        AdAccountEntity accountEntity = accountDAO.queryEnableAccountById("");
        boolean result = campaignDataManager.refreshMarketingEventLeads(accountEntity, 3, null);
        if (result) {
            log.info("refresh leads success!, result:{}", result);
        } else {
            log.warn("refresh leads fail!, result:{}", result);
        }
    }

    @Test
    public void refreshTest() {
        campaignDataManager.refreshByEaAndAdAccountId("74164", "", "巨量引擎", 7);
    }

    /**
     * 同步广告组到crm
     */
    @Test
    public void syncCampaign2Crm() {
//        refreshDataManager.syncHeadlinesCampaignToMarketingEventObj("74164", AdSourceEnum.getSourceByValue("3"));
    }

    /**
     * 查询crm市场活动 by广告组
     */
    @Test
    public void queryCrmMarketingEventByCampaign() {
        List<AdCampaignEntity> campaignData = Lists.newArrayList();
        AdCampaignEntity campaignEntity = new BaiduCampaignEntity();
        campaignEntity.setCampaignName("竞品关键词");
        campaignEntity.setEa("74164");
        campaignEntity.setCampaignId(97773897L);
        campaignData.add(campaignEntity);
        Page<ObjectData> objectDataPage = refreshDataManager.queryCrmMarketingEventByNames(campaignData, AdSourceEnum.getSourceByValue("1"));
        List<ObjectData> dataList = objectDataPage.getDataList();
        log.info(String.valueOf(dataList));
    }

    @Test
    public void syncAd2Crm() {
//        refreshDataManager.syncHeadlinesAdToSubMarketingEventObj("74164", AdSourceEnum.getSourceByValue("3"));
    }
    @Test
    public void refreshKeyword() {
        AdAccountEntity accountEntity = accountDAO.queryEnableAccountById("");
//        refreshDataManager.refreshHeadlinesKeyWord(accountEntity, AdSourceEnum.getSourceByValue("3"));
    }

    @Test
    public void syncKeyword2Crm() {
//        refreshDataManager.syncAdKeywordToMarketingKeywordObj("74164", AdSourceEnum.getSourceByValue("3"));
    }

    @Test
    public void queryKeywordFromCrm() {
        Page<ObjectData> objectDataPage = refreshDataManager.queryCrmMarketingKeyword("74164", Lists.newArrayList("销售crm软件"));
        log.info("result:{}", CrmJsonUtil.toJson(objectDataPage.getDataList()));
    }

    @Test
    public void syncKeywordPlan2Crm() {
//        refreshDataManager.syncKeywordServingPlan("74164", AdSourceEnum.getSourceByValue("3"));
    }

    @Test
    public void synvClue2Crm() {
//        refreshDataManager.syncClueDataToClueObj("74164", AdSourceEnum.getSourceByValue("3"));
    }

    @Test
    public void syncHeadlinesData() {
//        refreshDataManager.refreshHeadlinesData("74164", "", "巨量引擎");
    }

    @Test
    public void getAdData() {
//        AdAccountEntity accountEntity = accountDAO.queryAccountByEaAndSource("74164", AdSourceEnum.getSourceByValue("3"));
//        HeadlinesRequestResult<HeadlinesAdDataResult> requestResult = campaignApiManager.getHeadlinesAdData(************L, adTokenManager.getAccessToken(accountEntity));
//        log.info("result:{}",requestResult);
    }

    @Test
    public void getOAuthAdvertiserListTest() {
        HeadlinesRequestResult<GetOAuthAdvertiser> adertisers = accountApiManager.getOAuthAdvertisers("177057d859e08d838847d7ac428842f7fc4ae987", APP_ID, SECRET);
        log.info("result:{}", adertisers);
    }

    @Test
    public void getMajordomoAdvertiserListTest() {
        HeadlinesRequestResult<GetMajordomoAdvertiser> majordomoAdvertiserResult = accountApiManager.getMajordomoAdvertiserList(1704799507132419L, "b3dfd6bebebb13515df45c1af78afde9c568bcf5");
        log.info("result:{}", majordomoAdvertiserResult);
    }

    @Test
    public void getHeadlinesAccountInfoTest() {
        HeadlinesRequestResult<GetAccountInfoResultData> result = accountApiManager.getHeadlinesAccountInfo(1719202450055175L, "c8651e3af145bb6fe49d1be0797779a0f7ea595f");
        log.info("result:{}", result);
    }

    @Test
    public void refresDataTest() {
        RefreshDataVO vo = new RefreshDataVO();
        vo.setEa("74164");
        vo.setAdAccountId("d66c34155efd4072bd1e59286bef4d8e");
        vo.setSource("1");
        vo.setFsUserId(1123);
        adAccountService.refreshData(vo);
    }


    @Test
    public void headlinesCampaignTest() {
        HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryCampaignByCampaignId("74164", 1702629452743707L);
        headlinesCampaignEntity.setBudget(headlinesCampaignEntity.getBudget());
        headlinesCampaignEntity.setCampaignName(headlinesCampaignEntity.getCampaignName());
        headlinesCampaignEntity.setDeliveryMode(0);
        headlinesCampaignEntity.setBudgetMode(0);
        headlinesCampaignEntity.setLandingType(0);
        headlinesCampaignEntity.setStatus(0);
        headlinesCampaignEntity.setMarketingPurpose(0);
        headlinesCampaignEntity.setRefreshTime(new Date());
        headlinesCampaignEntity.setUpdateTime(new Date());
        headlinesCampaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        if (headlinesCampaignEntity.getAdAccountId() == null) {
            headlinesCampaignEntity.setAdAccountId("3d01871e214646c99fdf01b6b5c4289a");
        }
        headlinesCampaignDAO.updateCampaignForRefresh(headlinesCampaignEntity);
    }

    @Test
    public void getHeadlinesPromotionCustomReportTest() {
        // 广告维度
        List<String> dimensions = Lists.newArrayList("stat_time_day", "cdp_promotion_id");
        List<String> metrics = Lists.newArrayList("stat_cost", "show_cnt","click_cnt", "cpc_platform");
//        Map<String, String> order = Maps.newHashMap();
//        order.put("field","stat_cost");
//        order.put("type","DESC");
        //List<Map<String, String>> orderBy = Lists.newArrayList(order);
        String startTime = "2023-07-01";
        String endTime = "2023-07-05";
        int pageNum = 1;
        int pageSize = 10;
        CustomReportArg customReportArg = new CustomReportArg();
        customReportArg.setDimensions(dimensions);
        customReportArg.setMetrics(metrics);
        CustomReportArg.OrderBy orderBy = new CustomReportArg.OrderBy();
        orderBy.setField("stat_cost");
        orderBy.setType("DESC");
        customReportArg.setStartTime(startTime);
        customReportArg.setEndTime(endTime);
        customReportArg.setPageNum(pageNum);
        customReportArg.setPageSize(pageSize);
        customReportArg.setAdvertiserId(1754608219196424L);
        customReportArg.setOrderBy(Lists.newArrayList(orderBy));
        List<CustomReportArg.Filter> filters = Lists.newArrayList();
        CustomReportArg.Filter filter = new CustomReportArg.Filter();
        filter.setField("cdp_promotion_id");
        filter.setOperator(1);
        filter.setType(2);
        filter.setValues(Lists.newArrayList("7248836741086593062"));
        filters.add(filter);
        customReportArg.setFilters(filters);
        HeadlinesRequestResult<CustomReportResult> result = campaignApiManager.getHeadlinesCustomReport("8c2100d68f2d0a75af6f72a8d4ebaaf15726dd5e", customReportArg);
        log.info("结果1： {}", JSON.toJSONString(result));
        dimensions = Lists.newArrayList("stat_time_day", "cdp_promotion_id", "cdp_project_id");
        customReportArg.setDimensions(dimensions);
        HeadlinesRequestResult<CustomReportResult> result2 = campaignApiManager.getHeadlinesCustomReport("8c2100d68f2d0a75af6f72a8d4ebaaf15726dd5e", customReportArg);
        log.info("结果2： {}", JSON.toJSONString(result2));
    }

    @Test
    public void getHeadlinesProjectCustomReportTest() {
        // 项目维度
        List<String> dimensions = Lists.newArrayList("stat_time_day", "cdp_project_id");
        List<String> metrics = Lists.newArrayList("stat_cost", "show_cnt","click_cnt", "cpc_platform");
//        Map<String, String> order = Maps.newHashMap();
//        order.put("field","stat_cost");
//        order.put("type","DESC");
        //List<Map<String, String>> orderBy = Lists.newArrayList(order);
        String startTime = "2023-07-01";
        String endTime = "2023-08-05";
        int pageNum = 1;
        int pageSize = 300;
        CustomReportArg customReportArg = new CustomReportArg();
        customReportArg.setDimensions(dimensions);
        customReportArg.setMetrics(metrics);
        CustomReportArg.OrderBy orderBy = new CustomReportArg.OrderBy();
        orderBy.setField("stat_cost");
        orderBy.setType("DESC");
        customReportArg.setStartTime(startTime);
        customReportArg.setEndTime(endTime);
        customReportArg.setPageNum(pageNum);
        customReportArg.setPageSize(pageSize);
        customReportArg.setAdvertiserId(1754608219196424L);
        customReportArg.setOrderBy(Lists.newArrayList(orderBy));
        List<CustomReportArg.Filter> filters = Lists.newArrayList();
        CustomReportArg.Filter filter = new CustomReportArg.Filter();
        filter.setField("cdp_project_id");
        filter.setOperator(1);
        filter.setType(2);
        filter.setValues(Lists.newArrayList("7241151623783038979"));
        filters.add(filter);
        customReportArg.setFilters(filters);
        campaignApiManager.getHeadlinesCustomReport("e1af481ae43dfbc04f8299d24af45b410fecf13b", customReportArg);
    }

    @Test
    public void getAdvertiserPublicInfo() {
        List<AdvertiserPublicInfoResult> result = accountApiManager.getAdvertiserPublicInfo(Lists.newArrayList(1775542534611971L), "0a6b3c2533bc9a306d1bcc42870a7821bcaafdcd");
        log.info("result:{}", result);
    }

    @Test
    public void refreshAccountInfo() {
        headlinesAdMarketingManager.refreshAccountInfo(accountDAO.queryAccountById("216c8ffeaa6545228c4d075b8e072be5"));
    }

    @Test
    public void refreshHeadlinesProjectTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setEa("83668");
        adAccountEntity.setAccountId(1768674724529166L);
        adAccountEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        headlinesAdMarketingManager.refreshHeadlinesProject(adAccountEntity);
    }
}
