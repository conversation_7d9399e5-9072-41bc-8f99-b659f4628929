package com.facishare.marketing.provider.entity.cta;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CtaRelationEntity implements Serializable {
    //主键ID
    private String id;
    private String ea;
    //CtaId
    private String ctaId;
    private Integer objectType;
    //ObjectId
    private String objectId;
    //创建人
    private Integer createBy;
    // 创建时间瓬
    private Date createTime;
    //最后修改人
    private Integer updateBy;
    // 更新时间
    private Date updateTime;
}
