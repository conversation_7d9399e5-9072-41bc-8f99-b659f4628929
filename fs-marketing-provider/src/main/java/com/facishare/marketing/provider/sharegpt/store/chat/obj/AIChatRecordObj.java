package com.facishare.marketing.provider.sharegpt.store.chat.obj;

import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.result.ai.AgentResponse;
import com.facishare.marketing.api.vo.ai.ChatCompleteVO;
import com.facishare.marketing.common.util.GsonUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class AIChatRecordObj extends ObjectData {

    public static final String OBJECT_API_NAME = "AIChatRecordObj";

    public AIChatRecordObj() {
    }

    public static AIChatRecordObj wrap(ObjectData objectData) {
        AIChatRecordObj data = new AIChatRecordObj();
        if (objectData != null) {
            data.putAll(objectData);
        }
        return data;
    }

    public static List<AIChatRecordObj> wrap(List<ObjectData> objectDatas) {
        if (CollectionUtils.isEmpty(objectDatas)) return Lists.newArrayList();
        return objectDatas.stream().map(AIChatRecordObj::wrap).collect(Collectors.toList());
    }

    public String getSessionId() {
        return this.getString("session_id");
    }

    public AIChatRecordObj setSessionId(String sessionId) {
        this.put("session_id", sessionId);
        return this;
    }

    public String getTips() {
        return this.getString("tips");
    }

    public AIChatRecordObj setTips(String tips) {
        this.put("tips", tips);
        return this;
    }

    public String getContent() {
        return this.getString("content");
    }

    public AIChatRecordObj setContent(String content) {
        this.put("content", content);
        return this;
    }

    public AgentResponse.AgentAction getAction() {
        String actionJson = this.getString("action");
        if (StringUtils.isNotBlank(actionJson)) {
            return GsonUtil.fromJson(actionJson, AgentResponse.AgentAction.class);
        }
        return null;
    }

    public AIChatRecordObj setAction(AgentResponse.AgentAction action) {
        if (action != null) {
            this.put("action", GsonUtil.toJson(action));
        }
        return this;
    }

    public List<AgentResponse.AgentAction> getActions() {
        String actionsJson = this.getString("actions");
        if (StringUtils.isNotBlank(actionsJson)) {
            return GsonUtil.fromJson(actionsJson, ArrayList.class);
        }
        return null;
    }

    public AIChatRecordObj setActions(List<AgentResponse.AgentAction> actions) {
        if (actions != null) {
            this.put("actions", GsonUtil.toJson(actions));
        }
        return this;
    }

    public Object getData() {
        String dataJson = this.getString("data");
        if (StringUtils.isNotBlank(dataJson)) {
            return GsonUtil.fromJson(dataJson, Object.class);
        }
        return null;
    }

    public AIChatRecordObj setData(Object data) {
        if (data != null) {
            this.put("data", GsonUtil.toJson(data));
        }
        return this;
    }

    public ChatCompleteVO.Property getProperty() {
        String property = this.getString("property");
        if (StringUtils.isNotBlank(property)) {
            try {
                return GsonUtil.fromJson(property, ChatCompleteVO.Property.class);
            } catch (Exception e) {
            }
        }
        return new ChatCompleteVO.Property();
    }

    public AIChatRecordObj setProperty(ChatCompleteVO.Property property) {
        this.put("property", GsonUtil.toJson(property));
        return this;
    }

    public int getActionStatus() {
        return this.getInt("action_status") == null ? 0 : this.getInt("action_status");
    }

    public AIChatRecordObj setActionStatus(int actionStatus) {
        this.put("action_status", String.valueOf(actionStatus));
        return this;
    }

    public String getInstanceId() {
        return this.getString("instance_id");
    }

    public AIChatRecordObj setInstanceId(String instanceId) {
        this.put("instance_id", instanceId);
        return this;
    }

    public String getContentType() {
        return this.getString("content_type");
    }

    public AIChatRecordObj setContentType(String contentType) {
        this.put("content_type", contentType);
        return this;
    }

    public String getCardType() {
        return this.getString("card_type");
    }

    public AIChatRecordObj setCardType(String cardType) {
        this.put("card_type", cardType);
        return this;
    }

    // 0-nothing, 1-踩, 2-赞, 3-复制
    public int getLikeStatus() {
        return this.getInt("like_status") == null ? 0 : this.getInt("like_status");
    }

    public AIChatRecordObj setLikeStatus(int likeStatus) {
        this.put("like_status", String.valueOf(likeStatus));
        return this;
    }

    public Integer getSenderId() {
        return this.getInt("sender_id");
    }

    public AIChatRecordObj setSenderId(Integer senderId) {
        if (senderId != null) {
            this.put("sender_id", String.valueOf(senderId));
        }
        return this;
    }

    public String getAgentName() {
        return this.getString("agent_name");
    }

    public AIChatRecordObj setAgentName(String agentName) {
        this.put("agent_name", agentName);
        return this;
    }

    public String getPromptRecordId() {
        return this.getString("prompt_record_id");
    }

    public AIChatRecordObj setPromptRecordId(String promptRecordId) {
        this.put("prompt_record_id", promptRecordId);
        return this;
    }

    public AgentResponse.AgentProcess getProcess() {
        String process = this.getString("process");
        if (StringUtils.isNotBlank(process)) {
            try {
                return GsonUtil.fromJson(process, AgentResponse.AgentProcess.class);
            } catch (Exception e) {
            }
        }
        return null;
    }

    public AIChatRecordObj setProcess(AgentResponse.AgentProcess process) {
        if (process != null) {
            this.put("process", GsonUtil.toJson(process));
        }
        return this;
    }

    public Object getDisplayData() {
        String display_data = this.getString("display_data");
        if (StringUtils.isNotBlank(display_data)) {
            if (isJson(display_data)) {
                return GsonUtil.fromJson(display_data, Object.class);
            } else {
                return display_data;
            }
        }
        return null;
    }

    public AIChatRecordObj setDisplayData(Object displayData) {
        if (displayData != null) {
            this.put("display_data", GsonUtil.toJson(displayData));
        }
        return this;
    }

    public String getActionType() {
        return this.getString("action_type");
    }

    public AIChatRecordObj setActionType(String actionType) {
        this.put("action_type", actionType);
        return this;
    }

    public Object getOriginalData() {
        String original_data = this.getString("original_data");
        if (StringUtils.isNotBlank(original_data)) {
            if (isJson(original_data)) {
                return GsonUtil.fromJson(original_data, HashMap.class);
            } else {
                return original_data;
            }
        }
        return null;
    }

    public AIChatRecordObj setOriginalData(Object originalData) {
        if (originalData != null) {
            this.put("original_data", GsonUtil.toJson(originalData));
        }
        return this;
    }

    private boolean isJson(String string) {
        try {
            GsonUtil.fromJson(string, HashMap.class);
            return true;
        } catch (JsonSyntaxException ex) {
            return false;
        }
    }
}
