package com.facishare.marketing.provider.entity.marketingAssistant;

import com.facishare.marketing.common.typehandlers.value.SendWxTemplateMsgArg;
import com.facishare.marketing.common.typehandlers.value.YxzsSendUnionMessageExtendArg;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class YxzsAllSpreadWxTemplateNoticeSettingEntity  implements Serializable {
    private String id;
    private String ea;

    //开启公众号通知
    private Integer openWxNotice; //0关闭，1打开
    //公众号appid和通知模板id
    private String wxAppId;

    private SendWxTemplateMsgArg wxTemplateMsg;

    private Date createTime;
    private Date updateTime;
}