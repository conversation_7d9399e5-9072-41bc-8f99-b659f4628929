package com.facishare.marketing.provider.dao.sms.mw;

import com.facishare.marketing.provider.dto.SmsSendDetailDTO;
import com.facishare.marketing.provider.entity.MwCodeEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignDataEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSendDetailEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendEntity;
import com.facishare.marketing.provider.entity.sms.mw.SmsFeeStatisticEntity;
import com.facishare.marketing.provider.entity.sms.mw.SmsSendInfoEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by ranluch on 2019/2/19.
 */
public interface MwSmsSendDao {
    /**
     * 多表关联的查询。
     * mw_sms_send s  发送表
     * mw_sms_template st  模板表
     * marketing_activity_external_config mc  营销活动配置表
     * mw_sms_send_marketing_event_relation smr  发送表与市场活动关联表
     * coalesce(mc.marketing_event_id, smr.marketing_event_id) as  marketing_event_id 首先查询营销活动配置表，如果marketing_event_id为空则查询mw_sms_send_marketing_event_relation表
     * @return
     */
    @Select("<script>"
        +"select s.ea , s.id, s.create_time, s.template_id, s.update_time, s.signature_id , "
        + "s.creator_name, s.actual_sender_count, \n"
        + "s.to_sender_count, s.status , \n"
        + "s.total_fee, s.type , \n"
        + "s.user_groups, \n"
        + "s.schedule_time, \n"
        + "st.content, st.name, st.status as template_status, s.result_code, "
        + "coalesce(mc.marketing_event_id, smr.marketing_event_id) as  marketing_event_id, \n"
        + "st.param_detail, st.scene_type, \n"
        + "smr.external_config \n"
        + "from mw_sms_send s \n"
        + "join mw_sms_template st on s.template_id = st.id AND st.id != '40210d2fbb1640e1882aa698c2708f0c'\n"
        + "left join marketing_activity_external_config mc on mc.associate_id = s.id \n"
        + "left join mw_sms_send_marketing_event_relation smr on smr.send_id = s.id \n"
        + " WHERE s.ea = #{ea} "
        + "<if test=\"startTime != null\">\n  and s.create_time &gt;= #{startTime} </if>   "
        + " <if test=\"endTime != null\">\n and s.create_time &lt;= #{endTime} </if>\n"
        + "  <if test=\"creator != null\"> and  s.creator_name = #{creator}  </if>  "
        + "  <if test=\"channelType != null\"> and  s.channel_type = #{channelType}  </if>  "
        + "<if test=\"searchText != null\">  and st.\"content\" like '%'||#{searchText}||'%' </if>"
        + " order by s.create_time desc"
        + "</script>")
    List<SmsSendInfoEntity> querySmsSendInfoList(@Param("ea") String ea, @Param("page") Page page, @Param("searchText") String searchText, @Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("channelType") Integer channelType);

    @Select("<script>"
            +"select s.ea , s.id, s.create_time, s.template_id, s.update_time, s.signature_id , "
            + "s.creator_name, s.actual_sender_count, \n"
            + "s.to_sender_count, s.status , \n"
            + "s.total_fee, s.type , \n"
            + "s.user_groups, \n"
            + "s.schedule_time, \n"
            + "st.content, st.name, st.status as template_status, s.result_code, "
            + "coalesce(mc.marketing_event_id, smr.marketing_event_id) as  marketing_event_id, \n"
            + "st.param_detail, st.scene_type, \n"
            + "smr.external_config \n"
            + "from mw_sms_send s \n"
            + "left join mw_sms_template st on s.template_id = st.id AND st.id != '40210d2fbb1640e1882aa698c2708f0c'\n"
            + "left join marketing_activity_external_config mc on mc.associate_id = s.id \n"
            + "left join mw_sms_send_marketing_event_relation smr on smr.send_id = s.id \n"
            + " WHERE s.ea = #{ea} AND mc.marketing_activity_id IN"
            +   "<foreach collection = 'marketingActivityIds' item = 'marketingActivityId' open = '(' separator = ',' close = ')'>"
            +       "#{marketingActivityId}"
            +   "</foreach>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<SmsSendInfoEntity> querySmsSendByMarketingActivityIds(@Param("ea") String ea, @Param("marketingActivityIds")List<String> marketingActivityIds);

    @Select("select * from mw_sms_send where id = #{id}")
    MwSmsSendEntity getSMSSendById( @Param("id") String id);

    @Select("<script>"
        + "select msd.*, case when msd.err_code is not null then mc.desc else msd.err_desc end as errCodeDesc from mw_send_detail msd left join mw_code mc on mc.code = msd.err_code "
        + "where msd.send_id = #{sendId} "
        + "<if test=\"status != null\"> "
        + " and msd.status = #{status} "
        + " </if> "
        + "</script>")
    List<MwSendDetailEntity> getSendDetailBySmsSendIdAndStatus2Page(@Param("sendId") String sendId, @Param("status") Integer status, @Param("page") Page page);

    @Select("<script>"
        + "select * from mw_send_detail where send_id = #{sendId}"
        + "<if test=\"status != null\"> "
        + " and status = #{status} "
        + " </if> "
        + "</script>")
    List<MwSendDetailEntity> querySendDetailBySendId(@Param("sendId") String sendId, @Param("status") Integer status);

    @Select("<script>"
            + "select count(*) from mw_send_detail where send_id = #{sendId}"
            + "<if test=\"status != null\"> "
            + " and status = #{status} "
            + " </if> "
            + "</script>")
    int querySendDetailCountBySendId(@Param("sendId") String sendId, @Param("status") Integer status);

    @Select("<script>"
            + "select * from mw_send_detail where send_id = #{sendId}"
            + "<if test=\"status != null\"> "
            + " and status = #{status} "
            + " </if> "
            + "</script>")
    List<MwSendDetailEntity> pageQuerySendDetailBySendId(@Param("sendId") String sendId, @Param("status") Integer status, @Param("page") Page page);

    @Select("<script>"
        + "select count(*) from mw_send_detail where send_id IN "
        + "<foreach collection = 'sendIds' item = 'sendId' open = '(' separator = ',' close = ')'>"
        + " #{sendId}"
        + "</foreach> "
        + "<if test=\"status != null\"> "
        + " and status = #{status} "
        + " </if> "
        + "</script>")
    Integer countSendDetailBySendIds(@Param("sendIds") Collection<String> sendIds, @Param("status") Integer status);

    @Delete("delete from mw_sms_send where id = #{id}")
    boolean deleteSMSSendById(@Param("id") String id);

    @Delete("delete from mw_send_detail where send_id = #{smsSendId}")
    boolean deleteSMSSendDetailById(@Param("smsSendId") String smsSendId);

    @Insert("insert into mw_sms_send (\"id\", \"ea\", \"signature_id\", \"template_id\", \"status\", \"actual_sender_count\", \"to_sender_count\", \"total_fee\", \"type\", \"schedule_time\", \"host_name\", "
        + " \"creator_user_id\", \"creator_name\", \"user_groups\", \"channel_type\", \"business_type\", \"receiver\", \"send_node\", \"node_type\", \"object_id\" ) values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.signatureId},\n"
        + " #{obj.templateId},\n"
        + " #{obj.status},\n"
        + " #{obj.actualSenderCount},\n"
        + " #{obj.toSenderCount},\n"
        + " #{obj.totalFee},\n"
        + " #{obj.type},\n"
        + " #{obj.scheduleTime},\n"
        + " #{obj.hostName},\n"
        + " #{obj.creatorUserId},\n"
        + " #{obj.creatorName},\n"
        + " #{obj.userGroups},\n"
        + " #{obj.channelType},\n "
        + " #{obj.businessType},\n "
        + " #{obj.receiver},\n "
        +" #{obj.sendNode},\n "
        +" #{obj.nodeType},\n "
        +" #{obj.objectId})"
    )
    boolean insertSendEntity(@Param("obj") MwSmsSendEntity smsSendEntity);

    @Insert("<script>" + "INSERT INTO mw_send_detail (\"id\",\"send_id\",\"phone\",\"content\",\"fee\",\"status\",\"exdata\",\"params\", \"ea\", \"channel_type\", \"template_id\", \"variables\") VALUES\n"
        + "    <foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\">\n"
        + "      (#{entity.id}, #{entity.sendId}, #{entity.phone}, #{entity.content}, #{entity.fee}, #{entity.status}, #{entity.exdata}, #{entity.params}, #{entity.ea}, #{entity.channelType}, #{entity.templateId}, #{entity.variables})\n" + "    </foreach>" + "</script>")
    boolean batchInsertDetail(@Param("entities") Collection<MwSendDetailEntity> entities);

    @Update("update mw_sms_send set "
        + "\"template_id\" = #{obj.templateId}, "
        + "\"status\" = #{obj.status} , "
        + "\"actual_sender_count\" = #{obj.actualSenderCount}, "
        + "\"to_sender_count\" = #{obj.toSenderCount}, "
        + "\"total_fee\" = #{obj.totalFee}, "
        + "\"type\" = #{obj.type}, "
        + "\"schedule_time\" = #{obj.scheduleTime}, "
        + "\"user_groups\" = #{obj.userGroups}, "
        + "\"update_time\" = now() "
        + "  where \"id\" = #{obj.id}")
    boolean updateSendEntity(@Param("obj") MwSmsSendEntity smsSendEntity);

    @Update("update mw_sms_send set "
        + "\"status\" = #{obj.status} , "
        + "\"result_code\" = #{obj.resultCode}, "
        + "\"msgid\" = #{obj.msgid} , "
        + "\"custid\" = #{obj.custid}, "
        + "\"actual_sender_count\" = actual_sender_count - #{obj.senderReduce}, "
        + "\"total_fee\" = total_fee - #{obj.rebackFee}, "
        + "\"update_time\" = now() "
        + "  where \"id\" = #{obj.id}")
    boolean updateSendResult(@Param("obj") MwSmsSendEntity smsSendEntity);

    @Update("update mw_sms_send set "
            + "\"result_code\" = #{obj.resultCode}, "
            + "\"msgid\" = #{obj.msgid} , "
            + "\"custid\" = #{obj.custid}, "
            + "\"actual_sender_count\" = actual_sender_count - #{obj.senderReduce}, "
            + "\"total_fee\" = total_fee - #{obj.rebackFee}, "
            + "\"update_time\" = now() "
            + "  where \"id\" = #{obj.id}")
    boolean updateSendResultByPullRpt(@Param("obj") MwSmsSendEntity smsSendEntity);

    @Update("update mw_sms_send set "
        + "\"status\" = #{obj.status} , "
        + "\"update_time\" = now() "
        + "  where \"id\" = #{obj.id}")
    boolean updateSendEntityStatus(@Param("obj") MwSmsSendEntity smsSendEntity);

    @Select("select * from mw_sms_send where status = #{status} order by schedule_time asc, create_time desc limit #{limit}")
    List<MwSmsSendEntity> querySmsSendByStatus(@Param("status") Integer status, @Param("limit") Integer limit);

    @Select("select * from mw_sms_send where status = #{status} and (channel_type = 1 or channel_type = 3 or channel_type = 4) order by schedule_time asc, create_time desc limit #{limit}")
    List<MwSmsSendEntity> queryMarketingSmsSendByStatus(@Param("status") Integer status, @Param("limit") Integer limit);

    @Select("select * from mw_sms_send where status = #{status} and (channel_type != 1 and channel_type != 3 and channel_type != 4)  order by schedule_time asc, create_time desc limit #{limit}")
    List<MwSmsSendEntity> queryNotMarketingSmsSendByStatus(@Param("status") Integer status, @Param("limit") Integer limit);

    /**
     * @param conditionStatus 防止并发 发送mwSmsSendTask、拉取错误结果mwPullSendResultTask 导致并发错误
     */
    @Update("<script> update mw_send_detail set status = #{obj.status}, err_status = #{obj.errStatus}, err_code = #{obj.errCode} , err_desc = #{obj.errDesc}, update_time = now() where id = #{obj.id} <if  test = 'conditionStatus != null'> and status = #{conditionStatus} </if> </script>")
    boolean updateSendDetailStatus(@Param("obj") MwSendDetailEntity sendDetailEntity, @Param("conditionStatus") Integer conditionStatus);


    @Update("<script> " +
            "update " +
                " mw_send_detail " +
            "set " +
                " status = #{status}, " +
                "<if test = 'reply != null'> " +
                    " err_desc = #{reply}, " +
                "</if> " +
                " update_time = now() " +
            "where id = #{id}" +
            "</script>")
    int updateSendDetailById(@Param("status") Integer status, @Param("reply") String reply, @Param("id") String id);

    @Update("update mw_send_detail set status = #{status} where send_id = #{sendId} ")
    int batchUpdateSendDetailStatusBySendId(@Param("sendId") String sendId, @Param("status") Integer status );

    @Select("select * from mw_send_detail where send_id = #{sendId} and phone = #{phone}")
    MwSendDetailEntity querySendDetailBySendIdAndPhone(@Param("sendId") String sendId, @Param("phone") String phone);

    @Select("select * from mw_sms_send where ea = #{ea} and channel_type = #{channelType} order by create_time desc")
    List<MwSmsSendEntity> listSmsFeeByChannelType(@Param("ea") String ea, @Param("channelType") Integer channelType, @Param("page") Page page);

    @Select("<script>" +
        "SELECT mss.ea, mss.channel_type, sum(msd.fee) as totalFee \n"
        + "FROM mw_send_detail msd join mw_sms_send mss on msd.send_id = mss.id and msd.ea = mss.ea \n"
        + "WHERE mss.ea = #{ea} and mss.status = 1 and msd.status = 2 \n"
        + "    <if test=\"startDate != null\">\n"
        + "      AND msd.create_time &gt;= #{startDate}\n"
        + "    </if>\n"
        + "    <if test=\"endDate != null\">\n"
        + "      AND msd.create_time &lt; #{endDate}\n"
        + "    </if>\n"
        + "GROUP BY mss.ea, mss.channel_type\n"
        + "ORDER BY totalFee desc"
        + "</script>")
    List<SmsFeeStatisticEntity> querySmsFeeStatistic(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT channel_type, sum(fee) as totalFee \n"
            + "FROM mw_send_detail \n"
            + "WHERE ea = #{ea} and status = 2 \n"
            + "    <if test=\"startDate != null\">\n"
            + "      AND create_time &gt;= #{startDate}\n"
            + "    </if>\n"
            + "    <if test=\"endDate != null\">\n"
            + "      AND create_time &lt; #{endDate}\n"
            + "    </if>\n"
            + "GROUP BY channel_type\n"
            + "ORDER BY totalFee desc"
            + "</script>")
    List<SmsFeeStatisticEntity> querySmsFeeStatisticV2(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Insert("<script>" + "INSERT INTO mw_code (code,\"desc\") VALUES\n"
        + "    <foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\">\n"
        + "      (#{entity.code}, #{entity.desc})\n" + "    </foreach>" + "</script>")
    boolean batchInsertMwCode(@Param("entities") List<MwCodeEntity> entities);

    @Update("update mw_sms_send set "
        + "\"send_flag\" = #{sendFlagNew} , "
        + "\"update_time\" = now() "
        + "  where \"id\" = #{id} and send_flag=#{sendFlagOld}")
    boolean updateSendFlag(@Param("id") String id, @Param("sendFlagOld") Long sendFlagOld, @Param("sendFlagNew") Long sendFlagNew);

    @Update("UPDATE mw_sms_send SET send_time = now() WHERE id=#{id}")
    int updateSendTimeById(@Param("id")String id);

    @Select("<script>"
        + "select mst.name as templateName, msd.content, msd.phone, mss.channel_type as channelType, msd.status, msd.create_time as createTime, msd.err_code as errCode, mc.desc as errCodeDesc \n"
        + "from mw_sms_send mss\n"
        + "join mw_send_detail msd on mss.id = msd.send_id \n"
        + "join mw_sms_template mst on mst.id = mss.template_id and mst.id != '40210d2fbb1640e1882aa698c2708f0c'\n"
        + "left join mw_code mc on mc.code = msd.err_code \n"
        + "where mss.ea = #{ea} and msd.ea = #{ea} \n"
        + "<if test=\"status != null\"> "
        + "     and msd.status = #{status} \n"
        + " </if> "
        + "<if test=\"channelType == 1\"> "
        + "     and mss.channel_type in (1, 3, 4) \n"
        + " </if> "
        + "<if test=\"channelType == 2 or channelType == 5 or channelType == 6 or channelType == 7 or channelType == 8 "
        + " or channelType == 9 or channelType == 10 or channelType == 11 or channelType == 99 \"> "
        + "     and mss.channel_type = #{channelType} \n"
        + " </if> "
        + "<if test=\"channelType == null\"> "
        + "     and mss.channel_type is not null \n"
        + " </if> "
        + "<if test=\"startTime != null\">"
        + "     and msd.create_time &gt;= #{startTime} \n "
        + "</if>   "
        + " <if test=\"endTime != null\">"
        + "     and msd.create_time &lt;= #{endTime} \n"
        + "</if>\n"
        + "<if test=\"keyStr != null\">"
        +	    "AND (mst.name like concat('%',#{keyStr},'%') OR msd.phone like concat('%',#{keyStr},'%')) \n"
        + "</if>"
        + "ORDER BY msd.create_time desc\n"
        + "</script>")
    List<SmsSendDetailDTO> getSendDetail2Page(@Param("ea") String ea, @Param("status") Integer status, @Param("channelType") Integer channelType, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("keyStr") String keyStr, @Param("page") Page page);


    @Select("<script>"
            + "select mss.template_id templateId, msd.content, msd.phone, mss.channel_type as channelType, msd.status, msd.create_time as createTime, msd.err_code as errCode \n"
            + "from  mw_sms_send mss\n"
            + "join mw_send_detail msd on mss.ea = msd.ea and mss.id = msd.send_id \n"
            + "where mss.ea = #{ea} and msd.ea = #{ea} and mss.template_id != '40210d2fbb1640e1882aa698c2708f0c' \n"
            + "<if test=\"status != null\"> "
            + "     and msd.status = #{status} \n"
            + " </if> "
            + "<if test=\"channelType == 1\"> "
            + "     and mss.channel_type in (1, 3, 4) \n"
            + " </if> "
            + "<if test=\"channelType == 2 or channelType == 5 or channelType == 6 or channelType == 7 or channelType == 8 "
            + " or channelType == 9 or channelType == 10 or channelType == 11 or channelType == 99 \"> "
            + "     and mss.channel_type = #{channelType} \n"
            + " </if> "
            + "<if test=\"channelType == null\"> "
            + "     and mss.channel_type is not null \n"
            + " </if> "
            + "<if test=\"startTime != null\">"
            + "     and msd.create_time &gt;= #{startTime} \n "
            + "</if>   "
            + " <if test=\"endTime != null\">"
            + "     and msd.create_time &lt;= #{endTime} \n"
            + "</if>\n"
            + "ORDER BY msd.create_time desc\n"
            + "</script>")
    List<SmsSendDetailDTO> getSendDetail2PageV2(@Param("ea") String ea, @Param("status") Integer status, @Param("channelType") Integer channelType, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("page") Page page);

    @Select("<script>"
            + "select template_id templateId, content, phone, channel_type as channelType, status, create_time as createTime, err_code as errCode \n"
            + "from  mw_send_detail\n"
            + "where ea = #{ea} and template_id != '40210d2fbb1640e1882aa698c2708f0c' \n"
            + "<if test=\"status != null\"> "
            + "     and status = #{status} \n"
            + " </if> "
            + "<if test=\"channelType == 1\"> "
            + "     and channel_type in (1, 3, 4) \n"
            + " </if> "
            + "<if test=\"channelType == 2 or channelType == 5 or channelType == 6 or channelType == 7 or channelType == 8 "
            + " or channelType == 9 or channelType == 10 or channelType == 11 or channelType == 99 \"> "
            + "     and channel_type = #{channelType} \n"
            + " </if> "
            + "<if test=\"channelType == null\"> "
            + "     and channel_type is not null \n"
            + " </if> "
            + "<if test=\"startTime != null\">"
            + "     and create_time &gt;= #{startTime} \n "
            + "</if>   "
            + " <if test=\"endTime != null\">"
            + "     and create_time &lt;= #{endTime} \n"
            + "</if>\n"
            + "ORDER BY create_time desc\n"
            + "</script>")
    List<SmsSendDetailDTO> getSendDetail2PageV3(@Param("ea") String ea, @Param("status") Integer status, @Param("channelType") Integer channelType, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("page") Page page);

    @Update("update mw_send_detail set "
        + "\"err_status\" = #{errStatus}, "
        + "\"update_time\" = now() "
        + "  where \"id\" = #{id} and err_status is null")
    boolean updateSendDetailErrStatus(@Param("id") String id, @Param("errStatus") Integer errStatus);

    @Update("update mw_sms_send set type = #{type} where ea = #{ea} and id = #{id}")
    boolean updateSendTypeById(@Param("ea") String ea, @Param("id") String id, @Param("type") Integer type);

    @Select("<script>" +
            "SELECT SUM(total_fee) FROM mw_sms_send WHERE \"id\" IN " +
            "<foreach collection='ids' item='id' separator=',' open='(' close=')'>#{id}</foreach>" +
            "</script>")
    Integer sumTotalFeeByIds(@Param("ids") Collection<String> ids);

    @Select("<script>" +
            "SELECT count(*) FROM mw_send_detail WHERE ea = #{ea} and phone IN " +
            "<foreach collection='phones' item='phone' separator=',' open='(' close=')'>#{phone}</foreach>" +
            "<if test=\"startTime != null\">"
            + "     and create_time &gt;= #{startTime} \n "
            + "</if>   "
            + " <if test=\"endTime != null\">"
            + "     and create_time &lt;= #{endTime} \n"
            + "</if>\n" +
            "</script>")
    int countSendDetailByPhones(@Param("ea") String ea, @Param("phones") List<String> phones, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>" +
            "select distinct a.marketing_activity_id " +
            "from marketing_activity_external_config a " +
            "left join mw_send_detail b on a.associate_id = b.send_id " +
            "where b.ea = #{ea} and b.phone IN " +
            "<foreach collection='phones' item='phone' separator=',' open='(' close=')'>#{phone}</foreach>" +
              "<if test=\"startTime != null\">"
            + "     and b.create_time &gt;= #{startTime} \n "
            + "</if>   "
            + " <if test=\"endTime != null\">"
            + "     and b.create_time &lt;= #{endTime} \n"
            + "</if>\n" +
            "</script>")
    List<String> queryActivityIdsByPhones(@Param("ea") String ea, @Param("phones") List<String> phones, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("SELECT * FROM mw_code")
    List<MwCodeEntity> getAllMwCodeList();

    @Select("select * from mw_sms_send where where create_time >= '2023-05-19 23:00:00' ")
    MwSmsSendEntity ge( @Param("id") String id);

    @Select("<script>"
            + "select * from mw_sms_send  where create_time &gt;= #{specialTime}"
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id &gt; #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<MwSmsSendEntity> scanBySpecialTime(@Param("lastId") String lastId, @Param("specialTime") Date specialTime, @Param("limit") int limit);

    @Select( "select count(*) from mw_sms_send  where create_time >= #{specialTime} ")
    int countBySpecialTime(@Param("specialTime") Date specialTime);

    @Update("<script> update mw_send_detail "
            + " <set>"
            +"    <if test=\"templateId != null\">"
            +"          template_id = #{templateId},\n"
            +"    </if>"
            +"    <if test=\"channelType != null\">"
            +"          channel_type = #{channelType},\n"
            +"    </if>"
            + " update_time = now()"
            + " </set> "
            + "where send_id = #{sendId} and ea = #{ea} " +
            "</script>")
    int updateDetailTemplateIdAndChannelType(@Param("ea") String ea, @Param("sendId") String sendId, @Param("templateId") String templateId, @Param("channelType") Integer channelType);

    @Select( "select count(*) from mw_send_detail  where send_id = #{sendId} and ea = #{ea} ")
    int countTemplateAndChannelTypeIsNull(@Param("ea") String ea, @Param("sendId") String sendId);

    @Select ("<script>"
            + "SELECT Distinct ea FROM mw_send_detail WHERE create_time between #{startTime} and #{endTime} AND ea IN\n"
            + "<foreach collection='eas' item='ea' separator=',' open='(' close=')'>#{ea}</foreach>"
            + "</script>")
    List<String> findSendedEaByEasAndCreateTime(@Param("eas")List<String> eas, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select ("<script>" +
            "select distinct detail.phone as phone " +
            "from mw_send_detail detail " +
            "inner join mw_sms_send send on send.id = detail.send_id and send.ea = detail.ea " +
            "inner join marketing_activity_external_config config on config.associate_id = send.id and config.associate_id_type = 17 and config.ea = send.ea " +
            "where detail.ea = #{ea} and detail.status = 2 and send.template_id = #{templateId} and config.marketing_event_id = #{marketingEventId} " +
            "</script>")
    List<String> querySuccessPhoneByMktIdAndTemplateId(@Param("ea") String ea, @Param("templateId") String templateId, @Param("marketingEventId") String marketingEventId);

    @Select("select * from mw_send_detail where id = #{id}")
    MwSendDetailEntity getDetailById(@Param("id") String id);

    @Update("UPDATE mw_sms_send SET actual_sender_count = actual_sender_count + 1 WHERE id=#{id}")
    int incrementActualSenderCount(@Param("id") String id);
}
