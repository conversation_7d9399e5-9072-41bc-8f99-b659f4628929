package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity;
import com.github.mybatis.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/07/03
 **/
public interface MailSendReplyDAO {


    @Select("<script>"
        + " SELECT * FROM mail_send_reply WHERE ea = #{ea} AND type = #{type} AND status != 99 "
        + "<if test=\"name != null\">"
        +   " AND name LIKE CONCAT('%', #{name}, '%')"
        + "</if>"
        + " ORDER BY default_value DESC, update_time DESC"
        + "</script>")
    List<MailSendReplyEntity> querySendReplyByType(@Param("ea") String ea, @Param("type") Integer type, @Param("name") String name, @Param("page") Page page);


    @Insert("INSERT INTO mail_send_reply(id, ea, type, name, address, status, default_value, create_time, update_time) VALUES(#{entity.id}, #{entity.ea}, #{entity.type}, #{entity.name}, #{entity.address}, #{entity.status}, #{entity.defaultValue}, now(), now())")
    int insert(@Param("entity") MailSendReplyEntity entity);


    @Select(" SELECT * FROM  mail_send_reply WHERE id = #{id}")
    MailSendReplyEntity getById(@Param("id") String id);

    @Select("<script> "
            + "SELECT * FROM  mail_send_reply WHERE id IN"
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<MailSendReplyEntity> getByIds(@Param("ids")List<String> ids);

    @Select(" SELECT * FROM mail_send_reply WHERE ea = #{ea} AND type = #{type} AND default_value = true")
    MailSendReplyEntity getDefaultValueByType(@Param("ea") String ea, @Param("type") Integer type);

    @Update("<script>"
        + " UPDATE mail_send_reply " +
        "        <set>\n" +
        "            <if test=\"name != null\">\n" +
        "                \"name\" = #{name},\n" +
        "            </if>\n" +
        "            <if test=\"address != null\">\n" +
        "                \"address\" = #{address},\n" +
        "            </if>\n" +
        "            <if test=\"status != null\">\n" +
        "                \"status\" = #{status},\n" +
        "            </if>\n" +
        "            <if test=\"defaultValue != null\">\n" +
        "                \"default_value\" = #{defaultValue},\n" +
        "            </if>\n" +
        "        update_time = now() "+
        "        </set>\n" +
        "        WHERE id = #{id}" +
        "</script>")
    int updateById(@Param("id") String id, @Param("name") String name, @Param("address") String address, @Param("status") Integer status, @Param("defaultValue") Boolean defaultValue);

    @Select("SELECT * FROM mail_send_reply WHERE type=0 AND status != 99")
    List<MailSendReplyEntity> listAllUsableMailSender();

    @Select("SELECT * FROM mail_send_reply WHERE ea = #{ea} AND type = #{type} AND status != 99 AND address = #{address} ORDER BY create_time DESC LIMIT 1")
    MailSendReplyEntity queryByEmailAndType(@Param("ea") String ea, @Param("type") Integer type, @Param("address")String address);

    @Select("<script> "
            + "SELECT * FROM  mail_send_reply WHERE ea = #{ea} AND id IN"
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<MailSendReplyEntity> getByEaAndIds(@Param("ea") String ea, @Param("ids")List<String> ids);

}
