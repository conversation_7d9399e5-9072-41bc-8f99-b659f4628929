package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.vo.SceneTriggerVO;
import com.facishare.marketing.api.vo.qywx.ListSceneTriggerVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.MarketingEventFormEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.MarketingEventSetConfigArg.EventTypeAndColorData;
import com.facishare.marketing.api.arg.marketingEvent.GetMarketingEventByObjectIdArg;
import com.facishare.marketing.api.arg.marketingEvent.ListMultiVenueMarketingEventArg;
import com.facishare.marketing.api.arg.marketingEvent.SaveSyncRulesArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.marketingEvent.*;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.vo.QueryMarketingEventLevelDataVO;
import com.facishare.marketing.api.vo.marketingevent.*;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldValue;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.pay.PayOrderEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.pay.PayOrderManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingEventRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.open.ding.api.enums.CrmApiNameEnum;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
@Slf4j
@Service("marketingEventService")
public class MarketingEventServiceImpl implements MarketingEventService {
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private ContentMarketingEventMarketingUserGroupRelationDAO contentMarketingEventMarketingUserGroupRelationDAO;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private PayOrderManager payOrderManager;
    @Autowired
    private CampaignPayOrderDao campaignPayOrderDao;

    @Autowired
    private MarketingEventSyncRuleDao marketingEventSyncRuleDao;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @ReloadableProperty("qywx.group.message.default.cover")
    private String defaultCoverPath;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private MarketingEventCommonSettingManager marketingEventCommonSettingManager;
    @Autowired
    private BoardDao boardDao;
    @Autowired
    private BoardService boardService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private MarketingSceneDao marketingSceneDao;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private MarketingSceneService marketingSceneService;
    @Autowired
    private WorkspaceSyncSseServiceImpl workspaceSyncSseServiceImpl;

    @Autowired
    @Qualifier("workspaceSyncSseServiceV2")
    private WorkspaceSyncSseService workspaceSyncSseServiceV2;

    // Redis中存储同步状态的Key前缀
    private static final String WORKSPACE_SYNC_STATUS_PREFIX = "workspace_sync_status:";
    // Redis中同步状态Key的过期时间（秒），比分布式锁时间稍长，确保前端有足够时间查询
    private static final int WORKSPACE_SYNC_STATUS_EXPIRE_SECONDS = 600; // 10分钟

    @Override
    public Result<List<MarketingEventsBriefResult>> listBriefMarketingEvents(String ea, Integer fsUserId, ListBriefMarketingEventsArg arg) {
        List<String> nlikeEventFormList = Lists.newArrayList();
        nlikeEventFormList.add(MarketingEventFormEnum.ADVERTISE.getValue());
        arg.setNlikeEventFormList(nlikeEventFormList);
        List<MarketingEventsBriefResult> datas = marketingEventManager
            .listMarketingEvents(eieaConverter.enterpriseAccountToId(ea), fsUserId, arg);
        return Result.newSuccess(datas);
    }

    @Override
    public Result<MarketingEventsResult> getMarketingEventsDetail(String ea, Integer fsUserId, String marketingEventId) {
        MarketingEventsResult result = marketingEventManager.getMarketingEventsDetail(ea,fsUserId,marketingEventId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> addMaterial(String ea, Integer fsUserId, AddMaterialArg arg) {
        MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, arg.getMarketingEventId());
        if (marketingEventData == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }

        ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setObjectId(arg.getObjectId());
        entity.setObjectType(arg.getObjectType());
        entity.setMarketingEventId(arg.getMarketingEventId());
        entity.setIsMobileDisplay(false);
        contentMarketingEventMaterialRelationDAO.save(entity);
        ThreadPoolUtils.execute(() -> handleSpecialMaterial(arg.getObjectId(), arg.getObjectType(), arg.getMarketingEventId(), ea), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess(entity.getId());
    }

    @Override
    public Result<Boolean> checkAndAddMaterials(String ea, Integer fsUserId, AddMaterialsArg arg) {
        List<ContentMarketingEventMaterialRelationEntity> entities = Lists.newArrayList();
        arg.getMaterialInfos().forEach(val -> {
            ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setObjectId(val.getObjectId());
            entity.setObjectType(val.getObjectType());
            entity.setMarketingEventId(arg.getMarketingEventId());
            entities.add(entity);
        });
        List<ContentMarketingEventMaterialRelationEntity> toAddEntities = getDistinctEntities(ea, entities);
        if (CollectionUtils.isNotEmpty(toAddEntities)) {
            contentMarketingEventMaterialRelationDAO.batchAdd(toAddEntities);
        }
        return Result.newSuccess();
    }

    private List<ContentMarketingEventMaterialRelationEntity> getDistinctEntities(String ea, List<ContentMarketingEventMaterialRelationEntity> entities) {
        List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> objectInfos = convert(entities);
        List<ContentMarketingEventMaterialRelationEntity> hasExistEntities = contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, objectInfos);
        return entities.stream().filter(data -> {
            for (ContentMarketingEventMaterialRelationEntity hasExistEntity : hasExistEntities) {
                if (hasExistEntity.getEa().equals(data.getEa()) && hasExistEntity.getObjectType() == data.getObjectType() && hasExistEntity.getObjectId().equals(data.getObjectId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
    }

    private List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> convert(List<ContentMarketingEventMaterialRelationEntity> entities) {
        List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> objectInfos = entities.stream().map(val -> {
            ContentMarketingEventMaterialRelationEntity.ObjectInfo objectInfo = new ContentMarketingEventMaterialRelationEntity.ObjectInfo();
            objectInfo.setObjectType(val.getObjectType());
            objectInfo.setObjectId(val.getObjectId());
            objectInfo.setMarketingEventId(val.getMarketingEventId());
            return objectInfo;
        }).collect(Collectors.toList());
        return objectInfos;
    }

    @Override
    public PageResult<AbstractMaterialData> listMaterials(String ea, Integer fsUserId, ListMaterialsArg arg) {
        PageResult<AbstractMaterialData> materials = marketingEventManager.getMaterials(ea, arg.getObjectTypes(), arg.getId(), arg.getPageNum(), arg.getPageSize(), false,null);
        return materials;
    }

    @Override
    public Result mergeUserGroupToMarketingEvent(String ea, Integer fsUserId, AddUserGroupToMarketingEventArg arg) {
        List<ContentMarketingEventMarketingUserGroupRelationEntity> entities = contentMarketingEventMarketingUserGroupRelationDAO.list(ea, arg.getMarketingEventId());
        Set<String> userGroupIds = entities.stream().map(ContentMarketingEventMarketingUserGroupRelationEntity::getMarketingUserGroupId).collect(Collectors.toSet());
        Set<String> toKeepUserGroupIds = Sets.newHashSet(userGroupIds);
        toKeepUserGroupIds.retainAll(arg.getUserGroupIds());
        Set<String> toDeleteUserGroupIds = Sets.newHashSet(userGroupIds);
        toDeleteUserGroupIds.removeAll(toKeepUserGroupIds);
        Set<String> toInsertUserGroupIds = Sets.newHashSet(arg.getUserGroupIds());
        toInsertUserGroupIds.removeAll(toKeepUserGroupIds);
        if (CollectionUtils.isNotEmpty(toDeleteUserGroupIds)) {
            contentMarketingEventMarketingUserGroupRelationDAO.deleteByMarketingEventIdAndUserGroupIds(ea, arg.getMarketingEventId(), Lists.newArrayList(toDeleteUserGroupIds));
        }
        if (CollectionUtils.isNotEmpty(toInsertUserGroupIds)) {
            List<ContentMarketingEventMarketingUserGroupRelationEntity> toInsertEntities = Lists.newArrayList();
            for (String userGroupId : toInsertUserGroupIds) {
                ContentMarketingEventMarketingUserGroupRelationEntity entity = new ContentMarketingEventMarketingUserGroupRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setMarketingEventId(arg.getMarketingEventId());
                entity.setMarketingUserGroupId(userGroupId);
                toInsertEntities.add(entity);
            }
            contentMarketingEventMarketingUserGroupRelationDAO.batchInsert(toInsertEntities);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<MarketingUserGroupData>> listUserGroupFromMarketingEvent(String ea, Integer fsUserId, String marketingEventId) {
        List<MarketingUserGroupData> marketingUserGroups = marketingEventManager.getMarketingUserGroups(ea, marketingEventId);
        return Result.newSuccess(marketingUserGroups);
    }

    @Override
    public Result deleteRelation(String ea, Integer fsUserId, String marketingEventId, Integer objectType, String objectId){
        contentMarketingEventMaterialRelationDAO.deleteByEaAndMarketingEventIdAndObjectTypeAndObjectId(ea,marketingEventId,objectType,objectId);
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> setEventTypeAndColorConfig(String ea, Integer fsUserId, List<EventTypeAndColorData> eventTypeAndColorDataList) {
        FieldValueList eventTypeAndColorDataFieldValueList = convert2FieldValueList(eventTypeAndColorDataList);
        enterpriseMetaConfigDao.updateMarketingEventTypeColor(ea,eventTypeAndColorDataFieldValueList);
        return Result.newSuccess();
    }

    @Override
    public Result<List<EventTypeAndColorData>> getOrCreateEventTypeAndColorConfig(String ea, Integer fsUserId) {
        EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
        FieldValueList marketingEventTypeColor = enterpriseMetaConfigEntity.getMarketingEventTypeColor();
        List<EventTypeAndColorData> eventTypeAndColorDataList = convert2EventTypeAndColorDataList(marketingEventTypeColor);
        return Result.newSuccess(eventTypeAndColorDataList);
    }

    public FieldValueList convert2FieldValueList(List<EventTypeAndColorData> eventTypeAndColorDataList){
        FieldValueList fieldValueList = new FieldValueList();
        if (CollectionUtils.isEmpty(eventTypeAndColorDataList)){
            return fieldValueList;
        }
        for (EventTypeAndColorData eventTypeAndColorData : eventTypeAndColorDataList) {
            if (StringUtils.isEmpty(eventTypeAndColorData.getColor())||StringUtils.isEmpty(eventTypeAndColorData.getEventType())){
                continue;
            }
            FieldValue fieldValue = new FieldValue(eventTypeAndColorData.getEventType(),eventTypeAndColorData.getColor());
            fieldValueList.add(fieldValue);
        }
        return fieldValueList;
    }


    public List<EventTypeAndColorData>  convert2EventTypeAndColorDataList(FieldValueList eventTypeAndColorFieldValueList){
        List<EventTypeAndColorData>  eventTypeAndColorDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(eventTypeAndColorFieldValueList)){
            return eventTypeAndColorDataList;
        }
        for (FieldValue eventTypeAndColorFieldValue : eventTypeAndColorFieldValueList) {
            if (StringUtils.isEmpty(eventTypeAndColorFieldValue.getName())||StringUtils.isEmpty(eventTypeAndColorFieldValue.getValue())){
                continue;
            }
            EventTypeAndColorData fieldValue = new EventTypeAndColorData(eventTypeAndColorFieldValue.getName(),eventTypeAndColorFieldValue.getValue());
            eventTypeAndColorDataList.add(fieldValue);
        }
        return eventTypeAndColorDataList;
    }

    // TODO 修改为简单工厂模式
    private void handleSpecialMaterial(String objectId, Integer objectType, String marketingEventId, String ea) {
        if (objectType == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
            // 表单创建二维码
            customizeFormDataManager.createCustomizeFormDataQRCode(objectId, marketingEventId, ea);
        }
    }

    public Result<PageResult<GetMarketingEventByObjectIdResult>> getMarketingEventByObjectId(String ea, Integer fsUserId, GetMarketingEventByObjectIdArg arg) {
        Long time = (arg.getTime() == null ? new Date().getTime() : arg.getTime());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);

        List<GetMarketingEventByObjectIdResult> resultList = new ArrayList<>();

        List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntityList = contentMarketingEventMaterialRelationDAO.getByEaAndObjectTypeAndObjectId(ea, arg.getObjectType(), arg.getObjectId(), page);
        if (CollectionUtils.isNotEmpty(contentMarketingEventMaterialRelationEntityList)) {
            List<String> marketingEventIds = new ArrayList<>();
            for (ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity : contentMarketingEventMaterialRelationEntityList) {
                marketingEventIds.add(contentMarketingEventMaterialRelationEntity.getMarketingEventId());
            }

            Map<String, com.fxiaoke.crmrestapi.common.data.MarketingEventData> marketingEventDataMap = new HashMap<>();
            Filter filter = new Filter();
            filter.setFieldName("_id");
            filter.setOperator(OperatorConstants.IN);
            filter.setFieldValues(marketingEventIds);
            int totalCount = crmV2Manager.countByFilters(ea, -10000, MarketingEventFieldContants.API_NAME, ImmutableList.of(filter));
            int pageSize = 1000;
            int totalPage = totalCount/pageSize + totalCount%pageSize;
            for (int pageNum = 0; pageNum < totalPage; pageNum++) {
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.setLimit(pageSize);
                searchQuery.setOffset(pageNum * pageSize);
                searchQuery.addFilter("_id", marketingEventIds, OperatorConstants.IN);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmMarketingEventResult = crmV2Manager.getList(ea, -10000, MarketingEventFieldContants.API_NAME, searchQuery);
                List<ObjectData> dataList = crmMarketingEventResult.getDataList();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
                    for (ObjectData objectData : dataList) {
                        com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventVO = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(objectData);
                        if (null != marketingEventVO) {
                            marketingEventDataMap.put(marketingEventVO.getId(), marketingEventVO);
                        }
                    }
                }
            }
            List<Integer> userIds = new ArrayList<>();
            Iterator<Map.Entry<String, com.fxiaoke.crmrestapi.common.data.MarketingEventData>> it = marketingEventDataMap.entrySet().iterator();
            while(it.hasNext()) {
                Map.Entry<String, com.fxiaoke.crmrestapi.common.data.MarketingEventData> entry = it.next();
                com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventData = entry.getValue();
                userIds.add(marketingEventData.getCreateBy());
            }

            Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);

            for (ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity : contentMarketingEventMaterialRelationEntityList) {
                GetMarketingEventByObjectIdResult result = new GetMarketingEventByObjectIdResult();
                result.setMarketingEventId(contentMarketingEventMaterialRelationEntity.getMarketingEventId());

                com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventData = marketingEventDataMap.get(contentMarketingEventMaterialRelationEntity.getMarketingEventId());
                if (marketingEventData == null) {
                    continue;
                }
                result.setMarketingEventName(marketingEventData.getName());
                result.setMarketingEventType(marketingEventData.getEventType());
                result.setMarketingEventBizStatus(marketingEventData.getBizStatus());
                if (MapUtils.isNotEmpty(fsEmployeeMsgMap) && null != marketingEventData.getCreateBy()) {
                    FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(marketingEventData.getCreateBy());
                    if (null != fsEmployeeMsg) {
                        result.setCreator(fsEmployeeMsg.getName());
                    }
                }

                resultList.add(result);
            }
        }

        PageResult<GetMarketingEventByObjectIdResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }
    
    @Override
    public Result<Void> updateIsApplyObject(String relationId, Boolean isApplyObject) {
        contentMarketingEventMaterialRelationDAO.updateIsApplyObject(relationId, isApplyObject);
        return Result.newSuccess();
    }
    
    @Override
    public PageResult<AbstractMaterialData> listMaterialsByMarketingEvent(String ea, Integer fsUserId, ListMaterialsByMarketingEventArg arg) {
        return marketingEventManager.listMaterialsByMarketingEvent(ea, arg.getObjectType(), arg.getId(), arg.getTitle(), arg.getPageNum(), arg.getPageSize());
    }
    
    @Override
    public Result<List<PayOrderResult>> listPayOrdersByCampaignId(String ea, Integer fsUserId, ListPayOrdersByCampaignIdArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getCampaignId()));
        List<String> payOrderIds = campaignPayOrderDao.listPayOrderIdsByCampaignId(ea, arg.getCampaignId());
        Map<String, PayOrderEntity> payOrderIdToEntityMap = payOrderManager.syncAndGetPayOrderByIds(payOrderIds);
        List<PayOrderResult> result = payOrderIds.stream().filter(payOrderId -> payOrderIdToEntityMap.get(payOrderId) != null).map(payOrderId -> {
            PayOrderResult r = new PayOrderResult();
            BeanUtils.copyProperties(payOrderIdToEntityMap.get(payOrderId), r, "createTime");
            r.setCreateTime(payOrderIdToEntityMap.get(payOrderId).getCreateTime().getTime());
            return r;
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryMarketingEventLevelDataResult> queryMarketingEventLevelData(QueryMarketingEventLevelDataVO vo) {
        QueryMarketingEventLevelDataResult queryMarketingEventLevelDataResult = new QueryMarketingEventLevelDataResult();
        List<ObjectData> objectDataList = crmV2Manager.getMarketingEventTreeByPath(vo.getEa(), "marketing_event_path", vo.getMarketingEventId(), true);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Result.newSuccess(queryMarketingEventLevelDataResult);
        }
        List<QueryMarketingEventLevelDataResult.LevelData> dataList = Lists.newArrayList();
        for (ObjectData objectData : objectDataList) {
            QueryMarketingEventLevelDataResult.LevelData levelData  = new  QueryMarketingEventLevelDataResult.LevelData();
            levelData.setId(objectData.getId());
            levelData.setName(objectData.getName());
            levelData.setParentId(objectData.getString("parent_id"));
            dataList.add(levelData);
        }
        queryMarketingEventLevelDataResult.setDataList(dataList);
        return Result.newSuccess(queryMarketingEventLevelDataResult);
    }

    @Override
    public Result<Void> relateSubMarketingEvent(RelateSubMarketingEventVO vo) {
        // 校验子活动的合理性
        List<String> subMarketingEventIds = vo.getSubMarketingEventIds();
        Result<Void> checkResult = checkSubMarketingEvent(vo.getEa(), vo.getFsUserId(), subMarketingEventIds);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        // 关联处理
        ThreadPoolUtils.execute(() -> {
            for (String subMarketingEventId : subMarketingEventIds) {
                marketingEventManager.updateParentIdToCrm(vo.getEa(), vo.getMarketingEventId(), subMarketingEventId);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> unRelateSubMarketingEvent(UnRelateSubMarketingEventVO vo) {
        // 解绑处理
        marketingEventManager.updateParentIdToCrm(vo.getEa(), "", vo.getMarketingEventId());
        return Result.newSuccess();
    }

    private Result<Void> checkSubMarketingEvent(String ea, Integer fsUserId, List<String> subMarketingEventIds){
        // 去除不符合条件的活动
        /*subMarketingEventIds.removeIf(subMarketingEventId -> {
            // 检查活动类型和是否有父活动
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, subMarketingEventId);
            if (Objects.equals(marketingEventData.getEventType(), MarketingEventEnum.MULTIVENUE_MARKETING.getEventType()) || StringUtils.isNotBlank(marketingEventData.getParentId())) {
                return true;
            }
            // 检查有无子活动
            List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventDataByParentId(ea, fsUserId, subMarketingEventId);
            if (CollectionUtils.isNotEmpty(marketingEventDataList)) {
                return true;
            }
            return false;
        });

        if (CollectionUtils.isEmpty(subMarketingEventIds)) {
            return Result.newError(SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR);
        }*/

        for (String subMarketingEventId : subMarketingEventIds) {
            // 检查活动类型和是否有父活动
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, subMarketingEventId);
            if (Objects.equals(marketingEventData.getEventForm(), MarketingEventFormEnum.MULTIVENUE_MARKETING.getValue()) || StringUtils.isNotBlank(marketingEventData.getParentId())) {
                return Result.newError(SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR);
            }
            // 检查有无子活动
            List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventDataByParentId(ea, fsUserId, subMarketingEventId);
            if (CollectionUtils.isNotEmpty(marketingEventDataList)) {
                return Result.newError(SHErrorCode.MULTIVENUE_RELATE_SUB_ERROR);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<List<MarketingEventSimpleResult>> getSubMarketingEvent(GetSubMarketingEventVO vo) {
        List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventDataByParentId(vo.getEa(), vo.getFsUserId(), vo.getMarketingEventId());
        if (CollectionUtils.isNotEmpty(marketingEventDataList)) {
            List<MarketingEventSimpleResult> resultList = marketingEventDataList.stream().map(marketingEventData -> {
                MarketingEventSimpleResult simpleResult = new MarketingEventSimpleResult();
                simpleResult.setId(marketingEventData.getId());
                simpleResult.setName(marketingEventData.getName());
                simpleResult.setBeginTime(marketingEventData.getBeginTime());
                simpleResult.setEndTime(marketingEventData.getEndTime());
                simpleResult.setEventType(marketingEventData.getEventType());
                return simpleResult;
            }).collect(Collectors.toList());
            return Result.newSuccess(resultList);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<MultiVenueMarketingEventResult> getMultiVenueMarketingEvent(GetMultiVenueMarketingEventVO vo) {
        return marketingEventManager.getMultiVenueMarketingEventsDetail(vo.getEa(), vo.getFsUserId(), vo.getMarketingEventId());
    }

    @Override
    public Result<Void> syncDataToSubMarketingEvent(SyncDataToSubMarketingEventVO vo) {
        List<String> subMarketingEventIds = vo.getSubMarketingEventIds();
        if (CollectionUtils.isEmpty(subMarketingEventIds)) {
            return Result.newSuccess();
        }

        Result<Void> result = syncCampaignMember(vo.getEa(), subMarketingEventIds, vo.getCampaignIds(), vo.isAuto());
        return result;
    }

    /**
     * 1 根据ea查出规则，再查询参会人员
     * 2 组装参数，复用syncDataToSubMarketingEvent执行同步
     *
     * @return
     */
    @Override
    public Result<Void> syncDataToTargetMarketingEventByRule() {
        ThreadPoolUtils.execute(() -> {
            List<MarketingEventSyncRuleEntity> entities = marketingEventSyncRuleDao.queryAll();
            if (CollectionUtils.isNotEmpty(entities)) {
                for (MarketingEventSyncRuleEntity entity : entities) {
                    syncDataToTargetMarketingEventByRuleEntity(entity, null);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncDataToTargetMarketingEventById(String ea, String marketingEventId, String campaignObjId) {
        ThreadPoolUtils.execute(() -> {
            List<MarketingEventSyncRuleEntity> entities = marketingEventSyncRuleDao.queryAllByMarketingEventId(ea, marketingEventId);
            if (CollectionUtils.isNotEmpty(entities)) {
                for (MarketingEventSyncRuleEntity entity : entities) {
                    syncDataToTargetMarketingEventByRuleEntity(entity, campaignObjId);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    private void syncDataToTargetMarketingEventByRuleEntity(MarketingEventSyncRuleEntity entity, String campaignObjId) {
        try {
            String ea = entity.getEa();
            List<SaveSyncRulesArg.TargetMarketingEvent> targetMarketingEvents = JSON.parseArray(entity.getTargetMarketingEvents(), SaveSyncRulesArg.TargetMarketingEvent.class);
            if (CollectionUtils.isEmpty(targetMarketingEvents)) return;
            List<String> subMarketingEventIds = targetMarketingEvents.stream().map(SaveSyncRulesArg.TargetMarketingEvent::getMarketingEventId).collect(Collectors.toList());
            RuleGroupList ruleGroupJson = entity.getRuleGroupJson();
            List<PaasQueryFilterArg> args = ruleGroupJson.stream().map(val -> BeanUtil.copyByGson(val, PaasQueryFilterArg.class)).collect(Collectors.toList());
            PaasQueryFilterArg paasQueryFilterArg = args.get(0);
            if (paasQueryFilterArg == null) {
                paasQueryFilterArg = new PaasQueryFilterArg();
                paasQueryFilterArg.setQuery(new PaasQueryArg(0, 1));
            }
            paasQueryFilterArg.setSelectFields(Collections.singletonList("_id"));
            paasQueryFilterArg.getQuery().getFilters().stream().peek(e -> {
                if ("tag".equals(e.getFieldName())) {
                    e.setValueType(11);
                    e.setOperator("IN".equals(e.getOperator()) ? "LIKE" : e.getOperator());
                }
            });
            paasQueryFilterArg.getQuery().addFilter("marketing_event_id", FilterOperatorEnum.EQ.getValue(), Collections.singletonList(entity.getMarketingEventId()));
            if (StringUtils.isNotBlank(campaignObjId)) {
                paasQueryFilterArg.getQuery().addFilter("_id", FilterOperatorEnum.EQ.getValue(), Collections.singletonList(campaignObjId));
            }
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                log.warn("MarketingEventServiceImpl.syncDataToTargetMarketingEventByRule ObjectDatas is empty ea:{} id:{}", ea, entity.getMarketingEventId());
                return;
            }
            List<String> campaignObjIds = objectDataInnerPage.getDataList().stream().map(data -> data.getString("_id")).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(campaignObjIds)) {
                log.warn("MarketingEventServiceImpl.syncDataToTargetMarketingEventByRule campaignObjIds is empty ea:{} id:{}", ea, entity.getMarketingEventId());
                return;
            }
            List<CampaignMergeDataEntity> campaignParticipantsDTOS = campaignMergeDataDAO.getCampaignMergeDataByCampaignMembersObjIds(ea, entity.getMarketingEventId(), campaignObjIds);
            if (CollectionUtils.isEmpty(campaignParticipantsDTOS)) {
                log.warn("MarketingEventServiceImpl.syncDataToTargetMarketingEventByRule campaignMergeDatas is empty ea:{} id:{}", ea, entity.getMarketingEventId());
                return;
            }
            List<String> campaignIds = campaignParticipantsDTOS.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
            SyncDataToSubMarketingEventVO vo = new SyncDataToSubMarketingEventVO();
            vo.setEa(ea);
            vo.setSubMarketingEventIds(subMarketingEventIds);
            vo.setCampaignIds(campaignIds);
            vo.setEa(ea);
            vo.setAuto(true);
            this.syncDataToSubMarketingEvent(vo);
        } catch (Exception e) {
            log.warn("MarketingEventServiceImpl.syncDataToTargetMarketingEventByRule error ea:{} id:{}", entity.getEa(), entity.getMarketingEventId(), e);
        }
    }

    @Override
    public Result<List<SaveSyncRulesArg.SyncRule>> getSyncRules(String ea, Integer fsUserId, IdArg arg) {
        Result<List<SaveSyncRulesArg.SyncRule>> result = new Result<>();
        List<SaveSyncRulesArg.SyncRule> data = Lists.newArrayList();
        result.setData(data);
        List<MarketingEventSyncRuleEntity> entities = marketingEventSyncRuleDao.queryAllByMarketingEventId(ea, arg.getId());
        if (CollectionUtils.isNotEmpty(entities)) {
            entities.forEach(e -> {
                List<SaveSyncRulesArg.TargetMarketingEvent> targetMarketingEvents = JSON.parseArray(e.getTargetMarketingEvents(), SaveSyncRulesArg.TargetMarketingEvent.class);
                data.add(new SaveSyncRulesArg.SyncRule(targetMarketingEvents, e.getRuleGroupJson()));
            });
        }
        return result;
    }

    @Override
    public Result<Void> saveSyncRules(String ea, Integer fsUserId, SaveSyncRulesArg arg) {
        marketingEventSyncRuleDao.deleteAllByMarketingEventId(ea, arg.getMarketingEventId());
        arg.getRules().forEach(e -> {
            String id = UUIDUtil.getUUID();
            String marketingEventId = arg.getMarketingEventId();
            List<SaveSyncRulesArg.TargetMarketingEvent> targetMarketingEvents = e.getTargetMarketingEvents();
            RuleGroupList ruleGroupJson = e.getRuleGroupJson();
            marketingEventSyncRuleDao.insert(id, ea, marketingEventId, JSON.toJSONString(targetMarketingEvents), ruleGroupJson, fsUserId);
        });
        syncDataToTargetMarketingEventById(ea, arg.getMarketingEventId(), null);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<MultiVenueMarketingEventListResult>> listMultiVenueMarketingEvent(ListMultiVenueMarketingEventArg arg) {
        if(StringUtils.isBlank(arg.getEa())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<MultiVenueMarketingEventListResult> pageResult = new PageResult<>();
        PageArg pageArg = new PageArg(arg.getPageNo(), arg.getPageSize());
        ListBriefMarketingEventsArg paasArg = new ListBriefMarketingEventsArg();
        if(StringUtils.isNotBlank(arg.getKeyword())){
            paasArg.setName(arg.getKeyword());
        }
        paasArg.setEventFormList(Lists.newArrayList(MarketingEventFormEnum.MULTIVENUE_MARKETING.getValue()));
        ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false);
        paasArg.setOrderByList(Lists.newArrayList(orderBy));
        com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult>  marketingEventsBriefResults = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(arg.getEa()), -10000, paasArg, pageArg);

        List<MultiVenueMarketingEventListResult> eventList = Lists.newArrayList();
        if (marketingEventsBriefResults != null && CollectionUtils.isNotEmpty(marketingEventsBriefResults.getData())) {
            for (MarketingEventsBriefResult result : marketingEventsBriefResults.getData()) {
                MultiVenueMarketingEventListResult dto = new MultiVenueMarketingEventListResult();
                dto.setMarketingEventId(result.getId());
                dto.setEventType(result.getEventType());
                if(result.getBeginTime() != null){
                    dto.setStartTime(result.getBeginTime());
                }
                if(result.getEndTime() != null){
                    dto.setEndTime(result.getEndTime());
                }
                if (Objects.nonNull(result.getCover())){
                    dto.setCover(result.getCover());
                }
                dto.setTitle(result.getName());
                eventList.add(dto);
            }
            pageResult.setTotalCount(marketingEventsBriefResults.getTotalCount());
        }

        configMarketingEventData(arg.getEa(), eventList);

        pageResult.setPageNum(arg.getPageNo());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(eventList);

        return Result.newSuccess(pageResult);
    }

    private void configMarketingEventData(String ea, List<MultiVenueMarketingEventListResult> eventList) {
        if (CollectionUtils.isEmpty(eventList)){
            return;
        }

        Map<String, MultiVenueMarketingEventListResult> enntityMap = eventList.stream().filter(event -> StringUtils.isNotBlank(event.getEventType())).
                collect(Collectors.toMap(MultiVenueMarketingEventListResult::getMarketingEventId, v -> v, (v1, v2) -> v1));
        if (enntityMap.isEmpty()){
            return;
        }
        List<String> nPathList = enntityMap.values().stream().filter(o->CollectionUtils.isNotEmpty(o.getCover())).map(o -> (String)o.getCover().get(0).get("path")).collect(Collectors.toList());
        nPathList.add(defaultCoverPath);
        Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(nPathList, ea, false);

        for (MultiVenueMarketingEventListResult listActivityResult : eventList){
            MultiVenueMarketingEventListResult activityEntity = enntityMap.get(listActivityResult.getMarketingEventId());
            if (activityEntity != null) {
                if (CollectionUtils.isNotEmpty(activityEntity.getCover())) {
                    listActivityResult.setCoverUrl(urlMap.get((String)activityEntity.getCover().get(0).get("path")));
                }else {
                    listActivityResult.setCoverUrl(urlMap.get(defaultCoverPath));
                }
            }
        }
    }

    private Result<Void> syncCampaignMember(String ea, List<String> subMarketingEventIds, List<String> campaignIds, boolean auto) {
        List<CampaignMergeDataEntity> entities = buildSubCampaignMergeDataEntity(ea, subMarketingEventIds, campaignIds);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTSERVICEIMPL_712));
        }

        entities.stream().forEach(campaignMergeDataEntity -> {
            Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity, auto);
            String campaignMemberId = crmV2Manager.addCampaignMembersObjWithoutLock(ea, dataMap, campaignMergeDataEntity.getBindCrmObjectType(), campaignMergeDataEntity.getBindCrmObjectId(), campaignMergeDataEntity.getMarketingEventId());
            if (campaignMemberId != null) {
                campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberId);
                //campaignMergeDataManager.addCampaignDataOnlyUnLock(campaignMergeDataEntity);
                log.info("MarketingEventServiceImpl -> syncCampaignMember success, data:{}", campaignMergeDataEntity);
            }
        });
        return Result.newSuccess();
    }

    private Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity, boolean auto) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("MarketingEventServiceImpl -> campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        if (auto) {
            autoCampaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity, dataMap);
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put("name", name);
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }

    /**
     * 活动成员同步至其他市场活动：
     * 1、需清空【邀约状态】、【参会码】、【签到状态】、【签到时间】、【观看状态】、【回放状态】、【互动状态】、【互动次数】、【观看时长】、【回放时长】、【支付金额】值；
     * 2、若同步至非会议市场活动，需清空【审核状态】值；若同步至会议市场活动，需判断同步的会议活动是否开启审核，开启审核则状态为【待审核】，不开启则为【已审核】。
     * 3、活动成员上自定义字段值自动同步至其他活动。
     *
     * @param ea
     * @param campaignMergeDataEntity
     * @param dataMap
     */
    private void autoCampaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity, Map<String, Object> dataMap) {
        try {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
            dataMap.putAll(objectData);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
                FieldDescribe fieldDescribe = entry.getValue();
                if ("system".equals(fieldDescribe.getDefineType())) {
                    dataMap.remove(fieldDescribe.getApiName());
                }
            }
            dataMap.remove("invitation_status");
            dataMap.remove("participants_passcode");
            dataMap.remove("sign_in_status");
            dataMap.remove("signed_time");
            dataMap.remove("marketing_watch_stauts");
            dataMap.remove("marketing_playback_status");
            dataMap.remove("marketing_interaction_status");
            dataMap.remove("marketing_interaction_frequency");
            dataMap.remove("marketing_watch_duration");
            dataMap.remove("marketing_playback_duration");
            dataMap.remove("pay_total_amount");
            ObjectData marketingEventObj = crmV2Manager.getDetail(ea, -10000, CrmApiNameEnum.MarketingEventObj.getApiName(), campaignMergeDataEntity.getMarketingEventId());
            if (!MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(marketingEventObj.get(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()))) {
                dataMap.remove("approval_status");
            } else {
                ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(campaignMergeDataEntity.getMarketingEventId(), ea);
                Boolean enrollReview = activityEntity.getEnrollReview();
                if (null != enrollReview && enrollReview) {
                    dataMap.put("approval_status", "processing");
                } else {
                    dataMap.put("approval_status", "approved");
                }
            }
        } catch (Exception e) {
            log.warn("autoCampaignMergeDataEntityToCampaignMergeObjMap error", e);
        }
    }

    /**
     * 构建子活动活动成员对象
     *
     * @param ea
     * @param subMarketingEventIds
     * @param campaignIds
     * @return
     */
    private List<CampaignMergeDataEntity> buildSubCampaignMergeDataEntity(String ea, List<String> subMarketingEventIds, List<String> campaignIds) {
        // 查询父级活动的活动成员
        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataManager.listByIds(ea, campaignIds);
        if (CollectionUtils.isEmpty(campaignMergeDataEntities)) {
            return Lists.newArrayList();
        }

        List<CampaignMergeDataEntity> subCampaignMergeDataEntities = Lists.newArrayList();
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntities) {
            /*String bindCrmObjectId = campaignMergeDataEntity.getBindCrmObjectId();
            Integer bindCrmObjectType = campaignMergeDataEntity.getBindCrmObjectType();
            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
            if (campaignMergeDataObjectTypeEnum == null) {
                log.info("MarketingEventServiceImpl -> buildSubCampaignMergeDataEntity bindCrmObject failed, bindCrmObjectId:{}, bindCrmObjectType:{}", bindCrmObjectId, bindCrmObjectType);
                continue;
            }

            ObjectData objectData = null;
            try {
                objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
            } catch (Exception e) {
                log.warn("MarketingEventServiceImpl -> buildSubCampaignMergeDataEntity error e:{}", e);
            }
            if (objectData == null) {
                log.info("MarketingEventServiceImpl -> buildSubCampaignMergeDataEntity objectData is null, bindCrmObjectId:{}, bindCrmObjectType:{}", bindCrmObjectId, bindCrmObjectType);
                continue;
            }*/

            if (StringUtils.isBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                return null;
            }

            List<CampaignMergeDataEntity> tempSubList = Lists.newArrayList();
            for (String subMarketingEventId : subMarketingEventIds) {
                CampaignMergeDataEntity subCampaignMergeDataEntity = BeanUtil.copy(campaignMergeDataEntity, CampaignMergeDataEntity.class);
                //String phone = campaignMergeDataManager.getPhoneByObject(objectData);
                //String name = objectData.getName();
                String campaignId = UUIDUtil.getUUID();
                subCampaignMergeDataEntity.setId(campaignId);
                subCampaignMergeDataEntity.setMarketingEventId(subMarketingEventId);
                subCampaignMergeDataEntity.setCreateTime(new Date());
                subCampaignMergeDataEntity.setUpdateTime(new Date());
                subCampaignMergeDataEntity.setAddCampaignMember(true);
                //subCampaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
                //subCampaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
                //subCampaignMergeDataEntity.setName(name);
                //subCampaignMergeDataEntity.setPhone(phone);
                //subCampaignMergeDataEntity.setEa(ea);
                //subCampaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
                //subCampaignMergeDataEntity.setOuterUserId(campaignMergeDataEntity.getOuterUserId());
                //subCampaignMergeDataEntity.setInviteStatus(campaignMergeDataEntity.getInviteStatus());
                //subCampaignMergeDataEntity.setTotalAmount(campaignMergeDataEntity.getTotalAmount());
                tempSubList.add(subCampaignMergeDataEntity);
            }

            subCampaignMergeDataEntities.addAll(tempSubList);
        }
        return subCampaignMergeDataEntities;
    }

    @Override
    public Result<MarketingEventDetailResult> getMarketingEventsDetail(GetMarketingEventDetailArg arg) {
        // 这里添加获取市场活动详情的逻辑
        MarketingEventDetailResult result = new MarketingEventDetailResult();
        ObjectData marketingEventObj = crmV2Manager.getDetail(arg.getEa(), -10000, CrmApiNameEnum.MarketingEventObj.getApiName(), arg.getMarketingEventId());
        if (Objects.isNull(marketingEventObj)) {
            log.info("市场活动不存在, ea: {} subMarketingEventId: {}",arg.getEa(), arg.getMarketingEventId());
            return Result.newSuccess();
        }
        com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventVO = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(marketingEventObj);
        result.setName(marketingEventVO.getName());
        result.setStartTime(marketingEventVO.getBeginTime());
        result.setEndTime(marketingEventVO.getEndTime());
        result.setEventForm(marketingEventVO.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()));
        String eventForm = marketingEventVO.getString("event_form");
        if (MarketingEventFormEnum.LIVE_MARKETING.getValue().equals(eventForm)) {
            MarketingLiveEntity live = marketingLiveDAO.getLiveMarketingDetail(arg.getMarketingEventId(), eieaConverter.enterpriseAccountToId(arg.getEa()));
            result.setDescription(live.getDescription());
            result.setCover(fileV2Manager.getUrlByPath(arg.getEa(), live.getCover()));
            result.setLiveUrl(live.getShortViewUrl());
            result.setAssociatedAccountId(live.getAssociatedAccountId());
        } else if (MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(eventForm)) {
            ActivityEntity conference = conferenceDAO.getConferenceByMarketingEventId(arg.getMarketingEventId(), arg.getEa());
            result.setDescription(conferenceManager.getConferenceDetailsByPath(conference.getConferenceDetails()));
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), conference.getId(),conference.getEa());
            if (photoEntity != null) {
                result.setCover(fileV2Manager.getUrlByPath(arg.getEa(), photoEntity.getPath()));
            }
            result.setMapLocation(conference.getMapLocation());
            result.setMapAddress(conference.getMapAddress());
            result.setLocation(conference.getLocation());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> createEventWorkspaceObj(GetMarketingEventDetailArg arg) {
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("marketing_event_id", OperatorConstants.EQ, Lists.newArrayList(arg.getMarketingEventId()));
        List<String> selectFieldList = Lists.newArrayList("_id");
        List<ObjectData> objectDataList =  crmV2Manager.getAllObjByQueryArg(arg.getEa(), CrmObjectApiNameEnum.VM_WORKSPACE_OBJ.getName(), selectFieldList, query);
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            return Result.newSuccess(objectDataList.get(0).getId());
        }

        ObjectData marketingEventObj = crmV2Manager.getDetail(arg.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), arg.getMarketingEventId());
        if (Objects.isNull(marketingEventObj)) {
            log.info("市场活动不存在, ea: {} subMarketingEventId: {}",arg.getEa(), arg.getMarketingEventId());
            return Result.newSuccess();
        }
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("name",marketingEventObj.getName());
        dataMap.put("summary",marketingEventObj.getString("summary"));
        dataMap.put("icon","https://a2.fspage.com/FSR/weex/avatar/marketing_app/images/workspace-icons/4.png");
        dataMap.put("marketing_event_id", arg.getMarketingEventId());
        dataMap.put("works_space_type", "campaign");

        Integer owner = arg.getUserId() < 100000000?arg.getUserId(): null;
        if(owner!=null){
            dataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
        }
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = crmV2Manager.addObjectData(arg.getEa(), CrmObjectApiNameEnum.VM_WORKSPACE_OBJ.getName(), owner, dataMap);
        if (actionAddResultResult != null && actionAddResultResult.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
            String workSpaceId = actionAddResultResult.getData().getObjectData().getId();
            HashMap<String, Object> userDataMap = new HashMap<>();
            userDataMap.put("user_id",String.valueOf(owner));
            userDataMap.put("workspace_id", workSpaceId);
            if(owner!=null){
                userDataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
            }
            crmV2Manager.addObjectData(arg.getEa(), CrmObjectApiNameEnum.VM_WORKSPACE_USER_OBJ.getName(), owner, userDataMap);

            SyncWorkspaceAssetArg assetArg = new SyncWorkspaceAssetArg();
            assetArg.setEa(arg.getEa());
            assetArg.setFsUserId(arg.getUserId());
            assetArg.setMarketingEventId(arg.getMarketingEventId());

            assetArg.setWorkspaceId(workSpaceId);
            //异步同步工作资产
            syncEventWorkspaceAsset(assetArg);
            return Result.newSuccess(workSpaceId);
        } else {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<String> syncEventWorkspaceAsset(SyncWorkspaceAssetArg arg) {
        //同步运营任务到工作空间资产
        log.info("开始同步活动物料到工作空间资产, ea: {}, marketingEventId: {}, workspaceId: {}",
                arg.getEa(), arg.getMarketingEventId(), arg.getWorkspaceId());

        // 异步执行同步任务
        ThreadPoolUtils.execute(() -> {
            try {
                doSyncEventWorkspaceAsset(arg);
            } catch (Exception e) {
                log.error("同步活动物料到工作空间资产失败, ea: {}, marketingEventId: {}",
                        arg.getEa(), arg.getMarketingEventId(), e);
                // 确保在异常情况下也更新 Redis 状态为 FAILED，并推送 SSE 事件
                setWorkspaceSyncStatusInRedis(arg.getWorkspaceId(), "FAILED");
                sendSyncStatusToAllServices(arg.getWorkspaceId(), "FAILED");
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess("同步任务已提交");
    }

    /**
     * 执行同步活动物料到工作空间资产
     */
    private void doSyncEventWorkspaceAsset(SyncWorkspaceAssetArg arg) {
        String ea = arg.getEa();
        String marketingEventId = arg.getMarketingEventId();
        String workspaceId = arg.getWorkspaceId();
        Integer fsUserId = arg.getFsUserId();

        // 参数校验
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(marketingEventId)) {
            log.warn("同步活动物料参数不完整, ea: {}, marketingEventId: {}", ea, marketingEventId);
            setWorkspaceSyncStatusInRedis(workspaceId, "FAILED");
            sendSyncStatusToAllServices(workspaceId, "FAILED");
            return;
        }

        // 获取分布式锁，防止重复同步
        String lockKey = "mark_syncEventWorkspaceAsset_" + ea + "_" + marketingEventId + "_" + workspaceId;
        boolean lockAcquired = redisManager.lock(lockKey, 300); // 5分钟锁
        if (!lockAcquired) {
            log.warn("获取同步锁失败，可能正在同步中, ea: {}, marketingEventId: {}, workspaceId: {}",
                    ea, marketingEventId, workspaceId);
            return;
        }

        try {
            setWorkspaceSyncStatusInRedis(workspaceId, "SYNCING");
            sendSyncStatusToAllServices(workspaceId, "SYNCING");
            log.info("执行同步活动物料, ea: {}, marketingEventId: {}, workspaceId: {}", ea, marketingEventId, workspaceId);

            // 1. 同步看板数据
            syncBoardAssets(ea, marketingEventId, workspaceId, fsUserId);

            // 2. 同步微页面数据
            syncHexagonAssets(ea, marketingEventId, workspaceId, fsUserId);

            // 3. 同步文章数据
            syncArticleAssets(ea, marketingEventId, workspaceId, fsUserId);

            // 4. 同步SOP数据
            syncSopAssets(ea, marketingEventId, workspaceId, fsUserId);

            // 5. 同步海报数据
            syncQrPosterAssets(ea, marketingEventId, workspaceId, fsUserId);

            log.info("完成同步活动物料, ea: {}, marketingEventId: {}", ea, marketingEventId);
            setWorkspaceSyncStatusInRedis(workspaceId, "COMPLETED");
            sendSyncStatusToAllServices(workspaceId, "COMPLETED");
        } catch (Exception e) {
            log.error("执行同步活动物料失败, ea: {}, marketingEventId: {}, workspaceId: {}",
                    ea, marketingEventId, workspaceId, e);
            // 5. 同步失败后，更新 Redis 状态为 FAILED，并推送 SSE 事件
            setWorkspaceSyncStatusInRedis(workspaceId, "FAILED");
            sendSyncStatusToAllServices(workspaceId, "FAILED"); // 推送 FAILED 状态
            throw e; // 重新抛出异常，让外层ThreadPoolUtils的execute捕获并记录
        }finally {
            // 释放分布式锁
            redisManager.unLock(lockKey);
        }
    }
    /**
     * 在Redis中设置工作空间同步状态。
     *
     * @param workspaceId 工作空间ID。
     * @param newStatus   新的同步状态 (INITIAL, SYNCING, COMPLETED, FAILED)。
     */
    private void setWorkspaceSyncStatusInRedis(String workspaceId, String newStatus) {
        if (StringUtils.isBlank(workspaceId)) {
            log.warn("无法设置工作空间状态到Redis，workspaceId 为空. newStatus: {}", newStatus);
            return;
        }
        String redisKey = WORKSPACE_SYNC_STATUS_PREFIX + workspaceId;
        try {
            redisManager.set(redisKey, newStatus, WORKSPACE_SYNC_STATUS_EXPIRE_SECONDS);
            log.info("工作空间同步状态已在Redis中更新, workspaceId: {}, newStatus: {}", workspaceId, newStatus);
        } catch (Exception e) {
            log.error("设置工作空间同步状态到Redis时发生异常, workspaceId: {}, newStatus: {}", workspaceId, newStatus, e);
        }
    }

    /**
     * 从Redis获取工作空间同步状态。
     *
     * @param workspaceId 工作空间ID。
     * @return 包含同步状态字符串的结果，如果不存在则返回 "UNKNOWN"。
     */
    @Override
    public Result<String> getWorkspaceSyncStatusFromRedis(String workspaceId) {
        if (StringUtils.isBlank(workspaceId)) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR, "工作空间ID不能为空");
        }
        String redisKey = WORKSPACE_SYNC_STATUS_PREFIX + workspaceId;
        try {
            String status = redisManager.get(redisKey);
            if (status == null) {
                // 如果Redis中没有找到状态，可能意味着：
                // 1. 同步从未开始或工作空间ID错误
                // 2. 状态已过期被Redis清理
                // 3. 同步已完成且状态被移除（如果业务逻辑允许完成时移除）
                return Result.newSuccess("UNKNOWN");
            }
            return Result.newSuccess(status);
        } catch (Exception e) {
            log.error("从Redis获取工作空间同步状态时发生异常, workspaceId: {}", workspaceId, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR, "获取同步状态失败");
        }
    }

    /**
     * 同步看板资产
     */
    private void syncBoardAssets(String ea, String marketingEventId, String workspaceId, Integer fsUserId) {
        try {
            log.info("开始同步看板资产, ea: {}, marketingEventId: {}", ea, marketingEventId);

            // 获取活动关联的看板ID
            String boardId = boardDao.getSopBoardIdByObjectId(ea, marketingEventId);
            if (StringUtils.isBlank(boardId)) {
                log.info("活动未关联看板, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }

            // 获取看板详情
            Result<BoardResult> boardResult = boardService.getBoardDetail(ea, fsUserId, boardId);
            if (boardResult == null) {
                log.warn("获取看板详情失败, ea: {}, boardId: {}", ea, boardId);
                return;
            }

            Integer owner = fsUserId < 100000000?fsUserId: null;

            // 转换看板数据为运营任务格式
            Map<String, BoardColumnData> boardData = boardResult.getData().toBoardColumnFormat();

            // 构建资产数据
            Map<String, Object> assetDataMap = new HashMap<>();
            assetDataMap.put("name", boardResult.getData().getName()); // 资产名称
            assetDataMap.put("workspace_id", workspaceId); // 工作空间ID
            assetDataMap.put("target_type", "kanban"); // 类型：看板
            assetDataMap.put("asset_data", JSON.toJSONString(boardData)); // 资产数据JSON
            assetDataMap.put("target_id", boardId);
            if(owner!=null){
                assetDataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
            }
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("workspace_id", workspaceId); // 工作空间ID
            paramMap.put("target_type", "kanban"); // 类型：看板
            paramMap.put("target_id", boardId);
            // 同步到CRM工作空间资产对象
            com.fxiaoke.crmrestapi.common.result.Result result = crmV2Manager.addOrUpdateObjectDataAndAssetData(ea, CrmObjectApiNameEnum.VM_WORKSPACE_ASSET_OBJ.getName(), owner,paramMap, assetDataMap);

            if (result != null && result.isSuccess()) {
                log.info("看板资产同步成功, ea: {}, boardId: {}, assetId: {}",
                        ea, boardId, result.getData());
            } else {
                log.error("看板资产同步失败, ea: {}, boardId: {}, error: {}",
                        ea, boardId, result != null ? result.getMessage() : "unknown");
            }

        } catch (Exception e) {
            log.error("同步看板资产异常, ea: {}, marketingEventId: {}", ea, marketingEventId, e);
        }
    }

    /**
     * 同步微页面资产
     */
    private void syncHexagonAssets(String ea, String marketingEventId, String workspaceId, Integer fsUserId) {
        try {
            log.info("开始同步微页面资产, ea: {}, marketingEventId: {}", ea, marketingEventId);

            // 获取活动关联的微页面
            List<ContentMarketingEventMaterialRelationEntity> hexagonRelations =
                contentMarketingEventMaterialRelationDAO.getHexagonContentMarketingRelation(
                    ea, marketingEventId, ObjectTypeEnum.HEXAGON_SITE.getType(), null, 0, 1000);

            if (CollectionUtils.isEmpty(hexagonRelations)) {
                log.info("活动未关联微页面, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }

            // 批量获取微页面详情
            List<String> hexagonIds = hexagonRelations.stream()
                .map(ContentMarketingEventMaterialRelationEntity::getObjectId)
                .collect(Collectors.toList());

            List<HexagonSiteEntity> hexagonSites = hexagonSiteDAO.getByIds(hexagonIds);

            for (HexagonSiteEntity hexagonSite : hexagonSites) {
                try {
//                    // 构建微页面资产数据
//                    Map<String, Object> hexagonData = new HashMap<>();
//                    hexagonData.put("id", hexagonSite.getId());
//                    hexagonData.put("name", hexagonSite.getName());
//
//                    hexagonData.put("create_time", hexagonSite.getCreateTime());
//
//                    // 构建资产数据
//                    Map<String, Object> assetDataMap = new HashMap<>();
//                    assetDataMap.put("name", hexagonSite.getName()); // 资产名称
//                    assetDataMap.put("workspace_id", workspaceId); // 工作空间ID
//                    assetDataMap.put("target_type", "micropage"); // 类型：微页面
//                    assetDataMap.put("asset_data", JSON.toJSONString(hexagonData)); // 资产数据JSON
//                    assetDataMap.put("create_time", System.currentTimeMillis());
//                    assetDataMap.put("update_time", System.currentTimeMillis());
//                    Integer owner = fsUserId < 100000000?fsUserId: null;
//                    if(owner!=null){
//                        assetDataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
//                    }
//                    Map<String, String> paramMap = new HashMap<>();
//                    paramMap.put("workspace_id", workspaceId); // 工作空间ID
//                    paramMap.put("target_type", "poster"); // 类型：看板
//                    paramMap.put("target_id", hexagonSite.getId());
//                    // 同步到CRM工作空间资产对象
//                    com.fxiaoke.crmrestapi.common.result.Result result = crmV2Manager.addOrUpdateObjectDataAndAssetData(ea, CrmObjectApiNameEnum.VM_WORK_SPACE_ASSET_OBJ.getName(), owner,paramMap, assetDataMap);
//
//
//                    if (result != null && result.isSuccess()) {
//                        log.info("微页面资产同步成功, ea: {}, hexagonId: {}, data: {}",
//                                ea, hexagonSite.getId(), result.getData());
//                    } else {
//                        log.error("微页面资产同步失败, ea: {}, hexagonId: {}, error: {}",
//                                ea, hexagonSite.getId(), result != null ? result.getMessage() : "unknown");
//                    }

                } catch (Exception e) {
                    log.error("同步单个微页面资产异常, ea: {}, hexagonId: {}", ea, hexagonSite.getId(), e);
                }
            }

        } catch (Exception e) {
            log.error("同步微页面资产异常, ea: {}, marketingEventId: {}", ea, marketingEventId, e);
        }
    }

    /**
     * 同步文章资产
     */
    private void syncArticleAssets(String ea, String marketingEventId, String workspaceId, Integer fsUserId) {
        try {
            log.info("开始同步文章资产, ea: {}, marketingEventId: {}", ea, marketingEventId);

            // 获取活动关联的文章
            List<ContentMarketingEventMaterialRelationEntity> articleRelations =
                contentMarketingEventMaterialRelationDAO.getArticleContentMarketingRelation(
                    ea, marketingEventId, ObjectTypeEnum.ARTICLE.getType(), null, 0, 1000);

            if (CollectionUtils.isEmpty(articleRelations)) {
                log.info("活动未关联文章, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }

            // 批量获取文章详情
            List<String> articleIds = articleRelations.stream()
                .map(ContentMarketingEventMaterialRelationEntity::getObjectId)
                .collect(Collectors.toList());

            List<ArticleEntity> articles = articleDAO.getByIds(articleIds);

            for (ArticleEntity article : articles) {
                try {
                    // 构建文章资产数据
                    ArticleAssetData articleAssetData = BeanUtil.copy(article, ArticleAssetData.class);
                    String apath = article.getArticlePath();
                    if(StringUtils.isNotBlank(apath)){
                        byte[] bytes = fileV2Manager.downloadAFile(apath, ea);
                        String content = bytes == null ? "" : new String(bytes);
                        articleAssetData.setContent(content);
                    }

                    // 构建资产数据
                    Map<String, Object> assetDataMap = new HashMap<>();
                    assetDataMap.put("name", article.getTitle()); // 资产名称
                    assetDataMap.put("workspace_id", workspaceId); // 工作空间ID
                    assetDataMap.put("target_type", "article"); // 类型：文章
                    assetDataMap.put("asset_data", JSON.toJSONString(articleAssetData)); // 资产数据JSON
                    assetDataMap.put("target_id", article.getId());

                    Integer owner = fsUserId < 100000000?fsUserId: null;
                    if(owner!=null){
                        assetDataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
                    }
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("workspace_id", workspaceId); // 工作空间ID
                    paramMap.put("target_type", "poster"); // 类型：看板
                    paramMap.put("target_id", article.getId());

                    // 同步到CRM工作空间资产对象
                    com.fxiaoke.crmrestapi.common.result.Result result = crmV2Manager.addOrUpdateObjectDataAndAssetData(ea, CrmObjectApiNameEnum.VM_WORKSPACE_ASSET_OBJ.getName(), owner,paramMap, assetDataMap);


                    if (result != null && result.isSuccess()) {
                        log.info("文章资产同步成功, ea: {}, articleId: {}, data: {}",
                                ea, article.getId(), result.getData());
                    } else {
                        log.error("文章资产同步失败, ea: {}, articleId: {}, error: {}",
                                ea, article.getId(), result != null ? result.getMessage() : "unknown");
                    }

                } catch (Exception e) {
                    log.error("同步单个文章资产异常, ea: {}, articleId: {}", ea, article.getId(), e);
                }
            }

        } catch (Exception e) {
            log.error("同步文章资产异常, ea: {}, marketingEventId: {}", ea, marketingEventId, e);
        }
    }

    /**
     * 同步SOP资产
     */
    private void syncSopAssets(String ea, String marketingEventId, String workspaceId, Integer fsUserId) {
        try {
            log.info("开始同步SOP资产, ea: {}, marketingEventId: {}", ea, marketingEventId);

            String sceneTargetId = marketingEventId;
            String sceneType = "marketing_event";
            MarketingLiveEntity live = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
            if(live!=null){
                sceneTargetId = live.getId();
                sceneType = "live";
            }else {
                ActivityEntity activity = activityDAO.getActivityEntityByMarketingEventId(ea, marketingEventId);
                if (activity != null) {
                    sceneTargetId = activity.getId();
                    sceneType = "conference";
                }
            }

            MarketingSceneEntity marketingSceneInDB = marketingSceneDao.getMarketingSceneByTargetId(ea, sceneType, sceneTargetId);
            if(marketingSceneInDB == null){
                log.info("营销场景不存在, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }
            ListSceneTriggerVO triggerVO = new ListSceneTriggerVO();
            triggerVO.setEa(ea);
            triggerVO.setId(marketingSceneInDB.getId());
            triggerVO.setType(2);
            triggerVO.setPageNum(1);
            triggerVO.setPageSize(1000);
            triggerVO.setUserId(fsUserId);
            Result<PageResult<SceneTriggerVO>> pageResultResult = marketingSceneService.listSceneTriggerBySceneId(triggerVO);

            if (pageResultResult == null || !pageResultResult.isSuccess() || CollectionUtils.isEmpty(pageResultResult.getData().getResult())) {
                log.info("活动未关联SOP, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }


            for (SceneTriggerVO sopBoard : pageResultResult.getData().getResult()) {
                try {
                    // 获取SOP看板详情


                    // 转换SOP数据为运营任务格式


                    // 构建资产数据

                    // 同步到CRM工作空间资产对象



                } catch (Exception e) {
                    log.error("同步单个SOP资产异常, ea: {}, sopBoardId: {}", ea, sopBoard.getId(), e);
                }
            }

        } catch (Exception e) {
            log.error("同步SOP资产异常, ea: {}, marketingEventId: {}", ea, marketingEventId, e);
        }
    }

    /**
     * 同步海报资产
     */
    private void syncQrPosterAssets(String ea, String marketingEventId, String workspaceId, Integer fsUserId) {
        try {
            log.info("开始同步海报资产, ea: {}, marketingEventId: {}", ea, marketingEventId);

            // 获取活动关联的海报
            List<ContentMarketingEventMaterialRelationEntity> posterRelations =
                contentMarketingEventMaterialRelationDAO.getQrcodeContentMarketingRelation(
                    ea, marketingEventId, ObjectTypeEnum.QR_POSTER.getType(), null, 0, 1000);

            if (CollectionUtils.isEmpty(posterRelations)) {
                log.info("活动未关联海报, ea: {}, marketingEventId: {}", ea, marketingEventId);
                return;
            }

            // 批量获取海报详情
            List<String> posterIds = posterRelations.stream()
                .map(ContentMarketingEventMaterialRelationEntity::getObjectId)
                .collect(Collectors.toList());

            List<QRPosterEntity> posters = qrPosterDAO.getByIds(posterIds);

            for (QRPosterEntity poster : posters) {
                try {
                    QrPosterAssetsData data = new QrPosterAssetsData();
                    data.setMarketingEventId(marketingEventId);
                    data.setApath(poster.getApath());
                    data.setBgApath(poster.getBgApath());
                    data.setUrl(fileV2Manager.getUrlByPath(ea,poster.getApath()));
                    data.setBgUrl(fileV2Manager.getUrlByPath(ea,poster.getBgApath()));
                    data.setQrStyle(poster.getQrStyle());
                    data.setPosterStyle(poster.getPosterStyle());
                    data.setQrStyle(poster.getQrStyle());
                    data.setForwardType(poster.getForwardType());
                    data.setTargetId(poster.getTargetId());
                    data.setType(poster.getType());

                    // 构建资产数据
                    Map<String, Object> assetDataMap = new HashMap<>();
                    assetDataMap.put("name", poster.getTitle()); // 资产名称
                    assetDataMap.put("workspace_id", workspaceId); // 工作空间ID
                    assetDataMap.put("target_type", "poster"); // 类型：海报
                    assetDataMap.put("asset_data", JSON.toJSONString(data)); // 资产数据JSON
                    assetDataMap.put("target_id", poster.getId());
                    Integer owner = fsUserId < 100000000?fsUserId: null;
                    if(owner!=null){
                        assetDataMap.put("created_by",Lists.newArrayList(String.valueOf(owner)));
                    }
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("workspace_id", workspaceId); // 工作空间ID
                    paramMap.put("target_type", "poster"); // 类型：看板
                    paramMap.put("target_id", poster.getId());

                    // 同步到CRM工作空间资产对象
                    com.fxiaoke.crmrestapi.common.result.Result result = crmV2Manager.addOrUpdateObjectDataAndAssetData(ea, CrmObjectApiNameEnum.VM_WORKSPACE_ASSET_OBJ.getName(), owner,paramMap, assetDataMap);

                    if (result != null && result.isSuccess()) {
                        log.info("海报资产同步成功, ea: {}, boardId: {}, assetId: {}",
                                ea,  poster.getId(), result.getData());
                    } else {
                        log.error("海报资产同步失败, ea: {}, boardId: {}, error: {}",
                                ea,  poster.getId(), result != null ? result.getMessage() : "unknown");
                    }


                } catch (Exception e) {
                    log.error("同步单个海报资产异常, ea: {}, posterId: {}", ea, poster.getId(), e);
                }
            }

        } catch (Exception e) {
            log.error("同步海报资产异常, ea: {}, marketingEventId: {}", ea, marketingEventId, e);
        }
    }

    /**
     * 向所有SSE服务发送同步状态
     * 确保多节点部署下的消息推送
     */
    private void sendSyncStatusToAllServices(String workspaceId, String status) {
        try {
            // 发送到原有服务（本地连接）
            workspaceSyncSseServiceImpl.sendSyncStatus(workspaceId, status);
        } catch (Exception e) {
            log.error("发送状态到原有SSE服务失败, workspaceId: {}, status: {}", workspaceId, status, e);
        }

        try {
            // 发送到V2服务（跨节点支持）
            if (workspaceSyncSseServiceV2 instanceof com.facishare.marketing.provider.service.WorkspaceSyncSseServiceV2) {
                com.facishare.marketing.provider.service.WorkspaceSyncSseServiceV2 serviceV2 =
                        (com.facishare.marketing.provider.service.WorkspaceSyncSseServiceV2) workspaceSyncSseServiceV2;
                serviceV2.sendSyncStatus(workspaceId, status);
            }
        } catch (Exception e) {
            log.error("发送状态到V2 SSE服务失败, workspaceId: {}, status: {}", workspaceId, status, e);
        }
    }
}