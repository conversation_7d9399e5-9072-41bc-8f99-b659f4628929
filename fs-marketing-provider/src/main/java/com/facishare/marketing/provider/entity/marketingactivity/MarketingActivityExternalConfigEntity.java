package com.facishare.marketing.provider.entity.marketingactivity;

import com.facishare.marketing.api.vo.MarketingActivityGroupSenderVO;
import com.facishare.marketing.api.vo.MarketingActivityNoticeSendVO;
import com.facishare.marketing.api.vo.WeChatServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.util.ConvertUtil;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/20.
 */
@Data
public class MarketingActivityExternalConfigEntity implements Serializable {
    private String id;
    private String marketingActivityId;
    private ExternalConfig externalConfig;
    private int associateIdType;
    private String associateId;
    private String ea;
    /** 1 企业 2  个人 */
    private Integer marketingActivityType;
    private Date createTime;
    //市场活动ID
    private String marketingEventId;
    private String isNeedAudit = "false";

    public void fillExternalConfigEntity(WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO) {
        this.externalConfig = new ExternalConfig(weChatServiceMarketingActivityVO);
    }

    public void fillExternalConfigEntity(WeChatTemplateMessageData weChatTemplateMessageVO) {
        this.externalConfig = new ExternalConfig(ConvertUtil.convert(weChatTemplateMessageVO));
    }

    public void fillExternalConfigEntity(MarketingActivityNoticeSendVO allUserMarketingVO) {
        this.externalConfig = new ExternalConfig(allUserMarketingVO);
    }

    public void fillExternalConfigEntity(MarketingActivityGroupSenderVO marketingActivityGroupSenderVO) {
        this.externalConfig = new ExternalConfig(marketingActivityGroupSenderVO);
    }
    public void fillExternalConfigEntity(QywxGroupSendMessageVO qywxGroupSendMessageVO) {
        this.externalConfig = new ExternalConfig(qywxGroupSendMessageVO);
    }

    public void fillExternalConfigEntity(MailServiceMarketingActivityVO mailServiceMarketingActivityVO) {
        this.externalConfig = new ExternalConfig(mailServiceMarketingActivityVO);
    }
}
