package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.AddBoardArg;
import com.facishare.marketing.api.arg.AddBoardCardArg;
import com.facishare.marketing.api.arg.AddBoardCardTaskArg;
import com.facishare.marketing.api.arg.ListBoardCardActivityArg;
import com.facishare.marketing.api.arg.OfficeMessageArg;
import com.facishare.marketing.api.data.BoardStatisticData;
import com.facishare.marketing.api.data.BoardTemplateData;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.BoardCardActivityResult;
import com.facishare.marketing.api.result.BoardCardResult;
import com.facishare.marketing.api.result.BoardResult;
import com.facishare.marketing.api.vo.EnterpriseBoardTemplateVo;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.typehandlers.value.BoardCardActivityContentData;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO;
import com.facishare.marketing.provider.dao.BoardCardActivityDao;
import com.facishare.marketing.provider.dao.BoardCardDao;
import com.facishare.marketing.provider.dao.BoardCardListDao;
import com.facishare.marketing.provider.dao.BoardCardTaskDao;
import com.facishare.marketing.provider.dao.BoardDao;
import com.facishare.marketing.provider.dao.BoardToMarketingUserGroupRelationDao;
import com.facishare.marketing.provider.dao.BoardUserDao;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.DistributePlanDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.TriggerSnapshotDao;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.entity.BoardCardActivityEntity;
import com.facishare.marketing.provider.entity.BoardCardEntity;
import com.facishare.marketing.provider.entity.BoardCardListEntity;
import com.facishare.marketing.provider.entity.BoardCardTaskEntity;
import com.facishare.marketing.provider.entity.BoardEntity;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.innerData.MarketingActivityStatisticData;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/8/6 15:27
 * @Version 1.0
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class BoardManager {
    @Autowired
    private BoardCardActivityDao boardCardActivityDao;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private DistributorDao distributorDao;
    @Autowired
    private ClueDAO clueDao;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private BoardDao boardDao;
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardCardListDao boardCardListDao;
    @Autowired
    private BoardUserDao boardUserDao;
    @Autowired
    private BoardCardTaskDao boardCardTaskDao;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private EmployeeMsgSender employeeMsgSender;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private DistributePlanDao distributePlanDao;
    @Autowired
    private BoardToMarketingUserGroupRelationDao boardToMarketingUserGroupRelationDao;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;
    @Autowired
    private RedisManager redisManager;

    @ReloadableProperty("defaultUserBoard")
    private String defaultUserBoard;
    @ReloadableProperty("defaultUserBoard_en")
    private String defaultUserBoardEN;
    @ReloadableProperty("defaultEnterpriseBoard")
    private String defaultEnterpriseBoard;
    @ReloadableProperty("defaultEnterpriseBoard_en")
    private String defaultEnterpriseBoardEN;
    @Value("${host}")
    private String host;
    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    private final LoadingCache<String, Integer> EXTERNAL_USER_COUNT_CACHE = CacheBuilder.newBuilder().maximumSize(10000).expireAfterWrite(20, TimeUnit.MINUTES).build(new CacheLoader<String, Integer>() {
        @Override
        public Integer load(String ea) {
            return wechatWorkExternalUserObjManager.countAllExternalUser(ea);
        }
    });
    @Autowired
    private CrmMetadataManager crmMetadataManager;

    public List<EnterpriseBoardTemplateVo> listBoardTemplateData(){
        String redisKey = "boardSystemTemplate";
        String boardSystemTemplate = redisManager.get(redisKey);
        if (Strings.isNullOrEmpty(boardSystemTemplate)) {
            // Redis中没有，去DB里查
            String lockName = "boardSystemTemplateLock";
            if (!redisManager.lock(lockName, 2)) {
                // 没抢到锁，睡200ms重新开始
                try {
                    Thread.sleep(100);
                    return listBoardTemplateData();
                } catch (InterruptedException e) {
                    log.warn("exception:",  e);
                    return listBoardTemplateData();
                }
            }
            // 抢到锁，再次检查
            if (Strings.isNullOrEmpty(boardSystemTemplate = redisManager.get(redisKey))) {
                // 开始查DB
                List<BoardEntity> boardEntities = boardDao.listEnterpriseTemplateBoard("__SYSTEM");
                List<EnterpriseBoardTemplateVo> result = boardEntities.stream().map(boardEntity -> {
                    EnterpriseBoardTemplateVo vo = new EnterpriseBoardTemplateVo();
                    BeanUtils.copyProperties(boardEntity, vo);
                    vo.setCreatorName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
                    vo.setHaveAuthority(false);
                    return vo;
                }).collect(Collectors.toList());
                // 查完放回Redis中，设缓存有效期24h，后释放锁
                boardSystemTemplate = GsonUtil.toJson(result);
                redisManager.set(redisKey, 86400, boardSystemTemplate);
                boolean isUnLock = redisManager.unLock(lockName);
                // 解锁失败重试3次
                for (int i = 0; i < 3 && !isUnLock; i++) {
                    isUnLock = redisManager.unLock(lockName);
                }
                return result;
            }
            boolean isUnLock = redisManager.unLock(lockName);
            for (int i = 0; i < 3 && !isUnLock; i++) {
                isUnLock = redisManager.unLock(lockName);
            }
        }
        return JSON.parseArray(boardSystemTemplate, EnterpriseBoardTemplateVo.class);
    }

    public String initDefaultUserBoard(String ea, int fsUserId){
        String employeeName = fsAddressBookManager.getEmployeeNameById(ea, fsUserId);
//        String finalDefaultUserBoard = I18nUtil.getSuitedLangText(defaultUserBoard, defaultUserBoardEN);
        String finalDefaultUserBoard = null;
        String lang = marketingEventCommonSettingService.getLang(ea);
        int langType = StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
        if(langType == 1){
            finalDefaultUserBoard = defaultUserBoard;
        }else {
            finalDefaultUserBoard = defaultUserBoardEN;
        }
        String replacedDefaultUserBoard = finalDefaultUserBoard.replace("{fsUserId}", fsUserId + "").replace("{fsUserName}", employeeName);
        BoardTemplateData boardTemplateData = GsonUtil.fromJson(replacedDefaultUserBoard, BoardTemplateData.class);
        return addBoardByTemplateData(ea, fsUserId, boardTemplateData, true, true);
    }

    public String initDefaultEnterpriseBoard(String ea){
//        String finalEnterpriseBoard = I18nUtil.getSuitedLangText(defaultEnterpriseBoard, defaultEnterpriseBoardEN);
        String finalEnterpriseBoard = null;
        String lang = marketingEventCommonSettingService.getLang(ea);
        int langType = StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
        if(langType == 1){
            finalEnterpriseBoard = defaultEnterpriseBoard;
        }else {
            finalEnterpriseBoard = defaultEnterpriseBoardEN;
        }
        BoardTemplateData boardTemplateData = GsonUtil.fromJson(finalEnterpriseBoard, BoardTemplateData.class);
        return addBoardByTemplateData(ea, -10000, boardTemplateData, true, true);
    }

    /**
     * @param data 看板数据
     * @param createBySystem 是否是系统创建的
     * @param createBoardCardActivity 是否创建动态
     * @return 看板ID
     */
    public String addBoardByTemplateData(String ea, Integer fsUserId, BoardTemplateData data, boolean createBySystem, boolean createBoardCardActivity){
        AddBoardArg addBoardArg = new AddBoardArg();
        BeanUtils.copyProperties(data, addBoardArg);
        String boardId = this.addBoard(ea, fsUserId, addBoardArg);
        if (data.getMarketingUserGroupIds() != null){
            for (String marketingUserGroupId : data.getMarketingUserGroupIds()) {
                boardToMarketingUserGroupRelationDao.insertIgnore(ea, boardId, marketingUserGroupId);
            }
        }
//        for (BoardTemplateData.BoardCardListWithCardData boardCardList : data.getBoardCardLists()) {
//            String boardCardListId = this.addBoardCardList(ea, fsUserId, boardId, boardCardList.getName());
//            for (BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask boardCard : boardCardList.getBoardCards()) {
//                AddBoardCardArg addBoardCardArg = new AddBoardCardArg();
//                BeanUtils.copyProperties(boardCard, addBoardCardArg);
//                addBoardCardArg.setBoardCardListId(boardCardListId);
//                String boardCardId = this.addBoardCard(ea, fsUserId, boardId, addBoardCardArg, createBySystem);
//                for (BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask.TaskData task : boardCard.getTasks()) {
//                    AddBoardCardTaskArg addBoardCardTaskArg = new AddBoardCardTaskArg();
//                    BeanUtils.copyProperties(task, addBoardCardTaskArg);
//                    addBoardCardTaskArg.setBoardCardId(boardCardId);
//                    this.addBoardCardTask(ea, fsUserId, boardId, addBoardCardTaskArg, createBySystem);
//                }
//            }
//        }
        for (BoardTemplateData.BoardCardListWithCardData boardCardList : data.getBoardCardLists()) {
            String boardCardListId = this.addBoardCardList(ea, fsUserId, boardId, boardCardList.getName());
            List<AddBoardCardArg> boardCards = boardCardList.getBoardCards().stream().map(boardCard -> {
                AddBoardCardArg addBoardCardArg = new AddBoardCardArg();
                BeanUtils.copyProperties(boardCard, addBoardCardArg);
                addBoardCardArg.setBoardCardListId(boardCardListId);
                return addBoardCardArg;
            }).collect(Collectors.toList());
            // 批量插入批量排序
            List<BoardCardEntity> boardCardEntities = batchAddBoardCards(ea, fsUserId, boardId, boardCards, createBySystem, createBoardCardActivity);
            batchSortBoardCardToBoardCardList(ea, boardCardEntities);
            int index = 0;
            for (BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask boardCard : boardCardList.getBoardCards()) {
                String boardCardId = boardCardEntities.get(index).getId();
                for (BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask.TaskData task : boardCard.getTasks()) {
                    AddBoardCardTaskArg addBoardCardTaskArg = new AddBoardCardTaskArg();
                    BeanUtils.copyProperties(task, addBoardCardTaskArg);
                    addBoardCardTaskArg.setBoardCardId(boardCardId);
                    this.addBoardCardTask(ea, fsUserId, boardId, addBoardCardTaskArg, createBySystem, createBoardCardActivity);
                }
                index++;
            }
        }
        return boardId;
    }

    public String addBoard(String ea, Integer fsUserId, AddBoardArg arg){
        //添加看板
        String boardId = UUIDUtil.getUUID();
        String defaultTemplateId = arg.getDefaultTemplateId();
        BoardEntity boardEntity = new BoardEntity(boardId, ea, arg.getName(), arg.getDescription(), arg.getVisibleRage(), arg.getType(), arg.getObjectApiName(), arg.getObjectId(), fsUserId, arg.getCover(),
            defaultTemplateId);
        boardEntity.setTemplateType(BoardTemplateTypeEnum.NOT_TEMPLATE.getTemplateType());
        if (BoardTypeEnum.SOP_TYPE.getType().equals(arg.getType())) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(arg.getObjectId()), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_291));
            String sceneType = boardDao.getSceneTypeById(defaultTemplateId, ea);
            if (StringUtils.isEmpty(sceneType)) {
                sceneType = boardDao.getSceneTypeById(defaultTemplateId, "__SYSTEM");
            }
            Preconditions.checkArgument(StringUtils.isEmpty(boardDao.getSopBoardIdByObjectId(ea, arg.getObjectId())), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_296));
            boardEntity.setSceneType(sceneType);
            String marketingEventTypeBySceneType = MarketingSceneType.getMarketingEventTypeBySceneType(sceneType);
            if (StringUtils.isNotEmpty(marketingEventTypeBySceneType)) {
                boardEntity.setMarketingEventType(marketingEventTypeBySceneType);
                boardEntity.setMarketingEventId(arg.getObjectId());
                boardEntity.setAssociatedObjectType(BoardAssociatedObjectTypeEnum.MARKETING_EVENT_DATA.getType());
                boardEntity.setGoalType("");
            } else {
                Integer marketingActivityTypeBySceneType = MarketingSceneType.getMarketingActivityTypeBySceneType(sceneType);
                if (marketingActivityTypeBySceneType != null) {
                    boardEntity.setMarketingActivityType(marketingActivityTypeBySceneType);
                    boardEntity.setMarketingActivityId(arg.getObjectId());
                    boardEntity.setAssociatedObjectType(BoardAssociatedObjectTypeEnum.MARKETING_ACTIVITY_DATA.getType());
                    boardEntity.setGoalType("");
                }
            }
        }
        int insertRow = boardDao.insertBoard(boardEntity);
        Preconditions.checkState(insertRow == 1, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_315));
        //如果为公有看板列表，则无需添加看板用户，默认ea下的用户均可见
        if (arg.getVisibleRage().equals(BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange())) {
            return boardId;
        }
        //批量添加该看板对应用户
        List<Integer> boardUserIds = CollectionUtils.isEmpty(arg.getBoardUserIds()) ? new LinkedList<>() : arg.getBoardUserIds();
        boardUserIds.add(fsUserId);
        boardUserDao.batchInsertBoardUser(ea, boardId, boardUserIds);
        // 添加动态
        addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, "CREATE", BoardCardActivityActionTypeEnum.CREATE_BOARD.getActionType(), new BoardCardActivityContentData(),
            false, BoardOperatorTypeEnum.USER.getOperator());
        return boardId;
    }

    public String addBoardCardList(String ea, Integer fsUserId, String boardId, String name){
        String id = UUIDUtil.getUUID();
        int insertCount = boardCardListDao.insertBoardCardList(id, ea, boardId, name, fsUserId);
        Preconditions.checkState(insertCount > 0, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_333));
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getCardListDisplayKey(boardId), ImmutableList.of(new NestedId(id)));
        return id;
    }

    public String addBoardCard(String ea, Integer fsUserId, String boardId, AddBoardCardArg addBoardCardArg, boolean createBySystem){
        BoardCardEntity boardCard = new BoardCardEntity();
        String cardId = UUIDUtil.getUUID();
        boardCard.setId(cardId);
        boardCard.setEa(ea);
        BeanUtils.copyProperties(addBoardCardArg, boardCard, "startTime", "endTime");
        boardCard.setBoardId(boardId);
        boardCard.setCreator(fsUserId);
        boardCard.setStatus(BoardCardStatus.NOT_START.getStatus());
        if (addBoardCardArg.getStartTime() != null){
            boardCard.setStartTime(new Date(addBoardCardArg.getStartTime()));
        }
        if(addBoardCardArg.getEndTime() != null){
            boardCard.setEndTime(new Date(addBoardCardArg.getEndTime()));
        }
        boardCardDao.insertBoardCard(boardCard);
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getCardDisplayKey(addBoardCardArg.getBoardCardListId()), ImmutableList.of(new NestedId(cardId)));
        if (!createBySystem && addBoardCardArg.getPrincipals() != null && !addBoardCardArg.getPrincipals().isEmpty()){
            Set<Integer> principals = new HashSet<>(addBoardCardArg.getPrincipals());
            principals.remove(fsUserId);
            if (!principals.isEmpty()){
                sendAddAsBoardCardPrincipalNotification(ea, fsUserId, boardCard.getId(), principals);
            }
        }
        addBoardCardActivityWhenCreateBoardCard(ea, String.valueOf(fsUserId), addBoardCardArg, boardId, cardId, createBySystem, BoardOperatorTypeEnum.USER.getOperator());
        return cardId;
    }

    public boolean batchSortBoardCardToBoardCardList(String ea, List<BoardCardEntity> boardCards) {
        if (CollectionUtils.isEmpty(boardCards)) {
            return false;
        }
        for (BoardCardEntity boardCard : boardCards) {
            displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getCardDisplayKey(boardCard.getBoardCardListId()), ImmutableList.of(new NestedId(boardCard.getId())));
        }
        return true;
    }

    public List<BoardCardEntity> batchAddBoardCards(String ea, Integer fsUserId, String boardId, List<AddBoardCardArg> addBoardCardArgs, boolean createBySystem, boolean createBoardCardActivity) {
        if (CollectionUtils.isEmpty(addBoardCardArgs)) {
            return new ArrayList<>(0);
        }
        List<BoardCardEntity> boardCards = addBoardCardArgs.stream().map(addBoardCardArg -> {
            BoardCardEntity boardCard = new BoardCardEntity();
            String cardId = UUIDUtil.getUUID();
            boardCard.setId(cardId);
            boardCard.setEa(ea);
            BeanUtils.copyProperties(addBoardCardArg, boardCard, "startTime", "endTime");
            boardCard.setBoardId(boardId);
            boardCard.setCreator(fsUserId);
            boardCard.setStatus(BoardCardStatus.NOT_START.getStatus());
            if (addBoardCardArg.getStartTime() != null) {
                boardCard.setStartTime(new Date(addBoardCardArg.getStartTime()));
            }
            if (addBoardCardArg.getEndTime() != null) {
                boardCard.setEndTime(new Date(addBoardCardArg.getEndTime()));
            }
            if (!createBySystem && addBoardCardArg.getPrincipals() != null && !addBoardCardArg.getPrincipals().isEmpty()) {
                Set<Integer> principals = new HashSet<>(addBoardCardArg.getPrincipals());
                principals.remove(fsUserId);
                if (!principals.isEmpty()) {
                    sendAddAsBoardCardPrincipalNotification(ea, fsUserId, boardCard.getId(), principals);
                }
            }
            return boardCard;
        }).collect(Collectors.toList());
        boardCardDao.batchInsertBoardCard(boardCards);
        if (createBoardCardActivity) {
            addBoardCardActivityWhenCreateBoardCard(ea, fsUserId, boardCards, boardId, createBySystem);
        }
        return boardCards;
    }

    public String addBoardCardTask(String ea, Integer fsUserId, String boardId, AddBoardCardTaskArg arg, boolean createBySystem, boolean createBoardCardActivity){
        BoardCardTaskEntity boardCardTask = new BoardCardTaskEntity();
        BeanUtils.copyProperties(arg, boardCardTask, "startTime", "endTime");
        String id = UUIDUtil.getUUID();
        boardCardTask.setId(id);
        boardCardTask.setBoardId(boardId);
        if (arg.getStartTime() != null){
            boardCardTask.setStartTime(new Date(arg.getStartTime()));
        }
        if (arg.getEndTime() != null){
            boardCardTask.setEndTime(new Date(arg.getEndTime()));
        }
        boardCardTask.setStatus(BoardCardTaskStatus.NOT_START.getStatus());
        boardCardTask.setCreator(fsUserId);
        boardCardTask.setEa(ea);
        boardCardTaskDao.insertBoardCardTask(boardCardTask);
        if (!createBySystem && arg.getExecutor() != null && !arg.getExecutor().equals(fsUserId)){
            sendAddAsBoardCardTaskExecutorNotification(ea,fsUserId, id, arg.getExecutor());
        }
        if (createBoardCardActivity) {
            addBoardCardActivityWhenAddBoardCardTask(ea, String.valueOf(fsUserId), boardId, arg, createBySystem, BoardOperatorTypeEnum.USER.getOperator());
        }
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getTaskDisplayKey(arg.getBoardCardId()), ImmutableList.of(new NestedId(id)));
        return id;
    }

    public Map<String, BoardStatisticData> getBoardCardStatisticDataMapByEntities(String ea, Collection<BoardCardEntity> boardCards){
        if (boardCards == null || boardCards.isEmpty()){
            return new HashMap<>(0);
        }
        List<BoardCardResult> boardCardResults = boardCards.stream().map(boardCard -> {
            BoardCardResult boardCardResult = new BoardCardResult();
            BeanUtils.copyProperties(boardCard, boardCardResult, "startTime", "endTime");
            return boardCardResult;
        }).collect(Collectors.toList());
        return doGetBoardCardStatisticData(ea, boardCardResults);
    }

    public void fillStatisticDataToBoardCardResult(String ea, Collection<BoardCardResult> boardCardResults){
        Map<String, BoardStatisticData> boardCardIdToStatisticDataMap = doGetBoardCardStatisticData(ea, boardCardResults);
        boardCardResults.forEach(boardCardResult ->
            boardCardResult.setStatisticData(boardCardIdToStatisticDataMap.get(boardCardResult.getId()))
        );
    }

    public void fillStatisticDataToBoardTargetResult(String ea, BoardResult boardResult) {
        boardResult.setStatisticData(doGetBoardMainTargetStatisticData(ea, boardResult).get(boardResult.getId()));
    }

    public void batchFillStatisticDataToBoardTargetResults(String ea, List<BoardResult> boardResults) {
        Collection<CommonStatisticArg> commonStatisticArgs = new ArrayList<>();
        HashSet<String> goalUserGroupIds = new HashSet<>();
        List<String> workFinishedBoardIds = new ArrayList<>();
        for (BoardResult boardResult : boardResults) {
            String goalType = boardResult.getGoalType();
            if (BoardCardGoalType.GROUP_USER_COUNT.getType().equals(goalType)) {
                goalUserGroupIds.add(boardResult.getGoalUserGroupId());
                continue;
            }
            if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(goalType)) {
                workFinishedBoardIds.add(boardResult.getId());
                continue;
            }
            CommonStatisticArg commonStatisticArg = new CommonStatisticArg();
            BeanUtils.copyProperties(boardResult, commonStatisticArg, "type");
            commonStatisticArg.setType(boardResult.getAssociatedObjectType());
            commonStatisticArgs.add(commonStatisticArg);
        }
        Map<String, BoardStatisticData> statisticDataMap = doGetCommonStatisticData(ea, commonStatisticArgs);
        Map<String, Map<String, Integer>> groupUserCountMap = marketingUserGroupDao.batchQueryUserNumberByIds(ea, goalUserGroupIds);
        Map<String, Map<String, Long>> workCountMap = boardCardDao.batchQueryCardCountByIds(ea, workFinishedBoardIds);
        Map<String, Map<String, Long>> finishedWorkCountMap = boardCardDao.batchQueryFinishedCardCountByIds(ea, workFinishedBoardIds);
        for (BoardResult boardResult : boardResults) {
            String goalType = boardResult.getGoalType();
            if (BoardCardGoalType.GROUP_USER_COUNT.getType().equals(goalType)) {
                String goalUserGroupId = boardResult.getGoalUserGroupId();
                Map<String, Integer> map = groupUserCountMap.get(goalUserGroupId);
                Integer userNumber = null;
                if (map != null) {
                    userNumber = map.get("user_number");
                }
                BoardStatisticData statisticData = new BoardStatisticData();
                statisticData.setGroupUserCount(userNumber == null ? 0 : Math.toIntExact(userNumber));
                boardResult.setStatisticData(statisticData);
                continue;
            }
            if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(goalType)) {
                String boardId = boardResult.getId();
                Map<String, Long> map = workCountMap.get(boardId);
                if (map != null) {
                    Long goalValue = map.get("count");
                    boardResult.setGoalValue(goalValue == null ? 0 : Math.toIntExact(goalValue));
                }
                map = finishedWorkCountMap.get(boardId);
                Long workFinishedCount = null;
                if (map != null) {
                    workFinishedCount = map.get("count");
                }
                BoardStatisticData statisticData = new BoardStatisticData();
                statisticData.setWorkFinishedCount(workFinishedCount == null ? 0 : Math.toIntExact(workFinishedCount));
                boardResult.setStatisticData(statisticData);
                continue;
            }
            BoardStatisticData statisticData = statisticDataMap.get(boardResult.getId());
            if (statisticData != null) {
                boardResult.setStatisticData(statisticData);
            }
        }
    }

    public void fillPrincipalsNameToBoardCardResult(String ea, Collection<BoardCardResult> boardCardResults) {
        List<Integer> fsUserId = Lists.newArrayList();
        for (BoardCardResult boardCardResult : boardCardResults) {
            if (CollectionUtils.isEmpty(boardCardResult.getPrincipals())) {
                continue;
            }
            fsUserId.addAll(boardCardResult.getPrincipals());
        }
        if (CollectionUtils.isEmpty(fsUserId)) {
            return;
        }
        fsUserId = fsUserId.stream().distinct().collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserId, true);
        if (MapUtils.isEmpty(fsEmployeeMsgMap)) {
            return;
        }
        boardCardResults.forEach(data -> {
            List<String> userName = Lists.newArrayList();
            if (CollectionUtils.isEmpty(data.getPrincipals())) {
                return;
            }
            for (Integer userId : data.getPrincipals()) {
                FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(userId);
                if (fsEmployeeMsg != null) {
                    userName.add(fsEmployeeMsg.getFullName());
                }
            }
            data.setPrincipalsName(userName);
        });
    }

    public void fillTargetObjectNameToBoardResult(String ea, BoardResult boardResult) {
        if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(boardResult.getGoalType())) {
            return; // 工作项完成量 为目标的 不用显示关联对象
        }
        if (BoardCardGoalType.GROUP_USER_COUNT.getType().equals(boardResult.getGoalType())) {
            String marketingUserGroupName = marketingUserGroupDao.queryNameById(boardResult.getGoalUserGroupId(), ea);
            boardResult.setTargetObjectName(marketingUserGroupName);
            return;
        }
        String marketingActivityId = boardResult.getMarketingActivityId();
        if (BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType().equals(boardResult.getAssociatedObjectType()) && !Strings.isNullOrEmpty(marketingActivityId)) {
            Set<String> marketingActivityIdSet = new HashSet<>();
            marketingActivityIdSet.add(marketingActivityId);
            String targetObjectName = crmV2Manager.getObjectNameMapByIdsByCache(ea, -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), marketingActivityIdSet).get(marketingActivityId);
            boardResult.setTargetObjectName(targetObjectName);
            return;
        }
        String marketingEventId = boardResult.getMarketingEventId();
        if (BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardResult.getAssociatedObjectType()) && !Strings.isNullOrEmpty(marketingEventId)) {
            Set<String> marketingEventIdSet = new HashSet<>();
            marketingEventIdSet.add(marketingEventId);
            String targetObjectName = crmV2Manager.getObjectNameMapByIdsByCache(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIdSet).get(marketingEventId);
            boardResult.setTargetObjectName(targetObjectName);
            return;
        }
        String distributePlanId = boardResult.getDistributePlanId();
        if (BoardCardTypeEnum.DISTRIBUTE_PLAN_DATA.getType().equals(boardResult.getAssociatedObjectType()) && !Strings.isNullOrEmpty(distributePlanId)) {
            List<String> distributePlanIdList = new ArrayList<>();
            distributePlanIdList.add(distributePlanId);
            List<DistributePlanEntity> distributePlans = distributePlanDao.queryDistributePlanByIds(distributePlanIdList);
            if (distributePlans != null && distributePlans.size() > 0) {
                boardResult.setTargetObjectName(distributePlans.get(0).getPlanTitle());
            }
        }
    }

    public void fillTargetObjectNameToBoardCardResult(String ea, Collection<BoardCardResult> boardCardResults){
        Set<String> marketingActivityIds = boardCardResults.stream().filter(card -> BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType().equals(card.getType()))
                .map(BoardCardResult::getMarketingActivityId).filter(marketingActivityId -> !Strings.isNullOrEmpty(marketingActivityId)).collect(Collectors.toSet());
        Map<String, String> marketingActivityIdToNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), marketingActivityIds);

        Set<String> marketingEventIds = boardCardResults.stream().filter(card -> BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(card.getType())).map(BoardCardResult::getMarketingEventId)
                .filter(marketingEventId -> !Strings.isNullOrEmpty(marketingEventId)).collect(Collectors.toSet());
        Map<String, String> marketingEventIdToNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds);

        Set<String> distributePlanIds = boardCardResults.stream().filter(card -> BoardCardTypeEnum.DISTRIBUTE_PLAN_DATA.getType().equals(card.getType())).map(BoardCardResult::getDistributePlanId)
                .filter(distributePlanId -> !Strings.isNullOrEmpty(distributePlanId)).collect(Collectors.toSet());
        Map<String, String> distributeIdToNameMap = new HashMap<>(0);
        if (!distributePlanIds.isEmpty()){
            List<DistributePlanEntity> distributePlans = distributePlanDao.queryDistributePlanByIds(new ArrayList<>(distributePlanIds));
            distributeIdToNameMap = distributePlans.stream().collect(Collectors.toMap(DistributePlanEntity::getId, DistributePlanEntity::getPlanTitle, (v1, v2) -> v1));
        }

        for (BoardCardResult boardCardResult : boardCardResults) {
            if (BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType().equals(boardCardResult.getType()) && !Strings.isNullOrEmpty(boardCardResult.getMarketingActivityId())){
                boardCardResult.setTargetObjectName(marketingActivityIdToNameMap.get(boardCardResult.getMarketingActivityId()));
            }
            if (BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardCardResult.getType()) && !Strings.isNullOrEmpty(boardCardResult.getMarketingEventId())){
                boardCardResult.setTargetObjectName(marketingEventIdToNameMap.get(boardCardResult.getMarketingEventId()));
            }
            if (BoardCardTypeEnum.DISTRIBUTE_PLAN_DATA.getType().equals(boardCardResult.getType()) && !Strings.isNullOrEmpty(boardCardResult.getDistributePlanId())){
                boardCardResult.setTargetObjectName(distributeIdToNameMap.get(boardCardResult.getDistributePlanId()));
            }
        }
    }

    public Optional<List<BoardCardTaskEntity>> listBoardCardTaskEntityByBoardCardIds(String ea, List<String> boardCards) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(boardCards)) {
            return Optional.empty();
        }
        List<BoardCardTaskEntity> result = new LinkedList<>();
        List<List<String>> boardCardPartitions = Lists.partition(boardCards, 100);
        for (List<String> boardCardPartition : boardCardPartitions) {
            List<BoardCardTaskEntity> boardCardTaskEntities = boardCardTaskDao.listByBoardCardIds(ea, boardCardPartition);
            result.addAll(boardCardTaskEntities);
        }
        return Optional.of(result);
    }

    /**
     * 添加每一个卡片的最新动态，一次性拉取所有卡片的所有最新动态
     */
    public boolean fillLatestBoardCardActivityToBoardCardResult(String ea, Collection<BoardCardResult> boardCardResults) {
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(boardCardResults)) {
            return false;
        }
        //拉取所有卡片的最新动态
        List<String> boardCardIds = boardCardResults.stream().map(BoardCardResult::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boardCardIds)) {
            return true;
        }
        List<BoardCardActivityEntity> boardCardActivityEntities = boardCardActivityDao.listLatestBoardCardActivityByBoardCardIds(ea, boardCardIds);
        //创建用户信息缓存
        HashSet<Integer> operatorSet = new HashSet<>();
        HashMap<String, BoardCardActivityEntity> boardCardActivityForStringOperator = new HashMap<>();
        for (BoardCardActivityEntity boardCardActivityEntity : boardCardActivityEntities) {
            if (BoardOperatorTypeEnum.USER.getOperator().equals(boardCardActivityEntity.getOperatorType())) {
                if (boardCardActivityEntity.getOperator() != null) {
                    operatorSet.add(Integer.valueOf(boardCardActivityEntity.getOperator()));
                }
            } else {
                boardCardActivityForStringOperator.put(boardCardActivityEntity.getBoardCardId(), boardCardActivityEntity);
            }
        }
        List<Integer> operators = new ArrayList<>(operatorSet);
        Map<Integer, FSEmployeeMsg> fSEmployeeMsgCache = fsAddressBookManager.getEmployeeInfoByUserIds(ea, operators, true);
        Map<String, BoardCardActivityEntity> boardCardIdToBoardCardActivityEntity = boardCardActivityEntities.stream().collect(Collectors.toMap(BoardCardActivityEntity::getBoardCardId, Function.identity(), (k1, k2) -> k1));
        //动态添加到Results中
        boardCardResults.forEach(boardCardResult -> {
            BoardCardActivityEntity boardCardActivityEntity = boardCardIdToBoardCardActivityEntity.get(boardCardResult.getId());
            if (boardCardActivityEntity == null) {
                boardCardActivityEntity = boardCardActivityForStringOperator.get(boardCardResult.getBoardId());
            }
            if (boardCardResult.getLatestBoardCardActivity() == null) {
                boardCardResult.setLatestBoardCardActivity(new BoardCardActivityResult());
            }
            boardCardActivityEntityToBoardCardActivityResult(ea, boardCardActivityEntity, boardCardResult.getLatestBoardCardActivity(), fSEmployeeMsgCache);
        });
        return true;
    }

    private Map<String, BoardStatisticData> doGetBoardCardStatisticData(String ea, Collection<BoardCardResult> boardCardResults){
        if (boardCardResults == null || boardCardResults.isEmpty()){
            return new HashMap<>(0);
        }
        Collection<CommonStatisticArg> commonStatisticArgs = boardCardResults.stream().map(boardCardResult -> {
            CommonStatisticArg commonStatisticArg = new CommonStatisticArg();
            BeanUtils.copyProperties(boardCardResult,commonStatisticArg);
            return commonStatisticArg;
        }).collect(Collectors.toList());

        Set<String> boardCardIds = boardCardResults.stream().map(BoardCardResult::getId).collect(Collectors.toSet());
        Map<String, Integer> boardCardIdToAllTaskCount = boardCardTaskDao.groupCountTasksByBoardCardIdListAndStatus(ea, boardCardIds, null).stream().collect(DataCount.getMapCollector());
        Map<String, Integer> boardCardIdToFinishedTaskCount = boardCardTaskDao.groupCountTasksByBoardCardIdListAndStatus(ea, boardCardIds, BoardCardTaskStatus.FINISHED.getStatus()).stream().collect(DataCount.getMapCollector());
        Map<String, BoardStatisticData> statisticDataMap = doGetCommonStatisticData(ea, commonStatisticArgs);
        for (CommonStatisticArg commonStatisticArg : commonStatisticArgs) {
            String id = commonStatisticArg.getId();
            BoardStatisticData statisticData = statisticDataMap.get(id);
            statisticData.setTotalTaskCount(boardCardIdToAllTaskCount.get(id));
            statisticData.setFinishedTaskCount(boardCardIdToFinishedTaskCount.get(id));
        }
        return statisticDataMap;
    }

    public Map<String, BoardStatisticData> doGetBoardMainTargetStatisticData(String ea, BoardResult boardResult) {
        String goalType = boardResult.getGoalType();
        if (BoardCardGoalType.GROUP_USER_COUNT.getType().equals(goalType)) {
            String goalUserGroupId = boardResult.getGoalUserGroupId();
            Integer userNumber = marketingUserGroupDao.queryUserNumberById(ea, goalUserGroupId);
            BoardStatisticData statisticData = new BoardStatisticData();
            statisticData.setGroupUserCount(userNumber == null ? 0 : userNumber);
            Map<String, BoardStatisticData> res = new HashMap<>();
            res.put(boardResult.getId(), statisticData);
            return res;
        }
        if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(goalType)) {
            String boardId = boardResult.getId();
            boardResult.setGoalValue(boardCardDao.queryCardCountById(ea, boardId));
            BoardStatisticData statisticData = new BoardStatisticData();
            statisticData.setWorkFinishedCount(boardCardDao.queryFinishedCardCountById(ea, boardId));
            Map<String, BoardStatisticData> res = new HashMap<>();
            res.put(boardId, statisticData);
            return res;
        }
        CommonStatisticArg commonStatisticArg = new CommonStatisticArg();
        BeanUtils.copyProperties(boardResult, commonStatisticArg, "type");
        commonStatisticArg.setType(boardResult.getAssociatedObjectType());
        Collection<CommonStatisticArg> commonStatisticArgs = new ArrayList<>();
        commonStatisticArgs.add(commonStatisticArg);
        return doGetCommonStatisticData(ea, commonStatisticArgs);
    }

    @Data
    private static class CommonStatisticArg{
        private String id;
        private String type;
        private Integer marketingActivityType;
        private String marketingActivityId;
        private String marketingEventType;
        private String marketingEventId;
        private String distributePlanId;
    }

    private Map<String, BoardStatisticData> doGetCommonStatisticData(String ea, Collection<CommonStatisticArg> boardStatisticResults){
        if (boardStatisticResults == null || boardStatisticResults.isEmpty()){
            return new HashMap<>(0);
        }
        List<String> marketingEventIdList = boardStatisticResults.stream().map(CommonStatisticArg::getMarketingEventId).filter(e -> StringUtils.isNotBlank(e)).distinct().collect(Collectors.toList());
        List<ObjectData> marketingEventObjList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList(CrmV2MarketingEventFieldEnum.ID.getFieldName(), CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()), marketingEventIdList);
        Map<String, String> marketingEventIdToEventFormMap = marketingEventObjList.stream().filter(e -> StringUtils.isNotBlank(e.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()))).collect(Collectors.toMap(ObjectData::getId, e -> e.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()), (v1, v2) -> v1));
        Map<String, Integer> marketingActivityIdToTypeMap = boardStatisticResults.stream().filter(boardStatisticResult -> BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType().equals(boardStatisticResult.getType()))
                .filter(boardStatisticResult -> boardStatisticResult.getMarketingActivityType() != null).filter(boardStatisticResult -> !Strings.isNullOrEmpty(boardStatisticResult.getMarketingActivityId()))
                .collect(Collectors.toMap(CommonStatisticArg::getMarketingActivityId, CommonStatisticArg::getMarketingActivityType, (v1, v2) -> v1));
        Map<String, MarketingActivityStatisticData> activityStatisticDataMap = marketingActivityManager.getMarketingActivityStatisticDataByMarketingActivityIds(ea, marketingActivityIdToTypeMap);

        Integer externalUserCount = 0;
        boolean needStatisticExternalUser = boardStatisticResults.stream().anyMatch(boardStatisticResult -> BoardCardTypeEnum.EXTERNAL_USER_DATA.getType().equals(boardStatisticResult.getType()));
        if (needStatisticExternalUser){
            externalUserCount = EXTERNAL_USER_COUNT_CACHE.getUnchecked(ea);
        }

        Set<String> distributePlanIds = boardStatisticResults.stream().filter(boardStatisticResult -> BoardCardTypeEnum.DISTRIBUTE_PLAN_DATA.getType().equals(boardStatisticResult.getType()))
                .map(CommonStatisticArg::getDistributePlanId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, Integer> distributePlanToDistributorCount = new HashMap<>(0);
        Map<String, Integer> distributePlanToClueCount = new HashMap<>(0);
        if(!distributePlanIds.isEmpty()){
            distributePlanToDistributorCount = distributorDao.groupCountDistributorByDistributePlanIds(ea, distributePlanIds).stream().collect(DataCount.getMapCollector());
            distributePlanToClueCount = clueDao.groupCountClueByDistributePlanIds(ea, distributePlanIds).stream().collect(DataCount.getMapCollector());
        }

        Set<String> liveMarketingEventIds = boardStatisticResults.stream().filter(boardStatisticResult -> BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()))
                .filter(boardStatisticResult -> MarketingEventFormEnum.LIVE_MARKETING.getValue().equals(marketingEventIdToEventFormMap.get(boardStatisticResult.getMarketingEventId()))).map(CommonStatisticArg::getMarketingEventId)
                .filter(marketingEventId -> !Strings.isNullOrEmpty(marketingEventId)).collect(Collectors.toSet());
        Map<String, Integer> liveMarketingEventIdToPvCount = new HashMap<>(0);
        Map<String, Integer> liveMarketingEventIdToLeadCount = new HashMap<>(0);
        if (!liveMarketingEventIds.isEmpty()){
            liveMarketingEventIdToPvCount = marketingLiveStatisticsDAO.groupCountPvByMarketingEventIds(eieaConverter.enterpriseAccountToId(ea), liveMarketingEventIds).stream().collect(DataCount.getMapCollector());
            liveMarketingEventIdToLeadCount = customizeFormDataUserDAO.groupCountByMarketingEventIds(liveMarketingEventIds).stream().collect(DataCount.getMapCollector());
        }

        Set<String> contentMarketingEventIds = boardStatisticResults.stream().filter(boardStatisticResult -> BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()))
                .filter(boardStatisticResult -> MarketingEventFormEnum.ONLINE_MARKETING.getValue().equals(marketingEventIdToEventFormMap.get(boardStatisticResult.getMarketingEventId()))).map(CommonStatisticArg::getMarketingEventId)
                .filter(marketingEventId -> !Strings.isNullOrEmpty(marketingEventId)).collect(Collectors.toSet());
        Map<String, Integer> contentMarketingEventIdToPvCount = new HashMap<>(0);
        Map<String, Integer> contentMarketingEventIdToLeadCount = new HashMap<>(0);
        if (!contentMarketingEventIds.isEmpty()){
            contentMarketingEventIdToPvCount = marketingEventManager.calMarketingEventsPV(ea, new ArrayList<>(contentMarketingEventIds));
            contentMarketingEventIdToLeadCount = customizeFormDataUserDAO.groupCountByMarketingEventIds(contentMarketingEventIds).stream().collect(DataCount.getMapCollector());
        }

        Set<String> conferenceMarketingEventIds = boardStatisticResults.stream().filter(boardStatisticResult -> BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()))
                .filter(boardStatisticResult -> MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(marketingEventIdToEventFormMap.get(boardStatisticResult.getMarketingEventId()))).map(CommonStatisticArg::getMarketingEventId)
                .filter(marketingEventId -> !Strings.isNullOrEmpty(marketingEventId)).collect(Collectors.toSet());
        Map<String, Integer> conferenceMarketingEventIdToEnrollCount = new HashMap<>(0);
        Map<String, Integer> conferenceMarketingEventIdToCheckInUserCount = new HashMap<>(0);
        if (!conferenceMarketingEventIds.isEmpty()){
            conferenceMarketingEventIdToEnrollCount = activityEnrollDataDAO.groupCountByMarketingEventIds(conferenceMarketingEventIds).stream().collect(DataCount.getMapCollector());
            conferenceMarketingEventIdToCheckInUserCount = activityEnrollDataDAO.groupCountCheckInDataByMarketingEventIds(conferenceMarketingEventIds).stream().collect(DataCount.getMapCollector());
        }

        Map<String, BoardStatisticData> boardCardStatisticDataMap = new HashMap<>(boardStatisticResults.size());
        for (CommonStatisticArg boardStatisticResult : boardStatisticResults) {
            BoardStatisticData BoardStatisticData = new BoardStatisticData();
            if (BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType().equals(boardStatisticResult.getType()) && !Strings.isNullOrEmpty(boardStatisticResult.getMarketingActivityId())){
                MarketingActivityStatisticData marketingActivityStatisticData = activityStatisticDataMap.get(boardStatisticResult.getMarketingActivityId());
                if (marketingActivityStatisticData != null){
                    BeanUtils.copyProperties(marketingActivityStatisticData, BoardStatisticData);
                }
            }
            if (BoardCardTypeEnum.EXTERNAL_USER_DATA.getType().equals(boardStatisticResult.getType())){
                BoardStatisticData.setExternalUserCount(externalUserCount);
            }
            if (BoardCardTypeEnum.DISTRIBUTE_PLAN_DATA.getType().equals(boardStatisticResult.getType()) && !Strings.isNullOrEmpty(boardStatisticResult.getDistributePlanId())){
                BoardStatisticData.setLeadUserCount(distributePlanToClueCount.get(boardStatisticResult.getDistributePlanId()));
                BoardStatisticData.setDistributorCount(distributePlanToDistributorCount.get(boardStatisticResult.getDistributePlanId()));
            }
            String eventForm = marketingEventIdToEventFormMap.get(boardStatisticResult.getMarketingEventId());
            if (BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()) && MarketingEventFormEnum.LIVE_MARKETING.getValue().equals(eventForm) && !Strings.isNullOrEmpty(boardStatisticResult.getMarketingEventId())){
                BoardStatisticData.setLiveViewCount(liveMarketingEventIdToPvCount.get(boardStatisticResult.getMarketingEventId()));
                BoardStatisticData.setLeadUserCount(liveMarketingEventIdToLeadCount.get(boardStatisticResult.getMarketingEventId()));
            }
            if (BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()) && MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(eventForm) && !Strings.isNullOrEmpty(boardStatisticResult.getMarketingEventId())){
                BoardStatisticData.setEnrollUserCount(conferenceMarketingEventIdToEnrollCount.get(boardStatisticResult.getMarketingEventId()));
                BoardStatisticData.setCheckInUserCount(conferenceMarketingEventIdToCheckInUserCount.get(boardStatisticResult.getMarketingEventId()));
            }
            if (BoardCardTypeEnum.MARKETING_EVENT_DATA.getType().equals(boardStatisticResult.getType()) && MarketingEventFormEnum.ONLINE_MARKETING.getValue().equals(eventForm) && !Strings.isNullOrEmpty(boardStatisticResult.getMarketingEventId())){
                BoardStatisticData.setPv(contentMarketingEventIdToPvCount.get(boardStatisticResult.getMarketingEventId()));
                BoardStatisticData.setLeadUserCount(contentMarketingEventIdToLeadCount.get(boardStatisticResult.getMarketingEventId()));
            }
            boardCardStatisticDataMap.put(boardStatisticResult.getId(), BoardStatisticData);
        }
        return boardCardStatisticDataMap;
    }

    public boolean addBoardCardActivity(String ea,String operator, String boardId, String boardCardId, String actionType, BoardCardActivityContentData boardCardActivityContentData, boolean createBySystem, String operatorType) {
        //校验参数
        if (!BoardCardActivityActionTypeEnum.isValid(actionType)) {
            return false;
        }
        if (BoardCardActivityActionTypeEnum.SET_CARD_GOAL.getActionType().equals(actionType)) {
            if (boardCardActivityContentData.getBoardCardGoalTypeName() == null || boardCardActivityContentData.getBoardCardGoalValue() == null) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.MOVE_BOARD_CARD.getActionType().equals(actionType)) {
            if (boardCardActivityContentData.getMoveBoardCardFromBoardCardListName() == null || boardCardActivityContentData.getMoveBoardCardToBoardCardListName() == null) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType().equals(actionType)) {
            if (Strings.isNullOrEmpty(boardCardActivityContentData.getAssociateMarketingEventName())) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.ASSOCIATE_MARKETING_ACTIVITY.getActionType().equals(actionType)) {
            if (Strings.isNullOrEmpty(boardCardActivityContentData.getAssociateMarketingActivityName())) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.UPDATE_TASK_START_TIME.getActionType().equals(actionType)) {
            if (boardCardActivityContentData.getBoardCardTaskName() == null || boardCardActivityContentData.getUpdateBoardCardTaskStartTime() == null) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.UPDATE_TASK_END_TIME.getActionType().equals(actionType)) {
            if (boardCardActivityContentData.getBoardCardTaskName() == null || boardCardActivityContentData.getUpdateBoardCardTaskEndTime() == null) {
                return false;
            }
        }
        if (BoardCardActivityActionTypeEnum.FINISH_WORK.getActionType().equals(actionType) || BoardCardActivityActionTypeEnum.UNDO_WORK.getActionType().equals(actionType)) {
            if (Strings.isNullOrEmpty(boardCardActivityContentData.getStatus()) || !BoardCardStatus.isValid(boardCardActivityContentData.getStatus())) {
                return false;
            }
        }
        return boardCardActivityDao.insertBoardCardActivity(UUIDUtil.getUUID(), ea, boardId, boardCardId, actionType, boardCardActivityContentData, operator, operatorType, createBySystem) > 0;
    }


    public boolean addBoardMenuActivity(String ea,Integer fsUserId, String boardId, String actionType, BoardCardActivityContentData contentData, boolean createBySystem) {
        // 校验参数
        if (!BoardMenuActivityActionTypeEnum.isValid(actionType)) {
            return false;
        }
        if (BoardMenuActivityActionTypeEnum.SET_MAIN_GOAL.getActionType().equals(actionType)) {
            if (contentData.getBoardCardGoalTypeName() == null || contentData.getBoardCardGoalValue() == null) {
                return false;
            }
        }
        if (BoardMenuActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType().equals(actionType)) {
            if (Strings.isNullOrEmpty(contentData.getAssociateMarketingEventName())) {
                return false;
            }
        }
        if (BoardMenuActivityActionTypeEnum.ASSOCIATE_MARKETING_ACTIVITY.getActionType().equals(actionType)) {
            if (Strings.isNullOrEmpty(contentData.getAssociateMarketingActivityName())) {
                return false;
            }
        }
        return boardCardActivityDao.insertBoardCardActivity(UUIDUtil.getUUID(), ea, boardId, "board_menu", actionType, contentData, String.valueOf(fsUserId),
            BoardOperatorTypeEnum.USER.getOperator(), createBySystem) > 0;
    }

    /**
     * 获得最新的一条动态，如果不存在返回null
     */
    public BoardCardActivityResult getLatestBoardCardActivityByBoardCardId(String ea, String boardCardId) {
        ListBoardCardActivityArg arg = new ListBoardCardActivityArg();
        arg.setPageSize(1);
        arg.setPageNo(1);
        arg.setBoardCardId(boardCardId);
        return listBoardCardActivityByBoardCardId(ea, arg).isEmpty() ? null : listBoardCardActivityByBoardCardId(ea, arg).get(0);
    }

    /**
     * 分页获得动态,默认50条
     */
    public List<BoardCardActivityResult> listBoardCardActivityByBoardCardId(String ea, ListBoardCardActivityArg arg) {
        if (arg.getPageNo() == null || arg.getPageSize() == null) {
            int pageSize = 50;
            int pageNo = 1;
            arg.setPageNo(pageNo);
            arg.setPageSize(pageSize);
        }
        List<BoardCardActivityEntity> boardCardActivityEntities = boardCardActivityDao.listBoardCardActivityByBoardCardId(ea, arg.getBoardCardId(), arg.getLimit(), arg.getOffset());
        if (boardCardActivityEntities.isEmpty()) {
            return new LinkedList<>();
        }
        //所有的操作者id，用于创建缓存
        List<Integer> operators = boardCardActivityEntities.stream().filter(boardCardActivityEntity -> BoardOperatorTypeEnum.USER.getOperator().equals(boardCardActivityEntity.getOperatorType()))
            .map(BoardCardActivityEntity::getOperator).filter(Objects::nonNull).map(Integer::valueOf).distinct().collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsUserIdToNameCache = fsAddressBookManager.getEmployeeInfoByUserIds(ea, operators, true);
        return boardCardActivityEntities.stream().map(boardCardActivityEntity -> {
            BoardCardActivityResult boardCardActivityResult = new BoardCardActivityResult();
            boardCardActivityEntityToBoardCardActivityResult(ea, boardCardActivityEntity, boardCardActivityResult, fsUserIdToNameCache);
            return boardCardActivityResult;
        }).collect(Collectors.toList());
    }

    /**
     * 将数据库查询的动态Entity封装为前端可展示的的Result型
     */
    public void boardCardActivityEntityToBoardCardActivityResult(String ea, BoardCardActivityEntity boardCardActivityEntity, BoardCardActivityResult boardCardActivityResult, Map<Integer, FSEmployeeMsg> fSEmployeeMsgCache) {
        if (StringUtils.isBlank(ea) || boardCardActivityEntity == null || boardCardActivityResult == null) {
            return ;
        }
        boardCardActivityResult.setActionType(boardCardActivityEntity.getActionType());
        boardCardActivityResult.setContentTitle(jsonToContentTitle(ea, boardCardActivityEntity.getOperator(), boardCardActivityEntity, fSEmployeeMsgCache, boardCardActivityEntity.getOperatorType()));
        //如果是评论类型
        if (BoardCardActivityActionTypeEnum.COMMENT.getActionType().equals(boardCardActivityEntity.getActionType())) {
            boardCardActivityResult.setContentDetail(boardCardActivityEntity.getContent().getComment());
            //头像信息
            FSEmployeeMsg employeeInfo = getFSEmployeeMsgFromCache(fSEmployeeMsgCache, ea, Integer.valueOf(boardCardActivityEntity.getOperator()));
            fsAddressBookManager.buildNPathProfileImage2Url(employeeInfo, ea);
            boardCardActivityResult.setCommentAvatar(employeeInfo.getProfileImage());
        }
        //卡片的备注
        else if (BoardCardActivityActionTypeEnum.UPDATE_CARD_DESCRIPTION.getActionType().equals(boardCardActivityEntity.getActionType())) {
            boardCardActivityResult.setContentDetail(boardCardActivityEntity.getContent().getBoardCardDescription());
        }
        boardCardActivityResult.setOperator(boardCardActivityEntity.getOperator());
        boardCardActivityResult.setCreateBySystem(boardCardActivityEntity.getCreateBySystem());
        boardCardActivityResult.setCreateTime(boardCardActivityEntity.getCreateTime() == null ? null : boardCardActivityEntity.getCreateTime().getTime());
    }


    private String jsonToContentTitle(String ea, String operator, BoardCardActivityEntity entity, Map<Integer, FSEmployeeMsg> fsUserIdToNameCache, String operatorType){
        StringBuilder stringBuilder = new StringBuilder();
        // 系统触发器创建的
        if (BoardOperatorTypeEnum.TRIGGER.getOperator().equals(operatorType)) {
            stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_957));
            // 触发器名称
            stringBuilder.append(triggerSnapshotDao.queryNameByTriggerId(ea, operator));
            stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_960));
        } else {
            //添加操作人信息
            stringBuilder.append(getNameByFsUserIdFromCache(fsUserIdToNameCache, ea, Integer.valueOf(operator)));
        }
        stringBuilder.append(" ");
        BoardCardActivityContentData contentData = entity.getContent();
        switch (BoardCardActivityActionTypeEnum.valueOf(entity.getActionType().toUpperCase())) {
            case CREATE_CARD:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_969));
                stringBuilder.append(contentData.getBoardCardName());
                break;
            case UPDATE_CARD_NAME:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_973));
                stringBuilder.append(contentData.getBoardCardName());
                break;
            case UPDATE_CARD_PRINCIPAL:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_977));
                if (contentData.getBoardCardPrincipal() == null) {
                    break;
                }
                stringBuilder.append(getNameByFsUserIdFromCache(fsUserIdToNameCache, ea, contentData.getBoardCardPrincipal()));
                break;
            case UPDATE_CARD_DESCRIPTION:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_984));
                break;
            case ASSOCIATE_CARD_MARKETING_EVENT:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_987));
                stringBuilder.append(contentData.getAssociateMarketingEventName());
                break;
            case ASSOCIATE_MARKETING_ACTIVITY:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_991));
                stringBuilder.append(contentData.getAssociateMarketingActivityName());
                break;
            case ASSOCIATE_DISTRIBUTE_PLAN:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_995));
                stringBuilder.append(contentData.getDistributePlanName());
                break;
            case MOVE_BOARD_CARD:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_999));
                stringBuilder.append(contentData.getMoveBoardCardFromBoardCardListName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1001));
                stringBuilder.append(contentData.getMoveBoardCardToBoardCardListName());
                stringBuilder.append("'");
                break;
            case SET_CARD_GOAL:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1006));
                stringBuilder.append(contentData.getBoardCardGoalTypeName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1008));
                stringBuilder.append(contentData.getBoardCardGoalValue());
                break;
            case CREATE_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1012));
                stringBuilder.append(contentData.getBoardCardTaskName());
                break;
            case FINISH_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1016));
                stringBuilder.append(contentData.getBoardCardTaskName());
                break;
            case DELETE_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1020));
                stringBuilder.append(contentData.getBoardCardTaskName());
                break;
            case REDO_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1024));
                stringBuilder.append(contentData.getBoardCardTaskName());
                break;
            case PULL_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1028));
                stringBuilder.append(contentData.getBoardCardTaskName());
                break;
            case UPDATE_TASK_NAME:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1032));
                stringBuilder.append(contentData.getOldBoardCardTaskName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1034));
                stringBuilder.append(contentData.getBoardCardTaskName()).append("'");
                break;
            case UPDATE_TASK_START_TIME:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1038));
                stringBuilder.append(contentData.getBoardCardTaskName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1040));
                stringBuilder.append(DateUtil.convertTimeToStringWithoutSecond(contentData.getUpdateBoardCardTaskStartTime()));
                break;
            case UPDATE_TASK_END_TIME:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1038));
                stringBuilder.append(contentData.getBoardCardTaskName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1046));
                stringBuilder.append(DateUtil.convertTimeToStringWithoutSecond(contentData.getUpdateBoardCardTaskEndTime()));
                break;
            case COMMENT:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1050));
                break;
            case ASSIGN_TASK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1053));
                stringBuilder.append(contentData.getBoardCardTaskName());
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1055));
                if (contentData.getBoardCardTaskExecutor() == null) {
                    break;
                }
                stringBuilder.append(getNameByFsUserIdFromCache(fsUserIdToNameCache, ea, contentData.getBoardCardTaskExecutor()));
                break;
            case CREATE_BOARD:
                // 获得看板名称
                String boardName = boardDao.getBoardNameByBoardId(entity.getBoardId(), ea);
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1064)).append(boardName);
                break;
            case DELETE_BOARD:
                String name = boardDao.getBoardNameByBoardId(entity.getBoardId(), ea);
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1068)).append(name);
                break;
            case FINISH_WORK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1071)).append(contentData.getBoardCardName()).append("'");
                break;
            case UNDO_WORK:
                stringBuilder.append(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_1074)).append(contentData.getBoardCardName()).append("'");
                break;
            default:
                log.warn("存在错误的actionType类型");
                break;
        }
        return stringBuilder.toString();
    }

    /**
     * 根据operator查找cache，如果不存在，则查找并添加到cache中
     * @param fSEmployeeMsgCache 缓存
     * @return operator对应的名称
     */
    private String getNameByFsUserIdFromCache(Map<Integer, FSEmployeeMsg> fSEmployeeMsgCache, String ea, Integer operator) {
        if (operator == null || fSEmployeeMsgCache == null) {
            return "";
        }
        if (operator == -10000){
            return I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
        }
        FSEmployeeMsg value = fSEmployeeMsgCache.get(operator);
        if (value == null) {
            value = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(operator), true).get(operator);
            if (value == null) {
                value = new FSEmployeeMsg();
                value.setName("");
            }
            fSEmployeeMsgCache.put(operator, value);
        }
        return value.getName();
    }

    private FSEmployeeMsg getFSEmployeeMsgFromCache(Map<Integer, FSEmployeeMsg> fSEmployeeMsgCache, String ea, Integer operator) {
        if (operator == null || fSEmployeeMsgCache == null) {
            return new FSEmployeeMsg();
        }
        FSEmployeeMsg fsEmployeeMsg = fSEmployeeMsgCache.get(operator);
        if (fsEmployeeMsg == null) {
            fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(ea, operator);
            if (fsEmployeeMsg == null) {
                fsEmployeeMsg = new FSEmployeeMsg();
            }
            fSEmployeeMsgCache.put(operator, fsEmployeeMsg);
        }
        return fsEmployeeMsg;
    }

    public Map<String, BoardCardResult> doConvertBoardEntityToResult(Collection<BoardCardEntity> boardCards) {
        Map<String, BoardCardResult> resultMap = Maps.newHashMap();
        List<BoardEntity> boardEntities = boardDao.getBoardNameByIds(boardCards.stream().map(BoardCardEntity::getBoardId).collect(Collectors.toList()));
        List<BoardCardListEntity> boardCardListEntities = boardCardListDao.getBoardCardListNameByIds(boardCards.stream().map(BoardCardEntity::getBoardCardListId).collect(Collectors.toList()));
        Map<String, BoardEntity> boardEntityMap = boardEntities.stream().collect(Collectors.toMap(BoardEntity::getId, data -> data));
        Map<String, BoardCardListEntity> boardCardListEntityMap = boardCardListEntities.stream().collect(Collectors.toMap(BoardCardListEntity::getId, data -> data));
        for (BoardCardEntity boardCard : boardCards) {
            BoardCardResult boardCardResult = new BoardCardResult();
            BeanUtils.copyProperties(boardCard, boardCardResult, "startTime", "endTime");
            if (boardCard.getStartTime() != null) {
                boardCardResult.setStartTime(boardCard.getStartTime().getTime());
            }
            if (boardCard.getEndTime() != null) {
                boardCardResult.setEndTime(boardCard.getEndTime().getTime());
            }
            BoardEntity boardEntity = boardEntityMap.get(boardCard.getBoardId());
            BoardCardListEntity boardCardListEntity = boardCardListEntityMap.get(boardCard.getBoardCardListId());
            boardCardResult.setBoardName(boardEntity != null ? boardEntity.getName() : null);
            boardCardResult.setBoardCardListName(boardCardListEntity != null ? boardCardListEntity.getName() : null);
            resultMap.put(boardCard.getId(), boardCardResult);
        }
        return resultMap;
    }

    public void sendAddAsBoardCardPrincipalNotification(String ea, Integer fsUserId, String boardCardId, Set<Integer> principals) {
        BoardCardEntity boardCard = boardCardDao.getById(boardCardId, ea);
        if (boardCard != null){
            BoardEntity board = boardDao.getBoardByBoardId(boardCard.getBoardId(), ea);
            if (board != null){
                OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
                infoTitle.setContent("看板任务提醒");
                infoTitle.setInternationalContent("qx.ot.mark.kanban_task_reminder");
                List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
                content.add(OfficeMessageArg.LabelWarp.newInstance("描述", "qx.ot.mark.remark",
                        String.format("%s 将你设置为工作项负责人", fsAddressBookManager.getEmployeeNameById(ea, fsUserId)), "qx.ot.mark.owner_notice"));
                content.add(OfficeMessageArg.LabelWarp.newInstance("看板", "qx.ot.mark.kanban", board.getName()));
                content.add(OfficeMessageArg.LabelWarp.newInstance("工作项", "qx.ot.mark.task", boardCard.getName()));
                String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail&pageParams=${pageParams}&ea=" + ea + "#/notice/p";
                Map<String, String> pageParams = new HashMap<>(1);
                pageParams.put("id", boardCardId);
                fsUrl = fsUrl.replace("${pageParams}", UrlUtils.urlEncode(GsonUtil.toJson(pageParams)));
                employeeMsgSender.sendUnionMessageAsync(ea, -10000, principals, infoTitle, null, content, fsUrl,"cml://cmlMarketing/kanban_detail?id=${id}&id=${id}".replace("${id}", boardCardId), "/pkgs/pkg-kanban/pages/detail/detail?id=" + boardCardId);
            }
        }
    }

    public void sendAddAsBoardCardTaskExecutorNotification(String ea, Integer fsUserId, String boardCardTaskId, Integer executor) {
        BoardCardTaskEntity boardCardTask = boardCardTaskDao.getById(boardCardTaskId);
        if (boardCardTask != null){
            BoardCardEntity boardCard = boardCardDao.getById(boardCardTask.getBoardCardId(), ea);
            if (boardCard != null){
                BoardEntity board = boardDao.getBoardByBoardId(boardCardTask.getBoardId(), ea);
                if (board != null){
                    OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
                    infoTitle.setContent("看板任务提醒");
                    infoTitle.setInternationalContent("qx.ot.mark.kanban_task_reminder");
                    List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
                    content.add(OfficeMessageArg.LabelWarp.newInstance("描述", "qx.ot.mark.remark",
                            String.format("%s 把任务“%s”指派给你", fsAddressBookManager.getEmployeeNameById(ea, fsUserId), boardCardTask.getName()), "qx.ot.mark.task_notice"));
                    content.add(OfficeMessageArg.LabelWarp.newInstance("看板", "qx.ot.mark.kanban", board.getName()));
                    content.add(OfficeMessageArg.LabelWarp.newInstance("工作项", "qx.ot.mark.task", boardCard.getName()));
                    String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail&pageParams=${pageParams}&ea=" + ea + "#/notice/p";
                    Map<String, String> pageParams = new HashMap<>(1);
                    pageParams.put("id", boardCardTask.getBoardCardId());
                    fsUrl = fsUrl.replace("${pageParams}", UrlUtils.urlEncode(GsonUtil.toJson(pageParams)));
                    employeeMsgSender.sendUnionMessageAsync(ea, -10000, ImmutableList.of(executor), infoTitle, null, content, fsUrl, "cml://cmlMarketing/kanban_detail?id=${id}&id=${id}".replace("${id}", boardCardTask.getBoardCardId()), "/pkgs/pkg-kanban/pages/detail/detail?id=" + boardCardTask.getBoardCardId());
                }
            }
        }
    }

    /**
     * 当创建卡片时需要添加的动态
     */
    public boolean addBoardCardActivityWhenCreateBoardCard(String ea, String operator, AddBoardCardArg addBoardCardArg, String boardId, String cardId, boolean createBySystem, String operatorType) {
        //添加看板动态之创建卡片
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setBoardCardName(addBoardCardArg.getName());
        addBoardCardActivity(ea, operator, boardId, cardId, BoardCardActivityActionTypeEnum.CREATE_CARD.getActionType(), contentData, createBySystem, operatorType);
        contentData.setBoardCardName(null);
        //添加看板动态之关联市场活动
        if (!Strings.isNullOrEmpty(addBoardCardArg.getMarketingEventId())) {
            MarketingEventData marketingEventData = null;
            try {
                marketingEventData = marketingEventManager.getMarketingEventData(ea, Integer.valueOf(operator), addBoardCardArg.getMarketingEventId());
            } catch (Exception e) {
                log.warn("BoardManager.addBoardCardActivityWhenCreateBoardCard getMarketingEventData method is error : e", e);
            }
            if (marketingEventData != null) {
                contentData.setAssociateMarketingEventName(marketingEventData.getName());
                addBoardCardActivity(ea, operator, boardId, cardId, BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType(), contentData, createBySystem, operatorType);
                contentData.setAssociateMarketingEventName(null);
            }
        }
        //添加看板动态之设置目标
        if (!Strings.isNullOrEmpty(addBoardCardArg.getGoalType()) && addBoardCardArg.getGoalValue() != null) {
            contentData.setBoardCardGoalValue(addBoardCardArg.getGoalValue());
            contentData.setBoardCardGoalTypeName(addBoardCardArg.getGoalType());
            addBoardCardActivity(ea, operator, boardId, cardId, BoardCardActivityActionTypeEnum.SET_CARD_GOAL.getActionType(), contentData, createBySystem, operatorType);
            contentData.setBoardCardGoalValue(null);
            contentData.setBoardCardGoalTypeName(null);
        }
        return true;
    }

    private boolean addBoardCardActivityWhenCreateBoardCard(String ea, Integer fsUserId, List<BoardCardEntity> boardCards, String boardId, boolean createBySystem) {
        if (CollectionUtils.isEmpty(boardCards)) {
            return false;
        }
        Map<String, MarketingEventData> marketingEventDataIdToMarketingEventData = null;
        for (BoardCardEntity boardCard : boardCards) {
            //添加看板动态之创建卡片
            BoardCardActivityContentData contentData = new BoardCardActivityContentData();
            contentData.setBoardCardName(boardCard.getName());
            addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, boardCard.getId(), BoardCardActivityActionTypeEnum.CREATE_CARD.getActionType(), contentData, createBySystem,
                BoardOperatorTypeEnum.USER.getOperator());
            contentData.setBoardCardName(null);
            //添加看板动态之关联市场活动
            if (!Strings.isNullOrEmpty(boardCard.getMarketingEventId())) {
                try {
                    if (marketingEventDataIdToMarketingEventData == null) {
                        List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventData(ea, fsUserId, boardCards.stream().map(BoardCardEntity::getMarketingEventId).collect(Collectors.toList()));
                        marketingEventDataIdToMarketingEventData = marketingEventDataList.stream().filter(Objects::nonNull).collect(Collectors.toMap(MarketingEventData::getId, Function.identity(), (k1, k2) -> k1));
                    }
                    MarketingEventData marketingEventData = marketingEventDataIdToMarketingEventData.get(boardCard.getMarketingEventId());
                    if (marketingEventData != null) {
                        contentData.setAssociateMarketingEventName(marketingEventData.getName());
                        addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, boardCard.getId(), BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType(), contentData,
                            createBySystem, BoardOperatorTypeEnum.TRIGGER.getOperator());
                        contentData.setAssociateMarketingEventName(null);
                    }
                } catch (OuterServiceRuntimeException e) {
                    log.warn("BoardManager.addBoardCardActivityWhenCreateBoardCard", e);
                }
            }
            //添加看板动态之设置目标
            if (!Strings.isNullOrEmpty(boardCard.getGoalType()) && boardCard.getGoalValue() != null) {
                contentData.setBoardCardGoalValue(boardCard.getGoalValue());
                contentData.setBoardCardGoalTypeName(boardCard.getGoalType());
                addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, boardCard.getId(), BoardCardActivityActionTypeEnum.SET_CARD_GOAL.getActionType(), contentData, createBySystem, BoardOperatorTypeEnum.USER
                    .getOperator());
                contentData.setBoardCardGoalValue(null);
                contentData.setBoardCardGoalTypeName(null);
            }
        }
        return true;
    }

    public boolean addBoardCardActivityWhenAddBoardCardTask(String ea, String operator, String boardId, AddBoardCardTaskArg arg, boolean createBySystem, String operatorType) {
        //添加看板动态之创建任务
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setBoardCardTaskName(arg.getName());
        addBoardCardActivity(ea, operator, boardId, arg.getBoardCardId(), BoardCardActivityActionTypeEnum.CREATE_TASK.getActionType(), contentData, createBySystem, operatorType);
        //添加看板动态之指派任务或认领任务
        if (arg.getExecutor() != null && !arg.getExecutor().equals(1)) {
            contentData.setBoardCardTaskExecutor(arg.getExecutor());
            //如果任务的处理人是自己，则为认领任务
            if (arg.getExecutor().equals(Integer.valueOf(operator))) {
                addBoardCardActivity(ea, operator, boardId, arg.getBoardCardId(), BoardCardActivityActionTypeEnum.PULL_TASK.getActionType(), contentData, createBySystem, operatorType);
            }
            //如果任务的处理人是他人，则为指派任务
            else {
                addBoardCardActivity(ea, operator, boardId, arg.getBoardCardId(), BoardCardActivityActionTypeEnum.ASSIGN_TASK.getActionType(), contentData, createBySystem, operatorType);
            }
        }
        return true;
    }

}