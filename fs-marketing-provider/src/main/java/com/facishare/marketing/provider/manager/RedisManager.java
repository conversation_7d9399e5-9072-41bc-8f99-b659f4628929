package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.PreviewArticleTempValue;
import com.facishare.marketing.api.arg.RecordUtmParamArg;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.model.RedisKISApplyInfoEntity;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.innerData.GlobalCRMStatisticalData;
import com.facishare.marketing.provider.innerData.MiniAppVisitData;
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult;
import com.facishare.marketing.provider.manager.sms.RedisSendSmsCheckValue;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

//@Service
@Component
@Slf4j
public class RedisManager {

    private Gson gs = new Gson();

    /** 保存上传图片的尺寸 */
    private static final String PIC_UPLOAD_SIZE_KEY = "KM_UPLOAD_PIC_";
    /** 保存上传图片的尺寸时间1小时 */
    private static final int PIC_UPLOAD_SIZE_TIME = 3600;
    private static final String SET_IF_NOT_EXIST = "NX";//仅在键不存在时设置键。
    private static final String SET_WITH_EXPIRE_TIME = "EX";//PX表示超时时间是毫秒设置，EX表示超时时间是为 second 秒
    private static final Long RELEASE_SUCCESS = 1L;

    private ThreadLocal<String> threadLocal = new ThreadLocal<>();

    /** 保存预览文章的时间为 2小时 */
    private static final int PREVIEW_ARTICLE_TEMP_TIME = 2 * 3600;


    /** 预览文章key前缀 */
    private static final String PREVIEW_ARTICLE_TEMP_PRE = "PREVIEW_ARTICLE_TEMP_PRE";

    //KIS侧用户信息临时数据key前缀
    private static final String KIS_APPLY_INFO_PRE = "KM_KIS_APPLY_INFO_";

    //KIS侧用户信息临时数据 过期时间1小时
    private static final int KIS_APPLY_INFO_EXPIRED_TIME = 3600;

    //KIS侧名片海报url数据key前缀
    private static final String KIS_CARD_POSTER_PRE = "KIS_CARD_POSTER_PRE";

    //KIS侧名片海报url数据 过期时间15分钟
    private static final int KIS_CARD_POSTER_EXPIRED_TIME = 900;

    //HEXAGON站点数据key前缀
    private static final String HEXAGON_SITE_INFO_PRE = "HEXAGON_SITE_INFO_";
    //HEXAGON站点数据 过期时间1小时
    private static final int HEXAGON_SITE_INFO_EXPIRED_TIME = 3600;

    //HEXAGON页面数据key前缀
    private static final String HEXAGON_PAGE_INFO_PRE = "HEXAGON_PAGE_INFO_";
    //HEXAGON页面数据 过期时间1小时
    private static final int HEXAGON_PAGE_INFO_EXPIRED_TIME = 3600;

    //HEXAGON首页数据key前缀
    private static final String HEXAGON_HOMEPAGE_INFO_PRE = "HEXAGON_HOMEPAGE_INFO_";
    //HEXAGON首页数据 过期时间1小时
    private static final int HEXAGON_HOMEPAGE_INFO_EXPIRED_TIME = 3600;

    // 已完成流程实例id
    private static final String FINISHED_FLOW_INSTANCE_ID_PREFIX = "FINISHED_FLOW_INSTANCE_ID:";
    // 已完成实实例过期时间
    private static final long FINISHED_FLOW_INSTANCE_ID_EXPIRED_TIME_S = 600L;

    //企业微信应用access token key
    private static final String QYWX_ACCESS_TOKEN_PRE = "QYWX_ACCESS_TOKEN_";
    //企业微信应用access token key 过期时间2小时
    private static final int QYWX_ACCESS_TOKEN_EXPIRED_TIME = 7200;

    //企业微信小程序应用access token key 存储，用于识别自建应用的token
    private static final String QYWX_SELFAPP_ACCESS_TOKEN_STORE_PRE = "QYWX_SELFAPP_ACCESS_TOKEN_STORE_";

    //企业微信代开发suittoken
    private static final String QYWX_SUIT_ACCESS_TOKEN_PRE = "QYWX_SUIT_ACCESS_TOKEN_";
    //企业微信代开发suittoken key 过期时间2小时
    private static final int QYWX_SUIT_ACCESS_TOKEN_EXPIRED_TIME = 7200;

    //企业微信服务商access token key
    private static final String QYWX_PROVIDER_ACCESS_TOKEN_PRE = "QYWX_PROVIDER_ACCESS_TOKEN_";
    //企业微信服务商access token key 过期时间2小时
    private static final int QYWX_PROVIDER_ACCESS_TOKEN_EXPIRED_TIME = 7200;

    //企业微信代开发带参授权链接 qrcodeUrl key
    private static final String QYWX_QRCODE_URL_PRE = "QYWX_QRCODE_URL_";
    //企业微信代开发带参授权链接 qrcodeUrl key 过期时间10天
    private static final int QYWX_QRCODE_URL_EXPIRED_TIME = 3600 * 24 * 10;

    //小鹅通应用access token key
    private static final String XIAOETONG_ACCESS_TOKEN_PRE = "XIAOETONG_ACCESS_TOKEN_";
    //企业微信应用access token key 过期时间2小时
    private static final int XIAOETONG_ACCESS_TOKEN_EXPIRED_TIME = 7200;

    //目睹access token key
    private static final String MUDU_ACCESS_TOKEN_PRE = "MUDU_ACCESS_TOKEN_";
    //目睹access token key 过期时间1小时,提前2分钟到期
    private static final int MUDU_ACCESS_TOKEN_EXPIRED_TIME = 3600 - 120;

    //钉钉小程序accessToken
    private static final String DING_MINIAPP_ACCESS_TOKEN_PRE = "DING_MINIAPP_ACCESS_TOKEN_";
    // 钉钉小程序登录用户的访问凭证userAccessToken
    private static final String DING_MINIAPP_USER_ACCESS_TOKEN_PRE = "DING_MINIAPP_USER_ACCESS_TOKEN_PRE";
    //钉钉小程序企业部门
    private static final String DING_MINIAPP_DEPARTMENT = "DING_MINIAPP_DEPARTMENT";

    //企业微信企业的jsapi_ticket key
    private static final String CORP_JSAPI_TICKET_PRE = "CORP_JSAPI_TICKET_";
    //企业微信企业的jsapi_ticket过期时间2小时
    private static final int CORP_JSAPI_TICKET_EXPIRED_TIME = 7200;

    //企业微信应用的jsapi_ticket key
    private static final String AGENT_JSAPI_TICKET_PRE = "AGENT_JSAPI_TICKET_";
    //企业微信应用的jsapi_ticket过期时间2小时
    private static final int AGENT_JSAPI_TICKET_EXPIRED_TIME = 7200;

    //企业微信,内建应用重定向url key
    private static final String AGENT_AUTH_REDIRECT_URL_PRE = "AGENT_AUTH_REDIRECT_URL_";
    //企业微信,内建应用重定向url过期时间60秒
    private static final int AGENT_AUTH_REDIRECT_URL_EXPIRED_TIME = 180;

    //企业微信群
    private static final String QYWX_CUSTOMER_GROUP_PRE = "QYWX_CUSTOMER_GROUP_";
    private static final int QYWX_CUSTOMER_GROUP_EXPIRED_TIME = 7200;

    //市场活动关联的线索总数
    private static final String MARKETING_EVENT_ID_CLUE_COUNT_PRE = "MARKETING_EVENT_CLUE_COUNT_";
    private static final int MARKETING_EVENT_ID_CLUE_COUNT_EXPIRED_TIME = 600;

    // 企业微信员工详情
    private static final String QYWX_STAFF_DETAIL = "MARKETING_QYWX_STAFF_DETAIL";
    private static final int QYWX_STAFF_DETAIL_EXPIRED_TIME = 7200;

    //企业微信素材media id
    private static final String QYWX_MEDIA_ID_PRE = "QYWX_MEDIA_ID_";
    private static final int QYWX_MEDIA_ID_EXPIRED_TIME = 3 * 24 * 3600 - 60 * 10;

    //企业微信附件素材media id
    private static final String MARKETING_QYWX_ATTACHMENT_MEDIA_ID_PRE = "QYWX_ATTACHMENT_MEDIA_ID_";
    private static final int MARKETING_QYWX_ATTACHMENT_MEDIA_ID_EXPIRED_TIME = 3 * 24 * 3600;

    // 企微userid明文和密文转换
    private static final String QYWX_USER_ID_CONVERT_PRE = "QYWX_USER_ID_CONVERT_";
    private static final long QYWX_USER_ID_CONVERT_EXPIRED_TIME = 3600 * 24 * 30;

    //CRM对象统计数据
    private static final String CRM_OBJECT_STATISTICS_PRE = "MARKETING_CRM_OBJECT_STATISTICS_";
    private static final int CRM_OBJECT_STATISTICS_EXPIRED_TIME = 3600 * 8;

    //广告
    private static final String MARKETING_ADVERTISE_PRE="MARKETING_ADVERTISE_";
    private static final int ADVERTISE_EXPIRED_TIME = 24 * 3600 - 600;


    /** 新小程序相关 */
    //sessionKey的前缀
    private static final String KM_NEW_UID_2_SESSIONKEY_PRE = "KM_NEW_UID_2_SESSIONKEY_";
    //sessionKey的过期时间
    private static final int KM_NEW_UID_2_SESSIONKEY_EXPIRED_TIME = 3600 * 24 * 7;

    //dingding sessionKey的前缀
    private static final String DING_DING_NEW_UID_2_SESSIONKEY_PRE = "DING_NEW_UID_2_SESSIONKEY_";
    //dingding sessionKey的过期时间
    private static final int DING_DING_NEW_UID_2_SESSIONKEY_EXPIRED_TIME = 3600 * 24 * 7;

    //yqwxh5 sessionKey的前缀
    private static final String QYWX_H5_UID_2_SESSIONKEY_PRE = "QYWX_H5_UID_2_SESSIONKEY_";
    //yqwxh5 sessionKey的过期时间
    private static final int QYWX_H5_UID_2_SESSIONKEY_EXPIRED_TIME = 3600 * 24 ;


    // 客脉应用access token key
    public static final String KEMAI_ACCESS_TOKEN = "MARKETING_KEMAI_ACCESS_TOKEN_KEY";

    // 客脉pro应用access token key
    public static final String KEMAI_PRO_ACCESS_TOKEN = "MARKETING_KEMAI_PRO_ACCESS_TOKEN_KEY";

    public static final String PRE_BOUND_WX_APP_ID = "PRE_BOUND_WX_APP_ID_";

    //小程序应用access token key 过期时间2小时 - 10 分钟
    public static final int ACCESS_TOKEN_EXPIRED_TIME = 6600;

    // 第三方平台托管小程序，更新/发布/撤回操作key
    public static final String WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO = "WX_THIRD_PLATFORM_MINI_APP_UPDATE_OR_RELEASE_OR_UNDO";

    /**巨量引擎相关**/
    // 巨量引擎access_token过期时间 23h50m
    public static final int HEADLINES_ACCESS_TOKEN_EXPIRED_TIME = 85800;

    // 巨量引擎access_token key
    public static final String HEADLINES_ACCESS_TOKEN = "HEADLINES_ACCESS_TOKEN";

    public static final String DING_SUITE_TICKET_PRE = "DING_SUITE_TICKET_";

    /**钉钉小程序suiteTicket**/
    public static final int DING_SUITE_TICKET_EXPIRED_TIME = 6 * 3600;
    // 钉钉小程序用户访问凭证userAccessToken过期时间
    public static final int DING_USER_ACCESS_TOKEN_EXPIRED_TIME = 7200-300;

    /**小程序访问数据相关**/
    public static final String MINIAPP_TOTAL_USER_DATA_PRE = "MINIAPP_TOTALUSER_DATA_";
    public static final String MINIAPP_VISIT_DATA_PRE = "MINIAPP_VISIT_DATA_";
    public static final int MINIAPP_VISIT_DATA_EXPIRED_TIME = 24 * 3600 * 2;

    /**官网引流吸粉二维码**/
    public static final String OFFICIAL_WEBSITE_ADD_FAN_QR_CODE_PRE = "OW_ADD_FAN_QR_CODE_";
    public static final int OFFICIAL_WEBSITE_ADD_FAN_QRCODE_TIME = 24 * 3600 * 2;

    /**添加参会人员**/
    private static final String ADD_CAMPAIGN_MEMBER_OBJ = "ADD_CAMPAIGN_MEMBER_";
    private static final int ADD_CAMPAIGN_MEMBER_EXPIRED_TIME = 60 * 10;

    /**
     * 公众号扫码登陆会员信息
     */
    private static final String WX_ACCOUNT_MEMBER_INFO = "WX_ACCOUNT_MEMBER_INFO_";
    private static final int WX_ACCOUNT_MEMBER_INFO_EXPIRED_TIME = 60 * 10;

    private static final String MARKETING_PRIMARY_ID_KEY = "MARKETING_PRIMARY_ID";

    /**
     * 灰度企业获取api
     * **/
    private static final String MARKETING_GRAY_EA_INFO = "MARKETING_GRAY_EA_INFO_";

    private static final int MARKETING_GRAY_EA_INFO_EXPIRED_TIME = 3600 * 8;

    /**官网访客utm参数记录**/
    public static final String OFFICIAL_WEBSITE_VISITOR_UTM_RECORD = "OFFICIAL_WEBSITE_VISITOR_UTM_RECORD_";

    public static final int OFFICIAL_WEBSITE_VISITOR_UTM_RECORD_TIME = 60 * 60 * 24 * 7;

    //redis中的验证phone前缀
    private static final String PHONE_PRE = "PHONE_";
    //手机验证过期时间
    private static final int PHONE_TIME = 60 * 5;


    /**path转cpath缓存,这里传入的怕path可能是cpath或者apath**/
    public static final String PATH_TO_CPATH = "MARKETING_PATH_TO_CPATH_";

    public static final int PATH_TO_CPATH_TIME = 60 * 60 * 24 * 7;

    public static final int IMAGE_SIZE_TIME = 60 * 30;

    public static final int ENTERPRISE_INFO_STATUS_TIME = 60 * 60;


    private final static String AD_BIND_KEY = "mk:ad:bind:%s";

    private final static String EA_SECRET_KEY = "mk:secret:%s";

    public static final String SHANSHAN_TOKEN = "SHANSHAN_TOKEN_";


    private static final String QYWX_USER_ID_TO_OPEN_USER_ID = "QYWX_OPEN_USER_ID_TO_USER_ID_";

    private static final long QYWX_USER_ID_TO_OPEN_USER_ID_EXPIRED_TIME = 3600 * 24 * 30;

    private String ENTERPRISE_STATUS_PREFIX_KEY = "mk:corp:status:%s";

    private static final long ENTERPRISE_STATUS_EXPIRED_TIME = 3600 * 24;

    private static final String CALCULATE_ATTRIBUTE_DATA_PRE = "mk:cal:attribute:%s:%s";

    private static final String CALCULATE_MARKETING_EVENT_LEAD_DATA_PRE = "mk:cal:marketingEvent:lead:%s:%s";

    private static final String WHATS_APP_SEND_MESSAGE_PRE = "mk:whatsapp:send:%s:%s";

    private static final String MEMBER_MARKETING_NOTICE_PRE = "mk:member:notice:%s:%s";

    private static final String CAMPAIGN_MEMBER_STATUS_CHANGE = "mk:cam:mem:%s:%s";

    private static final String INIT_QYWX_DEPARTMENT_PRE = "mk:qywx:dep:init:%s";

    private static final String QYWX_DEPARTMENT_CHANGE_PRE = "mk:qywx:dep:change:%s:%s";

    private static final long TWO_HOURS = 3600 * 2;

    private static final long FIVE_MINUTES = 60 * 5;

    private static final long TEN_MINUTES = 60 * 10;

    // 抖音开发平台的AccessTokenKey的前缀
    private static final String DOU_YIN_ACCESS_TOKEN_KEY_PRE = "DOU_YIN_ACCESS_TOKEN_KEY_";

    // 抖音开发平台token的过期时间，官网那边是2个小时过期，在redis这边设置1个销售过期时间
    private static final int DOU_YIN_ACCESS_TOKEN_EXPIRE_TIME = 3600;


    private static final String AD_REFRESH_DATA_KEY = "mk:ad:refresh:%s:%s";

    private static final String MULTI_EVENT_SYSTEM_HEXAGON_KEY = "mk:mul:hexagon:%s:%s";

    private static final String MAIL_EA_STATISTIC_JOB_KEY = "mk:mail:sta:job:%s";

    private static final String MAIL_TASK_STATISTIC_JOB_KEY = "mk:mail:sta:task:%s:%s";

    private final ThreadLocal<Map<String, String>> lockMapForThread = new ThreadLocal<>();

    private static final int MAX_LOCKS_PER_THREAD = 100;

    @Autowired
    private MergeJedisCmd jedisCmd;

    public void setPreBoundWxAppId(String ea, String preWxAppId){
        jedisCmd.setex(PRE_BOUND_WX_APP_ID + ea, 10 * 24 * 60 * 60, preWxAppId);
    }

    public String getPreBoundWxAppId(String ea){
        String preBoundWxAppId = jedisCmd.get(PRE_BOUND_WX_APP_ID + ea);
        if (Strings.isNullOrEmpty(preBoundWxAppId)){
            return WxAppInfoEnum.MankeepPro.getAppId();
        }
        return preBoundWxAppId;
    }

    public boolean isMarketingFlowInstancePassed(String marketingFlowInstanceId){
        return jedisCmd.set(FINISHED_FLOW_INSTANCE_ID_PREFIX + marketingFlowInstanceId, "1", "NX", "EX", FINISHED_FLOW_INSTANCE_ID_EXPIRED_TIME_S) == null;
    }

    public void setPreviewArticleTempValue(String previewArticleId, PreviewArticleTempValue previewArticleTempValue){

        String key = PREVIEW_ARTICLE_TEMP_PRE + previewArticleId;
        String value = gs.toJson(previewArticleTempValue);
        jedisCmd.setex(key, PREVIEW_ARTICLE_TEMP_TIME, value);
    }

    public String getPreviewArticleTempValue(String previewArticleId){
        String key = PREVIEW_ARTICLE_TEMP_PRE + previewArticleId;
        return jedisCmd.get(key);
    }

    public void setUploadPicSize(String path, String size) {
        String key = PIC_UPLOAD_SIZE_KEY + path;
        jedisCmd.setex(key, PIC_UPLOAD_SIZE_TIME, size);
    }

    public String getUploadPicSize(String path) {
        String key = PIC_UPLOAD_SIZE_KEY + path;
        return jedisCmd.get(key);
    }

    public void setMiniappTotalUserData(String appid, String endTime, Long totalUser){
        if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(endTime) || totalUser == null){
            return;
        }

        String key = MINIAPP_TOTAL_USER_DATA_PRE + appid + "_" + endTime;
        String value = String.valueOf(totalUser);
        jedisCmd.setex(key, MINIAPP_VISIT_DATA_EXPIRED_TIME, value);
    }

    public Long getMiniappTotalUserData(String appid, String endTime){
        String key = MINIAPP_TOTAL_USER_DATA_PRE + appid + "_" + endTime;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }
        return Long.parseLong(value);
    }

    public void setMiniappVisitData(String appid, String dateType, MiniAppVisitData miniAppVisitData){
        if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(dateType) || miniAppVisitData == null){
            return;
        }

        String key = MINIAPP_VISIT_DATA_PRE + appid + "_" + dateType;
        String value = gs.toJson(miniAppVisitData);
        jedisCmd.setex(key, MINIAPP_VISIT_DATA_EXPIRED_TIME, value);
    }

    public MiniAppVisitData getMiniappVisitData(String appid, String dateType){
        String key = MINIAPP_VISIT_DATA_PRE + appid + "_" + dateType;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }

        return gs.fromJson(value, MiniAppVisitData.class);
    }

    public void setKISApplyInfoToRedis(String applyInfoKey, RedisKISApplyInfoEntity redisKISApplyInfoEntity) {
        if (redisKISApplyInfoEntity == null || applyInfoKey == null){
            return;
        }

        String key = KIS_APPLY_INFO_PRE + applyInfoKey;
        String value = gs.toJson(redisKISApplyInfoEntity);
        jedisCmd.setex(key, KIS_APPLY_INFO_EXPIRED_TIME, value);
    }



    public RedisKISApplyInfoEntity getKISApplyInfoFromRedis(String applyInfoKey){
        String key = KIS_APPLY_INFO_PRE + applyInfoKey;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }
        return gs.fromJson(value, RedisKISApplyInfoEntity.class);
    }

    public String getKISCardPoster(String cardUid) {
        String key = KIS_CARD_POSTER_PRE + cardUid;
        return jedisCmd.get(key);
    }

    public void setKISCardPosterToRedis(String cardUid, String url) {
        if (StringUtils.isBlank(url)) {
            return;
        }

        String key = KIS_CARD_POSTER_PRE + cardUid;
        jedisCmd.setex(key, KIS_CARD_POSTER_EXPIRED_TIME, url);
    }

    public boolean setHexagonSiteToRedis(String siteId, HexagonSiteEntity hexagonSiteEntity) {
        if (hexagonSiteEntity == null || StringUtils.isBlank(siteId)){
            return false;
        }

        String key = HEXAGON_SITE_INFO_PRE + siteId;
        String value = gs.toJson(hexagonSiteEntity);
        String result = jedisCmd.setex(key, HEXAGON_SITE_INFO_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    public boolean deleteHexgonSite(String siteId) {
        if (StringUtils.isBlank(siteId)){
            return false;
        }

        String key = HEXAGON_SITE_INFO_PRE + siteId;
        Long result = jedisCmd.del(key);
        return result > 0;
    }

    public void deleteHexagonSiteBatch(List<String> siteIdList) {
        if (CollectionUtils.isEmpty(siteIdList)){
           return;
        }
        jedisCmd.pipeline(pipelineCmd -> {
            for (String siteId : siteIdList) {
                pipelineCmd.del(HEXAGON_SITE_INFO_PRE + siteId);
            }
        });
    }

    public HexagonSiteEntity getHexagonSite(String siteId){
        if (StringUtils.isBlank(siteId)){
            return null;
        }

        String key = HEXAGON_SITE_INFO_PRE + siteId;
        String value =  jedisCmd.get(key);
        return gs.fromJson(value, HexagonSiteEntity.class);
    }

    public boolean setHexagonPageToRedis(String pageId, HexagonPageEntity hexagonPageEntity) {
        if (hexagonPageEntity == null || StringUtils.isBlank(pageId)){
            return false;
        }

        String key = HEXAGON_PAGE_INFO_PRE + pageId;
        String value = gs.toJson(hexagonPageEntity);
        String result = jedisCmd.setex(key, HEXAGON_PAGE_INFO_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    public boolean deleteHexgonPage(String pageId) {
        if (StringUtils.isBlank(pageId)){
            return false;
        }

        String key = HEXAGON_PAGE_INFO_PRE + pageId;
        Long result = jedisCmd.del(key);
        return result > 0;
    }

    public HexagonPageEntity getHexagonPage(String pageId){
        if (StringUtils.isBlank(pageId)){
            return null;
        }

        String key = HEXAGON_PAGE_INFO_PRE + pageId;
        String value =  jedisCmd.get(key);
        return gs.fromJson(value, HexagonPageEntity.class);
    }

    public boolean setHexagonHomePageToRedis(String siteId, HexagonPageEntity hexagonPageEntity) {
        if (hexagonPageEntity == null || StringUtils.isBlank(siteId)){
            return false;
        }

        String key = HEXAGON_HOMEPAGE_INFO_PRE + siteId;
        String value = gs.toJson(hexagonPageEntity);
        String result = jedisCmd.setex(key, HEXAGON_HOMEPAGE_INFO_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    @FilterLog
    public HexagonPageEntity getHexagonHomePage(String siteId){
        if (StringUtils.isBlank(siteId)){
            return null;
        }

        String key = HEXAGON_HOMEPAGE_INFO_PRE + siteId;
        String value =  jedisCmd.get(key);
        return gs.fromJson(value, HexagonPageEntity.class);
    }

    public boolean deleteHexagonHomePage(String siteId) {
        if (StringUtils.isBlank(siteId)){
            return false;
        }
        String key = HEXAGON_HOMEPAGE_INFO_PRE + siteId;
        Long result = jedisCmd.del(key);
        return result > 0;
    }

    public boolean setQywxAccessToken(String corpId, String agentId, String accessToken, Integer expiredTime) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(accessToken)) {
            return false;
        }
        String key = QYWX_ACCESS_TOKEN_PRE + corpId + "_" + agentId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : QYWX_ACCESS_TOKEN_EXPIRED_TIME, accessToken);
        return !Strings.isNullOrEmpty(result);
    }

    public boolean setQywxSelfAppAccessToken(String accessToken, Integer expiredTime) {
        if (Strings.isNullOrEmpty(accessToken)) {
            return false;
        }
        String key = QYWX_SELFAPP_ACCESS_TOKEN_STORE_PRE + accessToken;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : QYWX_ACCESS_TOKEN_EXPIRED_TIME, accessToken);
        return !Strings.isNullOrEmpty(result);
    }

    @FilterLog
    public String getQywxAccessToken(String corpId, String agentId) {
        if (Strings.isNullOrEmpty(corpId)) {
            return null;
        }
        String key = QYWX_ACCESS_TOKEN_PRE + corpId + "_" + agentId;
        return jedisCmd.get(key);
    }

    @FilterLog
    public String getQywxSelfAppAccessToken(String accessToken) {
        if (Strings.isNullOrEmpty(accessToken)) {
            return null;
        }
        String key = QYWX_SELFAPP_ACCESS_TOKEN_STORE_PRE + accessToken;
        return jedisCmd.get(key);
    }

    public void deleteQywxAccessToken(String corpId, String agentId){
        String key = QYWX_ACCESS_TOKEN_PRE + corpId + "_" + agentId;
        jedisCmd.del(key);
    }

    public boolean setCorpJsapiTicket(String corpId, String jsapiTicket, Integer expiredTime) {
        if (Strings.isNullOrEmpty(corpId)) {
            return false;
        }
        String key = CORP_JSAPI_TICKET_PRE + corpId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : CORP_JSAPI_TICKET_EXPIRED_TIME, jsapiTicket);
        return !Strings.isNullOrEmpty(result);
    }

    public String getCorpJsapiTicket(String corpId) {
        if (Strings.isNullOrEmpty(corpId)) {
            return null;
        }
        String key = CORP_JSAPI_TICKET_PRE + corpId;
        return jedisCmd.get(key);
    }

    public boolean setAgentJsapiTicket(String corpId, String agentId, String jsapiTicket, Integer expiredTime) {
        if (Strings.isNullOrEmpty(corpId)) {
            return false;
        }
        String key = AGENT_JSAPI_TICKET_PRE + corpId + "_" + agentId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : AGENT_JSAPI_TICKET_EXPIRED_TIME, jsapiTicket);
        return !Strings.isNullOrEmpty(result);
    }

    public String getAgentJsapiTicket(String corpId, String agentId) {
        if (Strings.isNullOrEmpty(corpId)) {
            return null;
        }
        String key = AGENT_JSAPI_TICKET_PRE + corpId + "_" + agentId;
        return jedisCmd.get(key);
//        if (Strings.isNullOrEmpty(agentId)) {
//            return null;
//        }
//        String key = null;
//        if (Strings.isNullOrEmpty(corpId)) {
//            key = AGENT_JSAPI_TICKET_PRE + corpId;
//        }else {
//            key = AGENT_JSAPI_TICKET_PRE + corpId + "_" + agentId;
//        }
//        return jedisCmd.get(key);
    }

    public boolean setAgentAuthRedirectUrl(String authKey, String url) {
        if (Strings.isNullOrEmpty(authKey)) {
            return false;
        }
        String key = AGENT_AUTH_REDIRECT_URL_PRE + authKey;
        String result = jedisCmd.setex(key, AGENT_AUTH_REDIRECT_URL_EXPIRED_TIME, url);
        return !Strings.isNullOrEmpty(result);
    }

    public String getAgentAuthRedirectUrl(String authKey) {
        if (Strings.isNullOrEmpty(authKey)) {
            return null;
        }
        String key = AGENT_AUTH_REDIRECT_URL_PRE + authKey;
        return jedisCmd.get(key);
    }

    public boolean setSessionByUid(String uid, String sessionKey) {
        if (EmptyUtil.isNullForList(uid, sessionKey)) {
            return false;
        }

        String key = KM_NEW_UID_2_SESSIONKEY_PRE + uid;
        String result = jedisCmd.setex(key, KM_NEW_UID_2_SESSIONKEY_EXPIRED_TIME, sessionKey);
        return !Strings.isNullOrEmpty(result);
    }



    public void delSessionByUid(String uid) {
        String key = KM_NEW_UID_2_SESSIONKEY_PRE + uid;
        jedisCmd.del(key);
    }

    public String uid2sessionKey(String uid) {
        String key = KM_NEW_UID_2_SESSIONKEY_PRE + uid;
        return jedisCmd.get(key);
    }

    public boolean setQywxH5ByUid(String uid, String sessionKey) {
        if (EmptyUtil.isNullForList(uid, sessionKey)) {
            return false;
        }

        String key = QYWX_H5_UID_2_SESSIONKEY_PRE + uid;
        String result = jedisCmd.setex(key, QYWX_H5_UID_2_SESSIONKEY_EXPIRED_TIME, sessionKey);
        return !Strings.isNullOrEmpty(result);
    }

    public void delQywxH5Session(String uid) {
        String key = QYWX_H5_UID_2_SESSIONKEY_PRE + uid;
        jedisCmd.del(key);
    }

    public String qywxH5Uid2sessionKey(String uid) {
        String key = QYWX_H5_UID_2_SESSIONKEY_PRE + uid;
        return jedisCmd.get(key);
    }

    public boolean setSessionByDingUid(String uid, String sessionKey) {
        if (EmptyUtil.isNullForList(uid, sessionKey)) {
            return false;
        }

        String key = DING_DING_NEW_UID_2_SESSIONKEY_PRE + uid;
        String result = jedisCmd.setex(key, DING_DING_NEW_UID_2_SESSIONKEY_EXPIRED_TIME, sessionKey);
        return !Strings.isNullOrEmpty(result);
    }

    public void delDingSession(String uid) {
        String key = DING_DING_NEW_UID_2_SESSIONKEY_PRE + uid;
        jedisCmd.del(key);
    }

    public String dingUid2sessionKey(String uid) {
        String key = DING_DING_NEW_UID_2_SESSIONKEY_PRE + uid;
        return jedisCmd.get(key);
    }

    /***
     * @param  key  键
     * @param value  值
     * @return true  false
     * @deprecated setnx
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * */
    public boolean setEaNewUser(String key, String value) {
        return jedisCmd.setnx(key, value) > 0;
    }

    public boolean getEaNewUser(String key) {
        String value = jedisCmd.get(key);
        return value != null;
    }


    /**
     * 尝试获取分布式锁
     *
     * @param lockKey 锁
     * @param expireTime 超期时间  300（秒）
     * @return 是否获取成功
     */
    @FilterLog
    public boolean lock(String lockKey, long expireTime) {
        Map<String, String> map = lockMapForThread.get();
        // 如果一条线程加这么多锁且不释放锁，很大概率是有问题的！
        if (map != null && map.size() >= MAX_LOCKS_PER_THREAD) {
            log.error("Thread {} has too many locks: {}, lockKey: {}", Thread.currentThread().getName(), map.size(), lockKey);
            return false;
        }
        String uuid = UUID.randomUUID().toString();
        String value = jedisCmd.set(lockKey, uuid, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
        if (value != null && value.equals("OK")) {
            if (map == null) {
                map = new HashMap<>();
                lockMapForThread.set(map);
            }
            map.put(lockKey, uuid);
            return true;
        }
        log.info("RedisManager.lock failed lockKey:{}, expireTime:{}", lockKey, expireTime);
        return false;
    }

    /**
     * 释放分布式锁
     *git
     * @param lockKey 锁
     * @return 是否释放成功
     */
    @FilterLog
    public boolean unLock(String lockKey) {
        Map<String, String> map = lockMapForThread.get();
        if (map == null) {
            return true;
        }
        String uuid = map.get(lockKey);
        if (StringUtils.isBlank(uuid)) {
            log.info("RedisManager.unLock failed, uuid is null, lockKey:{}", lockKey);
            return true;
        }
        String key = jedisCmd.get(lockKey);
        //可能会存在锁失效的时候
        if (key == null) {
            log.info("RedisManager.unLock failed, key is not exist or expired, lockKey:{}", lockKey);
            // 锁失效也要清理
            map.remove(lockKey);
            if (map.isEmpty()) {
                lockMapForThread.remove();
            }
            return true;
        }
        //保持原子性  （Get —>判断 -> del ）操作
        String script = "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end ";
        Object object = jedisCmd.eval(script, Collections.singletonList(lockKey), Collections.singletonList(uuid));
        boolean isSuccess = RELEASE_SUCCESS.equals(object);
        // 不论解锁成功或者失败，都要清理这个key, 如果map为空，清理整个ThreadLocal,避免使用线程池，线程可能会被复用，导致ThreadLocal数据残留
        map.remove(lockKey);
        if (map.isEmpty()) {
            lockMapForThread.remove();
        }
        return isSuccess;
    }

    // expireTime 单位秒
    public boolean lock(String lockKey, String lockValue, long expireTime) {
        String value = jedisCmd.set(lockKey, lockValue, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
        return value != null && value.equals("OK");
    }

    public boolean unLock(String lockKey, String expectValue) {
        String value = jedisCmd.get(lockKey);
        if (value == null) {
            return true;
        }
        if (value.equals(expectValue)) {
            jedisCmd.del(lockKey);
            return true;
        }
        return false;
    }

    /**
     * 尝试获取分布式锁,可以跨线程解锁
     *
     * @param lockKey 锁
     * @param expireTime 超期时间  300（秒）
     * @return 是否获取成功
     */
    public boolean lockIgnoreThread(String lockKey, long expireTime) {
        String uuid = UUID.randomUUID().toString();
        String value = jedisCmd.set(lockKey, uuid, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
        return value != null && value.equals("OK");
    }

    /**
     * 释放分布式锁,可以跨线程解锁
     *git
     * @param lockKey 锁
     * @return 是否释放成功
     */
    public boolean unLockIgnoreThread(String lockKey) {
        String key = jedisCmd.get(lockKey);
        //可能会存在锁失效的时候
        if (key == null) {
            return true;
        }
        Long data = jedisCmd.del(lockKey);
        return data != null && data > 0;
    }
    /**
     * 从redis查询企业微信群信息
     * @param groupId  企业微信群id
     * @return
     */
    public QueryCustomerGroupListResult getQYWXCustomerGroup(String groupId){
        if (StringUtils.isBlank(groupId)){
            return null;
        }

        String key = QYWX_CUSTOMER_GROUP_PRE + groupId;
        String value =  jedisCmd.get(key);
        return gs.fromJson(value, QueryCustomerGroupListResult.class);
    }

    /**
     * 将群信息存入到redis
     * @param groupId
     * @param entity
     * @return
     */
    public boolean setQYWXCustomerGroup(String groupId, QueryCustomerGroupListResult entity){
        if (entity == null || StringUtils.isBlank(groupId)){
            return false;
        }

        String key = QYWX_CUSTOMER_GROUP_PRE + groupId;
        String value = gs.toJson(entity);
        String result = jedisCmd.setex(key, QYWX_CUSTOMER_GROUP_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    /**
     * 获取企业微信员工详情
     * @param ea
     * @param userId
     * @return
     */
    public StaffDetailResult getQywxStaffDetail(String ea, String userId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(userId)) {
            return null;
        }
        StringJoiner key = new StringJoiner("_");
        key.add(QYWX_STAFF_DETAIL);
        key.add(ea);
        key.add(userId);
        String value = jedisCmd.get(key.toString());
        return gs.fromJson(value, StaffDetailResult.class);
    }

    /**
     * 设置企业微信员工详情
     * @param ea
     * @param staffDetailResult
     * @return
     */
    @FilterLog
    public boolean setQywxStaffDetail(String ea, StaffDetailResult staffDetailResult) {
        if (staffDetailResult == null || StringUtils.isBlank(staffDetailResult.getUserId()) || StringUtils.isBlank(ea)) {
            return false;
        }
        StringJoiner key = new StringJoiner("_");
        key.add(QYWX_STAFF_DETAIL);
        key.add(ea);
        key.add(staffDetailResult.getUserId());
        String value = gs.toJson(staffDetailResult);
        String result = jedisCmd.setex(key.toString(), QYWX_STAFF_DETAIL_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    public boolean setGlobalCrmStatisticData(String ea, Integer fsUserId, GlobalCRMStatisticalData data) {
        if (StringUtils.isBlank(ea) || data == null) {
            return false;
        }

        String key = CRM_OBJECT_STATISTICS_PRE + ea + "_" + fsUserId;
        String value = gs.toJson(data);
        String result = jedisCmd.setex(key, CRM_OBJECT_STATISTICS_EXPIRED_TIME, value);
        return StringUtils.isNotBlank(result);
    }

    public GlobalCRMStatisticalData getGlobalCRMStatisticalData(String ea, Integer fsUserId) {
        String key = CRM_OBJECT_STATISTICS_PRE + ea + "_" + fsUserId;
        String value = jedisCmd.get(key.toString());
        return gs.fromJson(value, GlobalCRMStatisticalData.class);
    }

    public void setQywxMediaId(String apath, String mediaId){
        jedisCmd.setex(QYWX_MEDIA_ID_PRE + apath, QYWX_MEDIA_ID_EXPIRED_TIME, mediaId);
    }

    public String getXiaoetongAccessToken(String ea, String appId) {
        if (Strings.isNullOrEmpty(ea) ||Strings.isNullOrEmpty(appId)) {
            return null;
        }
        String key = XIAOETONG_ACCESS_TOKEN_PRE + ea + "_" + appId;
        return jedisCmd.get(key);
    }


    public boolean setXiaoetongAccessToken(String ea, String appId, String accessToken, Integer expiredTime) {
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(appId) || Strings.isNullOrEmpty(accessToken)) {
            return false;
        }
        String key = XIAOETONG_ACCESS_TOKEN_PRE + ea + "_" + appId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : XIAOETONG_ACCESS_TOKEN_EXPIRED_TIME, accessToken);
        return !Strings.isNullOrEmpty(result);
    }

    public String getMuduAccessToken(String ea, String appId) {
        if (Strings.isNullOrEmpty(ea) ||Strings.isNullOrEmpty(appId)) {
            return null;
        }
        String key = MUDU_ACCESS_TOKEN_PRE + ea + "_" + appId;
        return jedisCmd.get(key);
    }

    public boolean setMuduAccessToken(String ea, String appId, String accessToken, Integer expiredTime) {
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(appId) || Strings.isNullOrEmpty(accessToken)) {
            return false;
        }
        String key = MUDU_ACCESS_TOKEN_PRE + ea + "_" + appId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : MUDU_ACCESS_TOKEN_EXPIRED_TIME, accessToken);
        return !Strings.isNullOrEmpty(result);
    }


    public boolean setDingMiniappAccessToken(String appId, String corpId, String accessToken, Integer expiredTime){
        String key = DING_MINIAPP_ACCESS_TOKEN_PRE + appId + corpId;
        String result = jedisCmd.setex(key, expiredTime, accessToken);
        return !Strings.isNullOrEmpty(result);
    }

    public String getDingMiniappAccessToken(String appId, String corpId){
        String key = DING_MINIAPP_ACCESS_TOKEN_PRE + appId + corpId;
        return jedisCmd.get(key);
    }

    public void setClueCountByMarketingEventId(String ea, String marketingEventId, int clueCount){
        jedisCmd.setex(MARKETING_EVENT_ID_CLUE_COUNT_PRE + ea + "_" + marketingEventId,  MARKETING_EVENT_ID_CLUE_COUNT_EXPIRED_TIME, String.valueOf(clueCount));
    }

    public String getClueCountByMarketingEventId(String ea, String marketingEventId){
        return jedisCmd.get(MARKETING_EVENT_ID_CLUE_COUNT_PRE + ea + "_" + marketingEventId);
    }

    public void setAdvertiseAccessToken(String ea, String accountId, String sourceType, String accessToken){
        jedisCmd.setex(MARKETING_ADVERTISE_PRE + ea + "_" + sourceType + "_" + accountId, ADVERTISE_EXPIRED_TIME, accessToken);
    }

    public void setAdvertiseAccessTokenWithExpiredTime(String ea, String accountId, String sourceType, String accessToken, Long expiredTime) {
        jedisCmd.setex(MARKETING_ADVERTISE_PRE + ea + "_" + sourceType + "_" + accountId, expiredTime, accessToken);
    }

    public void delAdvertiseAccessToken(String ea, String accountId, String sourceType){
        jedisCmd.del(MARKETING_ADVERTISE_PRE + ea + "_" + sourceType + "_" + accountId);
    }

    public String getAdvertiseAccessToken(String ea, String accountId, String sourceType){
        if (Strings.isNullOrEmpty(ea) ||Strings.isNullOrEmpty(sourceType) || Strings.isNullOrEmpty(accountId)) {
            return null;
        }

        String key = MARKETING_ADVERTISE_PRE + ea + "_" + sourceType + "_" + accountId;
        return jedisCmd.get(key);
    }

    public String getQywxMediaId(String apath){
        return jedisCmd.get(QYWX_MEDIA_ID_PRE + apath);
    }

    public void deleteMankeepRedisAccessToken() {
        jedisCmd.del(KEMAI_ACCESS_TOKEN);
    }

    public void deleteMankeepProRedisAccessToken() {
        jedisCmd.del(KEMAI_PRO_ACCESS_TOKEN);
    }

    public String getValueByKey(String key) {
        return jedisCmd.get(key);
    }

    public boolean setValueWithExpiredTime(String key, String value, int seconds) {
        String result = jedisCmd.setex(key, seconds, value);
        return StringUtils.isNotBlank(result);
    }

    /***
     * @param  key  键
     * @return string
     * */
    public String get(String key) {
        return jedisCmd.get(key);
    }

    /***
     * @param  key  键
     * @param value  值
     * @param  expiredTime  秒
     * @return true  false
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * */
    public boolean set(String key, int expiredTime, String value) {
        String result = jedisCmd.setex(key, expiredTime, value);
        return result != null;
    }

    public void delete(String key){
        jedisCmd.del(key);
    }

    public void batchDelete(List<String> keyList) {
        if (CollectionUtils.isEmpty(keyList)){
            return;
        }
        jedisCmd.pipeline(pipelineCmd -> {
            for (String key : keyList) {
                pipelineCmd.del(key);
            }
        });
    }

    /***
     * @param  key  键
     * @param value  值
     * @return true  false
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * */
    public boolean set(String key, String value) {
        return jedisCmd.setnx(key, value) > 0;
    }

    public boolean set(String key, String value, int seconds) {
        String val = jedisCmd.set(key, value, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, seconds);
        if (val != null && val.equals("OK")) {
            return true;
        }
        return false;
    }


    /**
     * 钉钉小程序suite_ticket
     * @param suiteKey      钉钉小程序应用凭证
     * @param suiteTicket   钉钉推送的suiteTicket。
     * @return              true or false
     */
    public boolean setDingSuiteTicket(String suiteKey, String suiteTicket) {
        String key = DING_SUITE_TICKET_PRE + suiteKey;
        String result = jedisCmd.setex(key, DING_SUITE_TICKET_EXPIRED_TIME, suiteTicket);
        return Strings.isNullOrEmpty(result);
    }

    /**
     * 获取钉钉小程序suite_ticket
     * @param suiteKey  钉钉小程序应凭证
     * @return          suiteTicket
     */
    public String getDingSuiteTicket(String suiteKey) {
        String key = DING_SUITE_TICKET_PRE + suiteKey;
        return jedisCmd.get(key);
    }

    /**
     * 获取钉钉小程序登录用户
     * 的访问凭证userAccessToken
     * @param dingUserId         登录用户钉钉userId
     * @param corpId             登录用户的钉钉corpId
     * @param suiteKey           小程序应用的唯一标识
     * @return                   userAccessToken
     */
    public String getDingMiniappUserAccessToken(String dingUserId, String corpId, String suiteKey) {
        String key = DING_MINIAPP_USER_ACCESS_TOKEN_PRE + dingUserId + corpId + suiteKey;
        return jedisCmd.get(key);
    }

    /**
     * 钉钉小程序登录用户
     * 的访问凭证userAccessToken
     * @param dingUserId            登录用户钉钉userId
     * @param corpId                登录用户的钉钉corpId
     * @param suiteKey              小程序应用的唯一标识
     * @param dingUserAccessToken   登录用户的访问凭证userAccessToken
     * @return                      true/false
     */
    public boolean setDingMiniappUserAccessToken(String dingUserId, String corpId, String suiteKey, String dingUserAccessToken) {
        String key = DING_MINIAPP_USER_ACCESS_TOKEN_PRE + dingUserId + corpId + suiteKey;
        String result = jedisCmd.setex(key, DING_USER_ACCESS_TOKEN_EXPIRED_TIME, dingUserAccessToken);
        return Strings.isNullOrEmpty(result);
    }

    public String getDingDepartmentByEa(String fsEa) {
        String key = DING_MINIAPP_DEPARTMENT + fsEa;
        return jedisCmd.get(key);
    }

    public boolean setDingDepartment(String fsEa,String departmentInfo) {
        String key = DING_MINIAPP_DEPARTMENT + fsEa;
        String result = jedisCmd.setex(key, 60 * 60, departmentInfo);
        return Strings.isNullOrEmpty(result);
    }

    public boolean setQywxSuitAccessToken(String suitId,String token) {
        String key = QYWX_SUIT_ACCESS_TOKEN_PRE + suitId;
        String result = jedisCmd.setex(key, QYWX_SUIT_ACCESS_TOKEN_EXPIRED_TIME, token);
        return Strings.isNullOrEmpty(result);
    }

    public String getQywxSuitAccessToken(String suitId) {
        return jedisCmd.get(QYWX_SUIT_ACCESS_TOKEN_PRE + suitId);
    }

    public void delQywxSuitAccessToken(String suitId) {
         jedisCmd.del(QYWX_SUIT_ACCESS_TOKEN_PRE + suitId);
    }


    public boolean setAddFanQrCodeId(String ImageQrCodeState,String addFanQrCodeId) {
        String key = OFFICIAL_WEBSITE_ADD_FAN_QR_CODE_PRE + ImageQrCodeState;
        String result = jedisCmd.setex(key, OFFICIAL_WEBSITE_ADD_FAN_QRCODE_TIME, addFanQrCodeId);
        return Strings.isNullOrEmpty(result);
    }

    public String getAddFanQrCodeId(String ImageQrCodeState) {
        return jedisCmd.get(OFFICIAL_WEBSITE_ADD_FAN_QR_CODE_PRE + ImageQrCodeState);
    }

    public void delAddFanQrCodeId(String ImageQrCodeState) {
        jedisCmd.del(OFFICIAL_WEBSITE_ADD_FAN_QR_CODE_PRE + ImageQrCodeState);
    }

    public String getAddCampaignMember(String campaignId) {
        return jedisCmd.get(ADD_CAMPAIGN_MEMBER_OBJ + campaignId);
    }

    public boolean setAddCampaignMember(String campaignId) {
        String key = ADD_CAMPAIGN_MEMBER_OBJ + campaignId;
        String result = jedisCmd.setex(key, ADD_CAMPAIGN_MEMBER_EXPIRED_TIME, campaignId);
        return Strings.isNullOrEmpty(result);
    }

    public void delAddCampaignMember(String campaignId) {
        jedisCmd.del(ADD_CAMPAIGN_MEMBER_OBJ + campaignId);
    }

    public String getDefaultCoverPath(String groupMessageDefaultCoverPath, String ea) {
        return jedisCmd.get(QYWX_MEDIA_ID_PRE + ea + groupMessageDefaultCoverPath);
    }

    public void setDefaultCoverPath(String groupMessageDefaultCoverPath, String ea, String mediaId) {
        jedisCmd.setex(QYWX_MEDIA_ID_PRE + ea + groupMessageDefaultCoverPath, QYWX_MEDIA_ID_EXPIRED_TIME, mediaId);
    }

    //获取上传附件mediaId
    public String getQywxAttachmentMediaId(String apath){
        return jedisCmd.get(MARKETING_QYWX_ATTACHMENT_MEDIA_ID_PRE + apath);
    }

    //保存上传附件mediaId
    public void setQywxAttachmentMediaId(String apath, String mediaId){
        jedisCmd.setex(MARKETING_QYWX_ATTACHMENT_MEDIA_ID_PRE + apath, MARKETING_QYWX_ATTACHMENT_MEDIA_ID_EXPIRED_TIME, mediaId);
    }

    public boolean setQywxProviderAccessToken(String corpId, String accessToken, Integer expiredTime) {
        if (Strings.isNullOrEmpty(corpId) || Strings.isNullOrEmpty(accessToken)) {
            return false;
        }
        String key = QYWX_PROVIDER_ACCESS_TOKEN_PRE + corpId;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : QYWX_PROVIDER_ACCESS_TOKEN_EXPIRED_TIME, accessToken);
        return !Strings.isNullOrEmpty(result);
    }
    public String getQywxProviderAccessToken(String corpId) {
        if (Strings.isNullOrEmpty(corpId)) {
            return null;
        }
        String key = QYWX_PROVIDER_ACCESS_TOKEN_PRE + corpId;
        return jedisCmd.get(key);
    }

    public boolean setQywxCustomerAuthUrl(String ea, String qrcodeUrl, Integer expiredTime) {
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(qrcodeUrl)) {
            return false;
        }
        String key = QYWX_QRCODE_URL_PRE + ea;
        String result = jedisCmd.setex(key, expiredTime != null ? expiredTime : QYWX_QRCODE_URL_EXPIRED_TIME, qrcodeUrl);
        return !Strings.isNullOrEmpty(result);
    }
    public String getQywxCustomerAuthUrl(String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return null;
        }
        String key = QYWX_QRCODE_URL_PRE + ea;
        return jedisCmd.get(key);
    }

    /**
     *
     * @param path(apath)
     * @param cPath
     * @return
     */
    public boolean setCPath(String path,String ea, String cPath) {
        if (Strings.isNullOrEmpty(path)||Strings.isNullOrEmpty(cPath)) {
            return false;
        }
        String key = PATH_TO_CPATH + ea + path;
        String result = jedisCmd.setex(key, PATH_TO_CPATH_TIME, cPath);
        return !Strings.isNullOrEmpty(result);
    }
    public String getCPathByPath(String path,String ea) {
        if (Strings.isNullOrEmpty(path)) {
            return null;
        }
        String key = PATH_TO_CPATH + ea + path;
        return jedisCmd.get(key);
    }

    public void delQywxCustomerAuthUrl(String ea) {
        jedisCmd.del(QYWX_QRCODE_URL_PRE + ea);
    }

    public boolean setMemberInfo(String browserUserId, JSONObject memberInfoObj) {
        if (Strings.isNullOrEmpty(browserUserId) || memberInfoObj == null) {
            return false;
        }
        String key = WX_ACCOUNT_MEMBER_INFO + browserUserId;
        try {
            jedisCmd.setex(key, WX_ACCOUNT_MEMBER_INFO_EXPIRED_TIME, JSON.toJSONString(memberInfoObj));
        } catch (Exception e) {
            log.warn("RedisManager RedisManager error", e);
        }
        return true;
    }

    public JSONObject getMemberInfo(String browserUserId) {
        if (Strings.isNullOrEmpty(browserUserId)) {
            return null;
        }
        String key = WX_ACCOUNT_MEMBER_INFO + browserUserId;
        try {
            return JSONObject.parseObject(jedisCmd.get(key));
        } catch (Exception e) {
            log.warn("RedisManager RedisManager error", e);
        }
        return null;
    }

    @FilterLog
    public String getPrimaryId() {
        String id = "";
        if (jedisCmd.exists(MARKETING_PRIMARY_ID_KEY)) {
            id = String.valueOf(jedisCmd.incrBy(MARKETING_PRIMARY_ID_KEY, 1));
        } else {
            id = this.initPrimaryId(MARKETING_PRIMARY_ID_KEY);
        }
        return id;
    }

    public boolean setGrayEaApiName(String ea,String apiNames) {
        if (Strings.isNullOrEmpty(ea)) {
            return false;
        }
        String key = MARKETING_GRAY_EA_INFO + ea;
        String result = jedisCmd.setex(key, MARKETING_GRAY_EA_INFO_EXPIRED_TIME, apiNames);
        return !Strings.isNullOrEmpty(result);
    }
    public String getGrayEaApiName(String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return null;
        }
        String key = MARKETING_GRAY_EA_INFO + ea;
        return jedisCmd.get(key);
    }

    @FilterLog
    public boolean setUtmParam(String visitorId, RecordUtmParamArg arg) {
        if (StringUtils.isEmpty(visitorId)||StringUtils.isEmpty(arg.getEa())||arg==null) {
            return false;
        }
        String key = OFFICIAL_WEBSITE_VISITOR_UTM_RECORD + arg.getEa() + visitorId;
        String value = gs.toJson(arg);
        try {
            jedisCmd.setex(key, OFFICIAL_WEBSITE_VISITOR_UTM_RECORD_TIME, value);
        } catch (Exception e) {
            log.warn("RedisManager setUtmParam error, visitorId is", visitorId);
        }
        return true;
    }
    public RecordUtmParamArg getUtmParam(String ea,String visitorId) {
        if (StringUtils.isEmpty(visitorId)||StringUtils.isEmpty(ea)) {
            return null;
        }
        String key = OFFICIAL_WEBSITE_VISITOR_UTM_RECORD + ea + visitorId;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }
        return gs.fromJson(value, RecordUtmParamArg.class);
    }


    /**
     * 生成每天的初始Id
     *
     * @param key
     * @return
     */
    private String initPrimaryId(String key) {
        String hashCol = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //自定义编号规则
        String hashColVal = hashCol + "00000001";
        Long expiresTime = getSecondsNextEarlyMorning();
        jedisCmd.setex(key, expiresTime, hashColVal);
        return hashColVal;
    }

    /**
     * 判断当前时间距离第二天凌晨的秒数
     *
     * @return 返回值单位为[s:秒]
     */
    private Long getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long time = (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
        return time + 1;
    }

    public void setSMCodeToRedis(String phone, RedisSendSmsCheckValue value){
        if(StringUtils.isBlank(phone) || StringUtils.isBlank(value.getValue())){
            return;
        }

        String key = PHONE_PRE + phone;
        jedisCmd.setex(key, PHONE_TIME, gs.toJson(value));
    }

    public RedisSendSmsCheckValue getSMCodeFromRedis(String phone){
        String key = PHONE_PRE + phone;
        String value =  jedisCmd.get(key);
        if (value == null){
            return null;
        }

        return gs.fromJson(value, RedisSendSmsCheckValue.class);
    }

    public void delSMCodeFromRedis(String phone){
        String key = PHONE_PRE + phone;
        jedisCmd.del(key);
    }

    public String getImageSizeFromRedis(String ea,String imageUrl){
        String key =  ea +"_"+ imageUrl;
        return jedisCmd.get(key);
    }

    public boolean setImageSizeToRedis(String ea,String imageUrl,String size){
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(imageUrl)) {
            return false;
        }
        String key =  ea +"_"+ imageUrl;
        String result = jedisCmd.setex(key,IMAGE_SIZE_TIME, size);
        return !Strings.isNullOrEmpty(result);
    }

    public String isBindAdAccount(String ea) {
        String key = String.format(AD_BIND_KEY, ea);
        String value = jedisCmd.get(key);
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }
        return null;
    }

    public void bindAdAccount(String ea) {
        String key = String.format(AD_BIND_KEY, ea);
        set(key, "true", 60 * 60 * 24);
    }


    public String getSecretKey(String ea) {
        String key = String.format(EA_SECRET_KEY, ea);
        return get(key);
    }

    public void setSecretKey(String ea, String secretKey) {
        String key = String.format(EA_SECRET_KEY, ea);
        set(key, secretKey, 60 * 60 * 24);
    }
    public String getShanShanToken(String ea ) {
        String key = SHANSHAN_TOKEN + ea;
        String value = jedisCmd.get(key);
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }
        return null;
    }

    public void setShanShanToken(String ea,String token,Integer timeStamp) {
        String key = SHANSHAN_TOKEN + ea ;
        set(key, token, timeStamp);
    }

    public void delShanShanToken(String ea) {
        String key = SHANSHAN_TOKEN + ea ;
        delete(key);
    }

    //获取上传附件mediaId
    public String getQywxAttachmentMediaIdByEa(String apath,String ea){
        return jedisCmd.get(MARKETING_QYWX_ATTACHMENT_MEDIA_ID_PRE + apath + ea);
    }

    //保存上传附件mediaId
    public void setQywxAttachmentMediaIdByEa(String apath, String ea,String mediaId){
        jedisCmd.setex(MARKETING_QYWX_ATTACHMENT_MEDIA_ID_PRE + apath + ea, MARKETING_QYWX_ATTACHMENT_MEDIA_ID_EXPIRED_TIME, mediaId);
    }

    /**
     * 企微userId转换（格式：[密文：明文]）
     * @param ea
     * @param userIdMap
     */
    public void setQywxUserIdConvert(String ea, Map<String, String> userIdMap) {
        jedisCmd.setex(QYWX_USER_ID_CONVERT_PRE + ea, QYWX_USER_ID_CONVERT_EXPIRED_TIME, gs.toJson(userIdMap));
    }

    public Map<String, String> getQywxUserIdConvert(String ea) {
        String value = jedisCmd.get(QYWX_USER_ID_CONVERT_PRE + ea);
        if (value == null){
            return null;
        }
        return gs.fromJson(value, Map.class);
    }

    public String getQyexOpenUserIdByUserId(String ea, String userId) {
        return jedisCmd.get(QYWX_USER_ID_TO_OPEN_USER_ID + ea + userId);    }

    public void setQyexOpenUserIdByUserId(String ea, String userId, String openUserId) {
        jedisCmd.setex(QYWX_USER_ID_TO_OPEN_USER_ID + ea + userId, QYWX_USER_ID_TO_OPEN_USER_ID_EXPIRED_TIME, openUserId);
    }

    //获取企业状态
    @FilterLog
    public String getEnterpriseStatus(String ea){
        return jedisCmd.get(String.format(ENTERPRISE_STATUS_PREFIX_KEY, ea));
    }
    public void setEnterpriseStatus(String ea, String value){
        String result = jedisCmd.setex(String.format(ENTERPRISE_STATUS_PREFIX_KEY, ea), ENTERPRISE_STATUS_EXPIRED_TIME, value);
    }

    public Long getKeywordIndexCommandCount(String ea) {
        Long count;
        String hashCol = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = "KeywordIndexCommand_" + ea + "_" + hashCol;
        if (jedisCmd.exists(key)) {
            count = jedisCmd.incrBy(key, 1);
        } else {
            count = this.initKeywordIndexCommandCount(key);
        }
        return count;
    }

    private Long initKeywordIndexCommandCount(String key) {

        //自定义编号规则
        Long expiresTime = getSecondsNextEarlyMorning();
        jedisCmd.setex(key, expiresTime, "0");
        return 0L;
    }

    public boolean lockCalculateAttributeData(String ea, String marketingEventId) {
        String key = String.format(CALCULATE_ATTRIBUTE_DATA_PRE, ea, marketingEventId);
        return lock(key, "ok", TWO_HOURS);
    }

    public boolean releaseCalculateAttributeDataLock(String ea, String marketingEventId) {
        String key = String.format(CALCULATE_ATTRIBUTE_DATA_PRE, ea, marketingEventId);
        return unLock(key, "ok");
    }

    public boolean lockCalculateMarketingEventLeadData(String ea, String marketingEventId) {
        String key = String.format(CALCULATE_MARKETING_EVENT_LEAD_DATA_PRE, ea, marketingEventId);
        return lock(key, "ok", TWO_HOURS);
    }

    public boolean releaseCalculateMarketingEventLeadDataLock(String ea, String marketingEventId) {
        String key = String.format(CALCULATE_MARKETING_EVENT_LEAD_DATA_PRE, ea, marketingEventId);
        return unLock(key, "ok");
    }

    public boolean lockWhatsAppTaskOperate(String ea, String taskId) {
        String key = String.format(WHATS_APP_SEND_MESSAGE_PRE, ea, taskId);
        return lock(key, "ok", TWO_HOURS);
    }

    public boolean unlockWhatsAppTaskOperate(String ea, String taskId) {
        String key = String.format(WHATS_APP_SEND_MESSAGE_PRE, ea, taskId);
        return unLock(key, "ok");
    }

    public long incCampaignMembersStatusChangeCount(String ea, String objectId) {
        String key = String.format(CAMPAIGN_MEMBER_STATUS_CHANGE, ea, objectId);
        if (jedisCmd.exists(key)) {
            return jedisCmd.incrBy(key, 1);
        }
        jedisCmd.setex(key, FIVE_MINUTES, "1");
        return 1;
    }

    public boolean unlockMemberMarketingOperate(String ea, String id) {
        String key = String.format(MEMBER_MARKETING_NOTICE_PRE, ea, id);
        return unLock(key, "ok");
    }

    public boolean lockMemberMarketingOperate(String ea, String id) {
        String key = String.format(MEMBER_MARKETING_NOTICE_PRE, ea, id);
        return lock(key, "ok", FIVE_MINUTES);
    }

    public boolean setDouYinAccessToken(String ea, String appKey, String accessToken) {
        String key = DOU_YIN_ACCESS_TOKEN_KEY_PRE + ea + "_" + appKey;
        String result = jedisCmd.setex(key, DOU_YIN_ACCESS_TOKEN_EXPIRE_TIME, accessToken);
        return "OK".equals(result);
    }

    public String getDouYinAccessToken(String ea, String appKey) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(appKey)) {
            return null;
        }
        String key = DOU_YIN_ACCESS_TOKEN_KEY_PRE + ea + "_" + appKey;
        return jedisCmd.get(key);
    }

    /**
     * 发布消息到Redis频道
     *
     * @param channel 频道名称
     * @param message 消息内容
     * @return 接收到消息的订阅者数量
     */
    public Long publish(String channel, String message) {
        try {
            // 使用jedisCmd的execute方法来执行publish命令
            return jedisCmd.execute(jedis -> jedis.publish(channel, message));
        } catch (Exception e) {
            log.error("发布Redis消息失败，频道: {}，消息: {}", channel, message, e);
            return 0L;
        }
    }

    /**
     * 订阅Redis频道模式（支持通配符）
     *
     * @param jedisPubSub 订阅处理器
     * @param patterns 频道模式（支持通配符，如 "workspace_sync_status:*"）
     */
    public void psubscribe(redis.clients.jedis.JedisPubSub jedisPubSub, String... patterns) {
        try {
            // 使用jedisCmd的execute方法来执行psubscribe命令
            jedisCmd.execute(jedis -> {
                jedis.psubscribe(jedisPubSub, patterns);
                return null;
            });
        } catch (Exception e) {
            log.error("订阅Redis频道模式失败，模式: {}", java.util.Arrays.toString(patterns), e);
        }
    }

    public boolean lockAdRefreshData(String ea, String adAccountId) {
        String key = String.format(AD_REFRESH_DATA_KEY, ea, adAccountId);
        return lock(key, TWO_HOURS);
    }

    public boolean unLockAdRefreshData(String ea, String adAccountId) {
        String key = String.format(AD_REFRESH_DATA_KEY, ea, adAccountId);
        return unLock(key);
    }

    public boolean lockInitQywxDepartment(String ea) {
        String key = String.format(INIT_QYWX_DEPARTMENT_PRE, ea);
        return lock(key, "ok", FIVE_MINUTES);
    }

    public boolean unlockInitQywxDepartment(String ea) {
        String key = String.format(INIT_QYWX_DEPARTMENT_PRE, ea);
        return unLock(key, "ok");
    }

    public boolean lockQywxDepartmentChange(String ea, String uniqueKey) {
        String key = String.format(QYWX_DEPARTMENT_CHANGE_PRE, ea, uniqueKey);
        return lock(key, "ok", FIVE_MINUTES);
    }

    public boolean unlockQywxDepartmentChange(String ea, String uniqueKey) {
        String key = String.format(QYWX_DEPARTMENT_CHANGE_PRE, ea, uniqueKey);
        return unLock(key, "ok");
    }

    public boolean lockMailEaStatistics(String ea) {
        return lock(String.format(MAIL_EA_STATISTIC_JOB_KEY, ea), "ok", TWO_HOURS);
    }

    public boolean unLockMailEaStatistics(String ea) {
        return unLock(String.format(MAIL_EA_STATISTIC_JOB_KEY, ea), "ok");
    }


    public boolean lockMailTaskStatistics(String ea, String taskId) {
        return lock(String.format(MAIL_TASK_STATISTIC_JOB_KEY, ea, taskId), "ok", TWO_HOURS);
    }

    public boolean unLockMailTaskStatistics(String ea, String taskId) {
        return unLock(String.format(MAIL_TASK_STATISTIC_JOB_KEY, ea, taskId), "ok");
    }

    public boolean lockMultiEventSystemHexagon(String ea, String marketingEventId) {
        String key = String.format(MULTI_EVENT_SYSTEM_HEXAGON_KEY, ea, marketingEventId);
        return lock(key, TEN_MINUTES);
    }

    public boolean unLockMultiEventSystemHexagon(String ea, String marketingEventId) {
        String key = String.format(MULTI_EVENT_SYSTEM_HEXAGON_KEY, ea, marketingEventId);
        return unLock(key);
    }
}
