package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.qywx.QywxGroupSendRangeTypeEnum;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dto.TriggerUserVO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2021-03-02.
 */
@Slf4j
@Component
public class TriggerInstanceManager {
	@Autowired
	private SceneTriggerDao sceneTriggerDao;
	@Autowired
	private TriggerSnapshotDao triggerSnapshotDao;
	@Autowired
	private TriggerInstanceDao triggerInstanceDao;
	@Autowired
	private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
	@Autowired
	private TriggerTaskInstanceDao triggerTaskInstanceDao;
	@Autowired
	private CampaignMergeDataManager campaignMergeDataManager;
	@Autowired
	private UserMarketingAccountManager userMarketingAccountManager;
	@Autowired
	private CampaignMergeDataDAO campaignMergeDataDAO;
	@Autowired
	private UserMarketingAccountDAO userMarketingAccountDAO;
	@Autowired
	private MarketingTriggerDao marketingTriggerDao;
	@Autowired
	private ConferenceDAO conferenceDAO;
	@Autowired
	private MarketingLiveDAO marketingLiveDAO;
	@Autowired
	private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
	@Autowired
	private TriggerTaskInstanceManager triggerTaskInstanceManager;

	public void startInstanceByTriggerSnapshotAndSceneAsync(String ea, String sceneTriggerId, String triggerSnapshotId, Collection<TriggerUserVO> triggerUsers, Map<String, Object> paramMap){
		if (triggerUsers == null || triggerUsers.isEmpty()){
			return ;
		}
		SceneTriggerEntity sceneTrigger = sceneTriggerDao.getById(ea, sceneTriggerId);
		if(!SceneTriggerLifeStatus.ENABLED.getLifeStatus().equals(sceneTrigger.getLifeStatus())){
			return ;
		}
		TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getById(ea, triggerSnapshotId);
		if (!TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(triggerSnapshot.getSnapshotStatus())){
			return ;
		}
		MarketingTriggerEntity triggerEntity = marketingTriggerDao.getById(triggerSnapshot.getTriggerId(), ea);
		if (null != triggerEntity && null != triggerEntity.getDeleted() && triggerEntity.getDeleted()) {
			return;
		}
		String marketingEventId = null;
		if (MarketingSceneType.CONFERENCE.getType().equals(sceneTrigger.getSceneType())){
			ActivityEntity conference = conferenceDAO.getConferenceById(sceneTrigger.getSceneTargetId());
			if (conference != null){
				marketingEventId = conference.getMarketingEventId();
			}
		}
		if (MarketingSceneType.LIVE.getType().equals(sceneTrigger.getSceneType())){
			MarketingLiveEntity live = marketingLiveDAO.getById(sceneTrigger.getSceneTargetId());
			if (live != null){
				marketingEventId = live.getMarketingEventId();
			}
		}
		if (MarketingSceneType.MARKETING_EVENT.getType().equals(sceneTrigger.getSceneType()) || MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(sceneTrigger.getSceneType())){
			marketingEventId = sceneTrigger.getSceneTargetId();
		}
		
		String finalMarketingEventId = marketingEventId;
		ThreadPoolUtils.execute(() -> {
			TriggerTaskSnapshotEntity triggerTaskSnapshot = triggerTaskSnapshotDao.getByTriggerSnapshotId(ea, triggerSnapshotId);
			List<TriggerTaskSnapshotEntity> triggerTaskSnapshotEntities = triggerTaskSnapshotDao.listByTriggerSnapshotId(ea, triggerSnapshotId);
			TriggerTaskSnapshotEntity.TriggerTaskSnapshotTree triggerTaskSnapshotTree = new TriggerTaskSnapshotEntity.TriggerTaskSnapshotTree(triggerTaskSnapshotEntities);

			boolean hasSensWorkWXMsg = triggerTaskSnapshotEntities.stream().anyMatch(e -> TriggerTaskTypeEnum.SEND_WORK_WX_MSG.getTriggerTaskType().equals(e.getTaskType()));
			boolean hasBranchJudgment = triggerTaskSnapshotEntities.stream().anyMatch(e -> TriggerTaskTypeEnum.BRANCH_JUDGMENT.getTriggerTaskType().equals(e.getTaskType()));
			// 定时类型客户群发SOP特殊处理:
			if ((TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(triggerSnapshot.getTriggerType()) || TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType()) || TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType()))
					&& hasSensWorkWXMsg && !hasBranchJudgment) {

				log.warn("定时类型客户群发SOP特殊处理");
				List<TriggerTaskInstanceEntity> triggerTaskInstances = Lists.newArrayList();
				List<TriggerTaskInstanceEntity> triggerTaskInstanceToAdds = Lists.newArrayList();
				for (TriggerUserVO triggerUser : triggerUsers) {

					if (filterByCondition(ea, triggerUser, triggerSnapshot, sceneTrigger, triggerTaskSnapshot)) {
						continue;
					}

					TriggerInstanceEntity triggerInstanceToAdd = new TriggerInstanceEntity();
					triggerInstanceToAdd.setId(UUIDUtil.getUUID());
					triggerInstanceToAdd.setEa(ea);
					triggerInstanceToAdd.setTriggerId(triggerSnapshot.getTriggerId());
					triggerInstanceToAdd.setTriggerSnapshotId(triggerSnapshotId);
					triggerInstanceToAdd.setSceneId(sceneTrigger.getSceneId());
					triggerInstanceToAdd.setSceneType(sceneTrigger.getSceneType());
					triggerInstanceToAdd.setSceneTargetId(sceneTrigger.getSceneTargetId());
					triggerInstanceToAdd.setMarketingUserId(triggerUser.getMarketingUserId());
					triggerInstanceToAdd.setCampaignId(triggerUser.getCampaignId());
					triggerInstanceToAdd.setTotalTaskCount(triggerTaskSnapshotTree.getTotalLevels());
					triggerInstanceToAdd.setExecuteTime(new DateTime().getMillis());
					if (CollectionUtils.isNotEmpty(triggerUser.getChatIdList())) {
						triggerInstanceToAdd.setChatIdList(GsonUtil.toJson(triggerUser.getChatIdList()));
					}
					Map<String, Object> finalParamMap = paramMap;
					if (finalParamMap == null) {
						finalParamMap = Maps.newHashMap();
					}
					if (finalParamMap.get("triggerRecordId") != null) {
						triggerInstanceToAdd.setTriggerRecordId(String.valueOf(finalParamMap.get("triggerRecordId")));
					}
					finalParamMap.put("isQywxTiming", true);
					boolean insertSuccess = triggerInstanceDao.insert(triggerInstanceToAdd) == 1;
					if (insertSuccess) {
						marketingTriggerDao.incrementInstanceCount(triggerSnapshot.getTriggerId(), ea);
						sceneTriggerDao.incrementInstanceCount(sceneTrigger.getId());
						for (TriggerTaskSnapshotEntity triggerTask : triggerTaskSnapshotEntities) {
							try {
								TriggerTaskInstanceEntity triggerTaskInstanceToAdd = triggerTaskInstanceManager.buildTriggerTaskInstance(ea, finalParamMap, triggerSnapshot, triggerInstanceToAdd.getId(), triggerTask);
								triggerTaskInstanceToAdds.add(triggerTaskInstanceToAdd);
								if (ExecuteTypeEnum.IMMEDIATELY.getExecuteType().equals(triggerTaskInstanceToAdd.getExecuteType())) {
									triggerTaskInstances.add(triggerTaskInstanceToAdd);
								}
							} catch (Exception e) {
								log.warn("Exception at triggerUser:{}, sceneTriggerId:{}, triggerSnapshotId:{}, ea:{}", triggerUser, sceneTriggerId, triggerSnapshotId, ea, e);
							}
						}
					}
				}
				if (CollectionUtils.isNotEmpty(triggerTaskInstanceToAdds)) {
					List<List<TriggerTaskInstanceEntity>> partitions = com.google.common.collect.Lists.partition(triggerTaskInstanceToAdds, 500);
					for (List<TriggerTaskInstanceEntity> partition : partitions) {
						triggerTaskInstanceDao.batchInsert(partition);
					}
				}
				for (TriggerTaskInstanceEntity triggerTaskInstance : triggerTaskInstances) {
					triggerTaskInstanceManager.finishTriggerTask(ea, triggerTaskInstance.getId());
				}

			} else {

				for (TriggerUserVO triggerUser : triggerUsers) {
					try {

						if (filterByCondition(ea, triggerUser, triggerSnapshot, sceneTrigger, triggerTaskSnapshot)) {
							continue;
						}

						// 市场活动的sop, 补充活动成员id
						if (Strings.isNullOrEmpty(triggerUser.getCampaignId()) && !Strings.isNullOrEmpty(finalMarketingEventId)) {
							UserMarketingAccountEntity marketingUserAccount = userMarketingAccountDAO.getById(triggerUser.getMarketingUserId());
							if (marketingUserAccount != null && !Strings.isNullOrEmpty(marketingUserAccount.getPhone())) {
								List<CampaignMergeDataEntity> data = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, finalMarketingEventId, marketingUserAccount.getPhone(), false);
								if (!data.isEmpty()) {
									triggerUser.setCampaignId(data.get(0).getId());
								}
							}
						}

						TriggerInstanceEntity triggerInstanceToAdd = new TriggerInstanceEntity();
						triggerInstanceToAdd.setId(UUIDUtil.getUUID());
						triggerInstanceToAdd.setEa(ea);
						triggerInstanceToAdd.setTriggerId(triggerSnapshot.getTriggerId());
						triggerInstanceToAdd.setTriggerSnapshotId(triggerSnapshotId);
						triggerInstanceToAdd.setSceneId(sceneTrigger.getSceneId());
						triggerInstanceToAdd.setSceneType(sceneTrigger.getSceneType());
						triggerInstanceToAdd.setSceneTargetId(sceneTrigger.getSceneTargetId());
						triggerInstanceToAdd.setMarketingUserId(triggerUser.getMarketingUserId());
						triggerInstanceToAdd.setCampaignId(triggerUser.getCampaignId());
						triggerInstanceToAdd.setTotalTaskCount(triggerTaskSnapshotTree.getTotalLevels());
						triggerInstanceToAdd.setExecuteTime(new DateTime().getMillis());
						if (CollectionUtils.isNotEmpty(triggerUser.getChatIdList())) {
							triggerInstanceToAdd.setChatIdList(GsonUtil.toJson(triggerUser.getChatIdList()));
						}
						if (paramMap != null && paramMap.get("triggerRecordId") != null) {
							triggerInstanceToAdd.setTriggerRecordId(String.valueOf(paramMap.get("triggerRecordId")));
						}
						boolean insertSuccess = triggerInstanceDao.insert(triggerInstanceToAdd) == 1;
						if (insertSuccess) {
							marketingTriggerDao.incrementInstanceCount(triggerSnapshot.getTriggerId(), ea);
							sceneTriggerDao.incrementInstanceCount(sceneTrigger.getId());
							TriggerTaskInstanceEntity triggerTaskInstanceToAdd = triggerTaskInstanceManager.buildTriggerTaskInstance(ea, paramMap, triggerSnapshot, triggerInstanceToAdd.getId(), triggerTaskSnapshot);
							triggerTaskInstanceDao.batchInsert(Collections.singleton(triggerTaskInstanceToAdd));
							if (ExecuteTypeEnum.IMMEDIATELY.getExecuteType().equals(triggerTaskInstanceToAdd.getExecuteType())) {
								triggerTaskInstanceManager.finishTriggerTask(ea, triggerTaskInstanceToAdd.getId());
							}
						}
					} catch (Exception e) {
						log.warn("Exception at triggerUser:{}, sceneTriggerId:{}, triggerSnapshotId:{}, ea:{}", triggerUser, sceneTriggerId, triggerSnapshotId, ea, e);
					}
				}
			}
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
	}

	/**
	 * 条件过滤
	 *
	 * @param ea
	 * @param triggerUser
	 * @param triggerSnapshot
	 * @param sceneTrigger
	 * @param triggerTask
	 * @return true:不过滤, false:过滤掉
	 */
	private boolean filterByCondition(String ea, TriggerUserVO triggerUser, TriggerSnapshotEntity triggerSnapshot, SceneTriggerEntity sceneTrigger, TriggerTaskSnapshotEntity triggerTask) {
		// 该触发器每个用户仅触发一次(前端写死传参)   包含了互动通知例外
		if (BooleanUtils.isTrue(triggerSnapshot.getUserExecuteOnce())
				&& triggerInstanceDao.countByTriggerAndSceneAndMarketingUserId(ea, triggerSnapshot.getTriggerId(), sceneTrigger.getSceneId(), triggerUser.getMarketingUserId()) > 0
				&& !JSON.toJSONString(triggerTask).contains("send_union_msg")){
			log.info("startInstanceByTriggerSnapshotAndSceneAsync userExecuteOnce ea:{} triggerId:{} triggerUser:{}", ea, triggerSnapshot.getTriggerId(), triggerUser);
			return true;
		}

		// 企微场景客户群sop, 不适用以下过滤
		if (MarketingSceneType.QYWX.getType().equals(sceneTrigger.getSceneType()) && triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
			log.info("startInstanceByTriggerSnapshotAndSceneAsync hasGroupSop ea:{} triggerId:{} triggerUser:{}", ea, triggerSnapshot.getTriggerId(), triggerUser);
			return false;
		}

		// 未提交表单且未关联6大对象  包含了互动通知例外
		if (isExcludeTriggerUser(ea, triggerUser) && !JSON.toJSONString(triggerTask).contains("send_union_msg")) {
			log.info("startInstanceByTriggerSnapshotAndSceneAsync isExcludeTriggerUser ea:{} triggerId:{} triggerUser:{}", ea, triggerSnapshot.getTriggerId(), triggerUser);
			return true;
		}

		// 营销用户标签过滤(触发人员为满足以下条件的用户选项)
		if (BooleanUtils.isTrue(triggerSnapshot.getFilterByCondition())) {
			boolean tagOperatorValid = "IN".equals(triggerSnapshot.getTagOperator()) || "LIKE".equals(triggerSnapshot.getTagOperator()) || "HASANYOF".equals(triggerSnapshot.getTagOperator());
			boolean excludeTags = BooleanUtils.isTrue(triggerSnapshot.getExcludeTags());
			if (tagOperatorValid && triggerSnapshot.getTagNames() != null && !triggerSnapshot.getTagNames().isEmpty()
					|| excludeTags && triggerSnapshot.getExcludeTagNames() != null && !triggerSnapshot.getExcludeTagNames().isEmpty()) {
				Map<String, List<TagName>> marketingUserToTagNameMap = userMarketingAccountManager
						.listTagNameListByUserMarketingAccountIds(ea, ChannelEnum.getAllChannelApiName(), com.google.common.collect.Lists.newArrayList(triggerUser.getMarketingUserId()));
				if (tagOperatorValid && triggerSnapshot.getTagNames() != null && !triggerSnapshot.getTagNames().isEmpty()) {
					if (marketingUserToTagNameMap == null || marketingUserToTagNameMap.get(triggerUser.getMarketingUserId()) == null || marketingUserToTagNameMap.get(triggerUser.getMarketingUserId()).isEmpty()) {
						return true;
					}
					List<TagName> tagNames = marketingUserToTagNameMap.get(triggerUser.getMarketingUserId());
					if (("IN".equals(triggerSnapshot.getTagOperator()) || "LIKE".equals(triggerSnapshot.getTagOperator())) && !tagNames.containsAll(triggerSnapshot.getTagNames())) {
						return true;
					}
					if ("HASANYOF".equals(triggerSnapshot.getTagOperator()) && tagNames.stream().noneMatch(tagName -> triggerSnapshot.getTagNames().contains(tagName))) {
						return true;
					}
				}
				if (excludeTags && triggerSnapshot.getExcludeTagNames() != null && !triggerSnapshot.getExcludeTagNames().isEmpty()
						&& marketingUserToTagNameMap != null && marketingUserToTagNameMap.get(triggerUser.getMarketingUserId()) != null) {
                    return marketingUserToTagNameMap.get(triggerUser.getMarketingUserId()).stream().anyMatch(tagName -> triggerSnapshot.getExcludeTagNames().contains(tagName));
				}
			}
		}

		return false;
	}

	/**
	 * 触发过滤：未提交表单且未关联6大对象
	 * @param ea
	 * @param triggerUser
	 * @return
	 */
	private boolean isExcludeTriggerUser(String ea, TriggerUserVO triggerUser){
		//未提交表单 且未关联6大对象
		if(StringUtils.isEmpty(triggerUser.getCampaignId())){
			List<String> apiNameList = Lists.newArrayList(ChannelEnum.CRM_LEAD.getApiName(), ChannelEnum.CRM_ACCOUNT.getApiName(),
					ChannelEnum.CRM_CONTACT.getApiName(), ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getApiName(), ChannelEnum.CRM_WX_USER.getApiName(), ChannelEnum.CRM_MEMBER.getApiName());
			boolean isRelateCrmObject = userMarketingAccountRelationManager.isRelateCrmObject(ea, triggerUser.getMarketingUserId(), apiNameList);
			if (!isRelateCrmObject){
				log.info("TriggerInstanceManager isExcludeTriggerUser exclude trigger user ea:{}, triggerUser:{}", ea, triggerUser);
				return true;
			}
		}

		return false;
	}

	/**
	 * 用户行为触发
	 *
	 * @param ea
	 * @param marketingUserId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param paramMap
	 */
	public void startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(String ea, String marketingUserId, String sceneType, String sceneTargetId, String triggerActionType, Map<String,Object> paramMap) {
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerAction(ea, sceneType, sceneTargetId, triggerActionType, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(marketingUserId, null)), paramMap);
			}
		});
	}

	/**
	 * 参会成员触发
	 *
	 * @param ea
	 * @param campaignId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param paramMap
	 */
	public void startInstanceByCampaignIdAndSceneMsgAndTriggerAction(String ea, String campaignId, String sceneType, String sceneTargetId, String triggerActionType, Map<String,Object> paramMap) {
		Map<String, String> campaignIdToMarketingUserIdMap = campaignMergeDataManager.getMarketingUserIdByCampaignIds(ea, ImmutableSet.of(campaignId));
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, campaignIdToMarketingUserIdMap.get(campaignId), paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerAction(ea, sceneType, sceneTargetId, triggerActionType, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				if (campaignIdToMarketingUserIdMap.get(campaignId) != null){
					this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(campaignIdToMarketingUserIdMap.get(campaignId), campaignId)), paramMap);
				} else {
					log.warn("getMarketingUserIdByCampaignIds fail ea:{}, campaignId:{}", ea, campaignId);
				}
			}
		});
	}

	/**
	 * 参会人员命中指定内容触发
	 *
	 * @param ea
	 * @param campaignId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param paramMap
	 */
	public void startInstanceByCampaignIdAndSceneAndTargetObject(String ea, String campaignId, String sceneType, String sceneTargetId, String triggerActionType, Map<String,Object> paramMap) {
		Map<String, String> campaignIdToMarketingUserIdMap = campaignMergeDataManager.getMarketingUserIdByCampaignIds(ea, ImmutableSet.of(campaignId));
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, campaignIdToMarketingUserIdMap.get(campaignId), paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		Integer targetObjectType = paramMap != null ? paramMap.get("objectType") != null ? (Integer) paramMap.get("objectType") : null : null;
		String targetObjectId = paramMap != null ? paramMap.get("objectId") != null ? (String) paramMap.get("objectId") : null : null;
		if (targetObjectType == null || StringUtils.isEmpty(targetObjectId)) {
			return;
		}
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerActionAndTargetObject(ea, sceneType, sceneTargetId, triggerActionType, targetObjectType, targetObjectId, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				if (campaignIdToMarketingUserIdMap.get(campaignId) != null){
					this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(campaignIdToMarketingUserIdMap.get(campaignId), campaignId)), paramMap);
				} else {
					log.warn("getMarketingUserIdByCampaignIds fail ea:{}, campaignId:{}", ea, campaignId);
				}
			}
		});
	}

	/**
	 * 用户行为命中指定内容触发
	 *
	 * @param ea
	 * @param marketingUserId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param paramMap
	 */
	public void startInstanceByMarketingUserIdAndSceneAndTargetObject(String ea, String marketingUserId, String sceneType, String sceneTargetId, String triggerActionType, Map<String,Object> paramMap) {
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		Integer targetObjectType = paramMap != null ? paramMap.get("objectType") != null ? (Integer) paramMap.get("objectType") : null : null;
		String targetObjectId = paramMap != null ? paramMap.get("objectId") != null ? (String) paramMap.get("objectId") : null : null;
		if (targetObjectType == null || StringUtils.isEmpty(targetObjectId)) {
			return;
		}
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerActionAndTargetObject(ea, sceneType, sceneTargetId, triggerActionType, targetObjectType, targetObjectId, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(marketingUserId, null)), paramMap);
			}
		});
	}

	/**
	 * 直播互动次数触发
	 *
	 * @param ea
	 * @param marketingUserId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param actionCount
	 * @param paramMap
	 */
	public void startInstanceByMarketingUserIdAndSceneAndActionCount(String ea, String marketingUserId, String sceneType, String sceneTargetId, String triggerActionType, Integer actionCount, Map<String,Object> paramMap) {
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		if (actionCount == null){
			actionCount = 1;
		}
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerActionAndActionCount(ea, sceneType, sceneTargetId, triggerActionType, actionCount, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(marketingUserId, null)), paramMap);
			}
		});
	}

	/**
	 * 观看直播时长触发
	 *
	 * @param ea
	 * @param marketingUserId
	 * @param sceneType
	 * @param sceneTargetId
	 * @param triggerActionType
	 * @param actionDurationMinutes
	 * @param paramMap
	 */
	public void startInstanceByMarketingUserIdAndSceneAndActionDurationMinutes(String ea, String marketingUserId, String sceneType, String sceneTargetId, String triggerActionType, Integer actionDurationMinutes, Map<String,Object> paramMap) {
		ThreadPoolUtils.execute(() -> {
			// 触发监听中的sop任务
			triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
		}, ThreadPoolTypeEnums.HEAVY_BUSINESS);
		if (actionDurationMinutes == null){
			actionDurationMinutes = 0;
		}
		List<TriggerSnapshotEntity> triggerSnapshots = sceneTriggerDao.listSnapshotByMatchedSceneAndTriggerActionAndActionDuration(ea, sceneType, sceneTargetId, triggerActionType, actionDurationMinutes, System.currentTimeMillis());
		triggerSnapshots.forEach(triggerSnapshot -> {
			SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, sceneType, sceneTargetId, triggerSnapshot.getTriggerId());
			if (sceneTrigger != null) {
				this.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), ImmutableList.of(new TriggerUserVO(marketingUserId, null)), paramMap);
			}
		});
	}

}
