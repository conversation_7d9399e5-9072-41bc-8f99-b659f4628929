package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName MarketingActivitySpreadRecordEntity
 * @Description
 * <AUTHOR>
 * @Date 2019/3/6 3:20 PM
 */
@Data
@Entity
public class MarketingActivitySpreadRecordEntity implements Serializable {
    private String id;
    private String ea;
    private Integer userId;
    private String marketingActivityId;
    private Date createTime;
    private Date updateTime;
    private String upstreamEa;
    private String memberId;
}
