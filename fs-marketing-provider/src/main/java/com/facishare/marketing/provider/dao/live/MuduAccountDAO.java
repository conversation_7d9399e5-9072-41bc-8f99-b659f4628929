package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.entity.live.MuduAccountEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MuduAccountDAO {
    @Insert("INSERT INTO mudu_account(id, ea, app_id, secret_key, create_time, update_time) VALUES(#{entity.id}, #{entity.ea}, #{entity.appId}, #{entity.secretKey}, now(), now())")
    int insert(@Param("entity")MuduAccountEntity entity);

    @Select("SELECT * FROM mudu_account WHERE ea=#{ea}")
    MuduAccountEntity getByEa(@Param("ea")String ea);

    @Select("SELCT ea FROM mudu_account")
    List<String> getBindEas();

    @Select("SELECT * FROM mudu_account WHERE app_id=#{appId} order by create_time desc limit 1")
    MuduAccountEntity getByAppId(@Param("appId")String appId);

    @Update("update mudu_account set app_id = #{entity.appId}, secret_key = #{entity.secretKey}, update_time = now() where ea = #{entity.ea}")
    int update(@Param("entity") MuduAccountEntity entity);
}
