package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.WorkspaceSyncSseService;
import com.facishare.marketing.common.sse.RedisMQ;
import com.facishare.marketing.common.sse.RedisMQHandler;
import com.alibaba.fastjson.JSONObject;
import com.github.jedis.support.MergeJedisCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 工作空间同步状态的 SSE 服务 V2版本
 * 基于现有RedisMQ机制实现多节点支持
 * 
 * 优势：
 * 1. 复用现有RedisMQ基础设施
 * 2. 支持多节点部署
 * 3. 实现简单，维护成本低
 * 4. 兼容现有代码结构
 */
@Service("workspaceSyncSseServiceV2")
@Slf4j
public class WorkspaceSyncSseServiceV2 implements WorkspaceSyncSseService {

    @Autowired
    private MergeJedisCmd jedisCmd;

    // 本地连接管理（用于即时推送）
    private final Map<String, CopyOnWriteArrayList<SseEmitter>> localEmitters = new ConcurrentHashMap<>();
    
    // Redis消息队列前缀
    private static final String WORKSPACE_SYNC_QUEUE_PREFIX = "workspace_sync_queue:";

    /**
     * 添加SSE连接
     * 支持两种模式：
     * 1. 本地连接：立即推送，0延迟
     * 2. Redis队列：跨节点消息传递
     */
    @Override
    public void addEmitter(String workspaceId, SseEmitter emitter) {
        // 1. 添加到本地连接管理
        addToLocalEmitters(workspaceId, emitter);
        
        log.info("为工作空间 {} 添加SSE连接，节点: {}", workspaceId, getNodeId());
    }

    /**
     * 发送同步状态
     * 混合策略：本地立即推送 + Redis队列跨节点推送
     */
    public void sendSyncStatus(String workspaceId, String status) {
        log.info("发送工作空间 {} 同步状态: {}，节点: {}", workspaceId, status, getNodeId());
        
        // 1. 立即推送到本地连接（0延迟）
        sendToLocalEmitters(workspaceId, status);
        
        // 2. 发送到Redis队列（跨节点）
        sendToRedisQueue(workspaceId, status);
    }

    /**
     * 创建基于Redis队列的SSE连接
     * 用于处理跨节点的消息推送
     */
    public SseEmitter createQueueBasedSseEmitter(String workspaceId) {
        String queueChannel = WORKSPACE_SYNC_QUEUE_PREFIX + workspaceId;
        
        // 使用现有的RedisMQHandler创建SSE连接
        SseEmitter emitter = RedisMQHandler.onMessage(jedisCmd, queueChannel);
        
        log.info("创建基于队列的SSE连接，工作空间: {}，频道: {}，节点: {}", 
                workspaceId, queueChannel, getNodeId());
        
        return emitter;
    }

    /**
     * 添加到本地连接管理
     */
    private void addToLocalEmitters(String workspaceId, SseEmitter emitter) {
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = 
                localEmitters.computeIfAbsent(workspaceId, k -> new CopyOnWriteArrayList<>());
        workspaceEmitters.add(emitter);

        // 注册回调
        emitter.onCompletion(() -> {
            workspaceEmitters.remove(emitter);
            if (workspaceEmitters.isEmpty()) {
                localEmitters.remove(workspaceId);
            }
            log.debug("工作空间 {} 的本地SSE连接完成，节点: {}", workspaceId, getNodeId());
        });

        emitter.onTimeout(() -> {
            emitter.complete();
            workspaceEmitters.remove(emitter);
            if (workspaceEmitters.isEmpty()) {
                localEmitters.remove(workspaceId);
            }
            log.debug("工作空间 {} 的本地SSE连接超时，节点: {}", workspaceId, getNodeId());
        });
    }

    /**
     * 推送到本地连接
     */
    private void sendToLocalEmitters(String workspaceId, String status) {
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = localEmitters.get(workspaceId);
        if (workspaceEmitters == null || workspaceEmitters.isEmpty()) {
            log.debug("工作空间 {} 在当前节点 {} 没有本地连接", workspaceId, getNodeId());
            return;
        }

        log.debug("向工作空间 {} 的 {} 个本地连接推送状态: {}，节点: {}", 
                workspaceId, workspaceEmitters.size(), status, getNodeId());

        for (SseEmitter emitter : workspaceEmitters) {
            try {
                emitter.send(SseEmitter.event().name("sync-status").data(status));
            } catch (IOException e) {
                log.warn("向工作空间 {} 的本地连接发送状态失败，节点: {}: {}", 
                        workspaceId, getNodeId(), e.getMessage());
            } catch (Exception e) {
                log.error("向工作空间 {} 的本地连接发送状态异常，节点: {}: {}", 
                        workspaceId, getNodeId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 发送到Redis队列（跨节点）
     */
    private void sendToRedisQueue(String workspaceId, String status) {
        try {
            String queueChannel = WORKSPACE_SYNC_QUEUE_PREFIX + workspaceId;
            
            JSONObject message = new JSONObject();
            message.put("workspaceId", workspaceId);
            message.put("status", status);
            message.put("timestamp", System.currentTimeMillis());
            message.put("nodeId", getNodeId());
            message.put("finish", true); // 标记消息完成，避免长时间等待
            
            // 使用现有的RedisMQ发布消息
            RedisMQ.publish(jedisCmd, queueChannel, message.toJSONString());
            
            log.debug("发送Redis队列消息，频道: {}，状态: {}，节点: {}", 
                    queueChannel, status, getNodeId());
            
        } catch (Exception e) {
            log.error("发送Redis队列消息失败，workspaceId: {}，status: {}，节点: {}", 
                    workspaceId, status, getNodeId(), e);
        }
    }

    /**
     * 获取当前节点ID
     */
    private String getNodeId() {
        String nodeId = System.getenv("NODE_ID");
        if (nodeId == null) {
            nodeId = System.getProperty("node.id", "unknown-" + System.currentTimeMillis());
        }
        return nodeId;
    }

    /**
     * 获取本地连接统计信息
     */
    public Map<String, Integer> getLocalConnectionStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        localEmitters.forEach((workspaceId, emitters) -> {
            stats.put(workspaceId, emitters.size());
        });
        return stats;
    }
}
