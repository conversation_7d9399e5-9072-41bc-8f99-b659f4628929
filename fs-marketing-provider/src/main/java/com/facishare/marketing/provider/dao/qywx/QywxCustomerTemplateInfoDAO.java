package com.facishare.marketing.provider.dao.qywx;


import com.facishare.marketing.provider.entity.qywx.QywxCustomerTemplateInfoEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created  By zhoux 2020/05/15
 **/
public interface QywxCustomerTemplateInfoDAO {

    @Update(" UPDATE qywx_customer_template_info SET suit_ticket = #{suitTicket},update_time =now() WHERE suit_id = #{suitId}")
    int updateSuitTicket(@Param("suitTicket") String suitTicket, @Param("suitId") String suitId);

    @Select(" SELECT * from qywx_customer_template_info where suit_id =#{suitId} ")
    QywxCustomerTemplateInfoEntity selectOne(@Param("suitId") String suitId);

    @Insert("insert into qywx_customer_template_info values (#{entity.id}, #{entity.suitId}, #{entity.secret}, #{entity.suitTicket},now(),now())")
    int insert(@Param("entity") QywxCustomerTemplateInfoEntity entity);


}
