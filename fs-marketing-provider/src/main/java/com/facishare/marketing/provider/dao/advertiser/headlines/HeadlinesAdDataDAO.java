package com.facishare.marketing.provider.dao.advertiser.headlines;

import com.facishare.marketing.provider.bo.advertise.AdCampaignDataStatisticsBO;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdDataEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdvertiserDataOverviewEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignDataEntity;
import com.facishare.marketing.provider.entity.baidu.TrendGraphDataDTOEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created by wang<PERSON><PERSON> on 2021/8/6 4:37 下午
 */
public interface HeadlinesAdDataDAO {
    @Insert("insert into headlines_ad_data (\"id\", \"ea\",\"ad_id\", \"campaign_id\", \"stat_datetime\", \"show\", \"click\", \"cost\", \"avg_click_cost\", \"create_time\", \"update_time\") values("
            + " #{obj.id},\n"
            + " #{obj.ea},\n"
            + " #{obj.adId},\n"
            + " #{obj.campaignId},\n"
            + " #{obj.statDatetime},\n"
            + " #{obj.show},\n"
            + " #{obj.click},\n"
            + " #{obj.cost},\n"
            + " #{obj.avgClickCost},\n"
            + " now(),\n"
            + " now()\n"
            + ") ON CONFLICT DO NOTHING;")
    void addHeadlinesAdData(@Param("obj") HeadlinesAdDataEntity adDataEntity);

    @Insert("<script>"
            + "INSERT INTO headlines_ad_data(\"id\", \"ea\", \"ad_id\", \"campaign_id\", \"stat_datetime\", " +
            "\"show\", \"click\", \"cost\", \"avg_click_cost\", \"leads\", \"create_time\", \"update_time\"," +
            " \"ad_account_id\", \"type\") VALUES"
            +  "  <foreach collection='adDataEntityList' item='item' separator=','>"
            +  "   (#{item.id}, #{item.ea}, #{item.adId}, #{item.campaignId}, #{item.statDatetime}, #{item.show}, #{item.click},#{item.cost}, #{item.avgClickCost}, #{item.leads}, now(), now(), #{item.adAccountId}, #{item.type})"
            +  "  </foreach>"
            +  "ON CONFLICT DO NOTHING;"
            + "</script>")
    int batchInsert(@Param("adDataEntityList") List<HeadlinesAdDataEntity> adDataEntityList);


    @Insert("insert into headlines_ad_data (\"id\", \"ea\",\"ad_id\", \"ad_account_id\", \"campaign_id\", \"stat_datetime\", \"create_time\") values("
            + " #{obj.id},\n"
            + " #{obj.ea},\n"
            + " #{obj.adId},\n"
            + " #{obj.adAccountId},\n"
            + " #{obj.campaignId},\n"
            + " #{obj.statDatetime},\n"
            + " now()\n"
            + ") ON CONFLICT DO NOTHING;")
    void addHeadlinesAdDataIgnore(@Param("obj") HeadlinesAdDataEntity adDataEntity);

    @Update("update headlines_ad_data set "
            + "\"campaign_id\" = #{obj.campaignId}, "
            + "\"show\" = #{obj.show}, "
            + "\"click\" = #{obj.click}, "
            + "\"cost\" = #{obj.cost}, "
            + "\"avg_click_cost\" = #{obj.avgClickCost}, "
            + "\"update_time\" = now()"
            + "  where \"id\" = #{obj.id} and \"stat_datetime\" = #{obj.statDatetime}")
    int updateHeadlinesAdDataRefreshData(@Param("obj") HeadlinesAdDataEntity headlinesAdDataEntity);

    @Update(" <script> "
            +" update headlines_ad_data as had set "
            + " \"campaign_id\"=tmp.campaignId, "
            + " \"show\"=tmp.show, "
            + " \"click\"=tmp.click, "
            + " \"cost\"=tmp.cost, "
            + " \"avg_click_cost\"=tmp.avgClickCost, "
            + " \"update_time\" = now() "
            + " from (values "
            + " <foreach collection='adDataEntityList' item='item' separator=','>"
            + " (#{item.id}, #{item.statDatetime}, #{item.campaignId}, #{item.show}, #{item.click}, #{item.cost}, #{item.avgClickCost}) "
            + " </foreach> "
            + " ) as tmp(id, statDateTime, campaignId, show, click, cost, avgClickCost) "
            + " where had.id=tmp.id and had.stat_datetime=tmp.statDatetime::date "
            + "</script>"
    )
    void batchUpdateHeadlinesAdDataRefreshData(@Param("adDataEntityList") List<HeadlinesAdDataEntity> headlinesAdDataEntityList);


    @Select("<script>"
            + "SELECT * FROM headlines_ad_data WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND ad_id=#{adId} AND stat_datetime=#{statDatetime}::DATE"
            + "</script>")
    HeadlinesAdDataEntity queryAdDataByDate(@Param("ea") String ea,@Param("adAccountId") String adAccountId, @Param("adId") Long adId, @Param("statDatetime") Date statDatetime);


    @Select("<script>  " +
            "SELECT * FROM headlines_ad_data \n"
            + "WHERE ea = #{ea} and campaign_id = #{campaignId} and action_date = #{actionDate}::DATE\n"
            + "</script>")
    HeadlinesCampaignDataEntity queryCampaignDataByDate(@Param("ea") String ea, @Param("campaignId") Long campaignId, @Param("actionDate") Date actionDate);


    @Select("<script>" +
            " SELECT COALESCE(sum(had.show),0) as show,COALESCE(sum(had.click),0) as click,COALESCE(sum(had.cost),0) as cost,COALESCE(sum(had.avg_click_cost),0) as avg_click_cost,COALESCE(sum(had.leads),0) as leads \n " +
            " FROM headlines_ad_data had " +
            " WHERE had.ea=#{ea} AND had.ad_id=#{adId} " +
            "    <if test=\"startTime != null\">\n"+
            "         AND had.stat_datetime &gt;= #{startTime}\n" +
            "    </if>\n"+
            "    <if test=\"adAccountId != null\">\n"+
            "         AND had.ad_account_id=#{adAccountId}\n" +
            "    </if>\n"+
            "    <if test=\"endTime != null\">\n" +
            "         AND had.stat_datetime &lt;= #{endTime}\n" +
            "    </if>\n" +
            "</script>")
    HeadlinesAdDataEntity queryAdDataByEaAndAdId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adId") Long adId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Select("SELECT * FROM headlines_ad_data WHERE id=#{id}")
    HeadlinesAdDataEntity queryAdDataById(@Param("id") String id);



    @Select("<script>  "
            + "SELECT sum(hcd.show) as show, sum(hcd.click) as click, sum(hcd.cost) as cost, sum(hcd.)\n"
            + "FROM headlines_campaign_data hcd \n"
            + "JOIN headlines_campaign hc on hc.campaign_id = hcd.campaign_id and hcd.ea = hc.ea\n"
            + "WHERE hcd.ea=#{ea} AND hcb.source=#{source}"
            + "    <if test=\"startTime != null\">\n"
            + "         AND hcd.action_date &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND hcd.action_date &lt;= #{endTime}\n"
            + "    </if>\n"
            + " <if test=\"marketingEventId != null\">\n"
            + "     and hc.marketing_event_id = #{marketingEventId} \n"
            + " </if>\n"
            + "</script>")
    HeadlinesAdvertiserDataOverviewEntity queryAdDataOverview(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("source") String source);

    @Select("<script>  "
            + "SELECT bcd.action_date as actionDate, COALESCE(bcd.pv,0), COALESCE(bcd.click,0), COALESCE(bcd.cost,0)\n"
            + "FROM baidu_campaign_data bcd \n"
            + "JOIN baidu_campaign bc on bc.campaign_id = bcd.campaign_id and bcd.ea = bc.ea\n"
            + "WHERE bc.ea=#{ea} and bc.marketing_event_id = #{marketingEventId} and bc.source = #{source}"
            + "    <if test=\"startTime != null\">\n"
            + "         AND bcd.action_date &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND bcd.action_date &lt;= #{endTime}\n"
            + "    </if>\n"
            + "ORDER BY bcd.action_date"
            + "</script>")
    List<TrendGraphDataDTOEntity> queryTrendGraphData(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("source") String source);

    @Select("<script>"
            + "SELECT bc.campaign_name as campaignName\n"
            + "from baidu_campaign bc\n"
            + "left JOIN baidu_campaign_data bcd on bcd.campaign_id = bc.campaign_id  and bcd.ea = bc.ea\n"
            + "    <if test=\"startTime != null\">\n"
            + "         AND bcd.action_date &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND bcd.action_date &lt;= #{endTime}\n"
            + "    </if>\n"
            + "WHERE bc.ea =#{ea} \n"
            + "GROUP BY bc.id\n"
            + "ORDER BY bc.marketing_event_id, bc.campaign_name"
            + "</script>")
    List<String> getAllCampaignDataByEa(@Param("ea")String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Insert("<script>"
            + "INSERT INTO headlines_ad_data(id,ea,ad_id,ad_account_id,campaign_id,\"stat_datetime\") "
            + "VALUES(#{obj.id}, #{obj.ea}, #{obj.adId}, #{obj.adAccountId}, #{obj.campaignId}, #{obj.statDatetime}::DATE)"
            + "</script>")
    void insertAdDataIgnore(@Param("obj") HeadlinesAdDataEntity adDataEntity);


    @Update("<script>"
            + "update headlines_ad_data set "
            + " \"leads\" = #{newLeads} "
            + "  where \"id\" = #{id} and \"leads\" = #{oldLeads}"
            + "</script>")
    int updateAdClue(@Param("id") String id, @Param("oldLeads") Integer oldLeads, @Param("newLeads") Integer newLeads);

    @Update("<script>"
            + "update headlines_ad_data set "
            + " \"leads\" = #{newLeads} "
            + "  where \"id\" = #{id} and \"leads\" is null "
            + "</script>")
    int updateAdClueNull(@Param("id") String id, @Param("newLeads") Integer newLeads);

    @Select("<script>  "
            + "SELECT COALESCE(SUM(leads),0) \n"
            + "FROM headlines_ad_data \n"
            + "WHERE ea = #{ea} \n"
            +	"<if test=\"adAccountId != null\">"
            +       "AND ad_account_id = #{adAccountId} \n"
            +   "</if>"
            +	"<if test=\"adId != null\">"
            +       "AND ad_id = #{adId} \n"
            +   "</if>"
            + "</script>")
    Integer queryClueCountByAdId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adId") Long adId);

    @Select("<script>  "
            + "SELECT *"
            + "FROM headlines_ad_data "
            + "WHERE ea = #{ea} AND ad_id = ANY( ARRAY "
            + "<foreach collection = 'adIds' item = 'item' index='index' open = '[' separator = ',' close = ']'>"
            + "         #{adIds[${index}],jdbcType=INTEGER}"
            + "</foreach> )"
            +	"<if test=\"startTime != null\">"
            +	    "AND stat_datetime &gt;= #{startTime}\n"
            +   "</if>"
            +	"<if test=\"endTime != null\">"
            +	    "AND stat_datetime &lt;= #{endTime}\n"
            +   "</if>"
            + "</script>")
    List<HeadlinesAdDataEntity> getByAdIdListAndDate(@Param("ea") String ea, @Param("adIds") List<Long> adIds,
                                                     @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>  "
            + "SELECT had.stat_datetime as actionDate, COALESCE(had.click,0) as click, COALESCE(had.cost,0) as cost\n"
            + "FROM headlines_ad_data had \n"
            + "JOIN headlines_ad ha on had.ad_id = ha.ad_id and had.ea = ha.ea\n"
            + "WHERE had.ea=#{ea} and ha.sub_marketing_event_id = #{marketingEventId} and ha.source = #{source}"
            + "    <if test=\"startTime != null\">\n"
            + "         AND had.stat_datetime &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND had.stat_datetime &lt;= #{endTime}\n"
            + "    </if>\n"
            + "ORDER BY had.stat_datetime"
            + "</script>")
    List<TrendGraphDataDTOEntity> queryTrendGraphDataByAdData(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("source") String source);

    @Select("<script>  "
            + "select * FROM headlines_ad_data \n"
            + "WHERE ea = #{ea} and ad_account_id=#{adAccountId} and type = #{type} and ad_id =ANY(ARRAY"
            +        "<foreach collection = 'adIdList' index ='index' item = 'cam' open = '[' separator = ',' close = ']'>"
            +           "#{adIdList[${index}]}"
            +       "</foreach> )"
            + " and stat_datetime = ANY(ARRAY"
            +        "<foreach collection = 'dateList' index ='index' item = 'cam' open = '[' separator = ',' close = ']'>"
            +           "#{dateList[${index}]}::DATE"
            +       "</foreach> )"
            + "</script>")
    List<HeadlinesAdDataEntity> queryByAdIdAndStatTimeDate(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adIdList") List<Long> adIdList, @Param("dateList") List<Date> dateList, @Param("type") String type);


    @Select("<script>"
            + "select * from (select sum(\"show\") totalPv, sum(cost) totalCost, sum(click) totalClick, ad_id adGroupId from headlines_ad_data"
                    + " where ea = #{ea} and stat_datetime >= #{beginTime} and stat_datetime <![CDATA[<=]]> #{endTime} "
                    + " AND ad_id = ANY(ARRAY"
                    +   "<foreach collection = 'headLinesAdIdList' index='index' item = 'item' open = '[' separator = ',' close = ']'>"
                    +       "#{headLinesAdIdList[${index}]}"
                    +   "</foreach>"
                    + " )"
                    + " group by ad_id) "
                    + " as a "
                    + " left join "
                    + " (select sum(\"show\") relativeTotalPv, sum(cost) relativeTotalCost, sum(click) relativeTotalClick, ad_id as relativeAdGroupId from headlines_ad_data "
                    + " where ea = #{ea} and stat_datetime >= #{relativeBeginTime} and stat_datetime <![CDATA[<=]]> #{relativeEndTime} "
                    + " AND ad_id = ANY(ARRAY"
                    +   "<foreach collection = 'headLinesAdIdList' index='index' item = 'item' open = '[' separator = ',' close = ']'>"
                    +      "#{headLinesAdIdList[${index}]}"
                    +   "</foreach>"
                    + " )"
                    + " group by ad_id) "
                    + " as b on ( a.adGroupId = b.relativeAdGroupId) "
            + "</script>"
    )
    List<AdCampaignDataStatisticsBO> statisticsAdDataGroupByAdIdList(@Param("ea") String ea, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                                                           @Param("relativeBeginTime") Date relativeBeginTime, @Param("relativeEndTime") Date relativeEndTime, @Param("headLinesAdIdList") List<Long> headLinesAdIdList);


    @Select("<script>"
            + "select * from headlines_ad_data  where ea = #{ea}"
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<HeadlinesAdDataEntity> scanById(@Param("ea") String ea, @Param("lastId") String lastId, @Param("limit") int limit);

    @Select( "select count(*) from headlines_ad_data  where ea = #{ea}")
    int countByEa( @Param("ea") String ea);

    @Select("<script>  "
            +"SELECT sum(click) FROM headlines_ad_data \n"
            + "WHERE ea = #{ea} and ad_account_id=#{adAccountId} and stat_datetime = #{actionDate}::DATE\n"
            + "</script>")
    Long sumClickByActionDate(@Param("ea") String ea,@Param("adAccountId") String adAccountId, @Param("actionDate") Date date);
}
