package com.facishare.marketing.provider.entity.qr;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class QRPosterUserEntity implements Serializable {
    /**
     * UUID自增主键
     */
    private String id;

    /**
     * 二维码海报ID
     * {@link com.facishare.marketing.provider.entity.qr.QRPosterEntity}
     */
    private String qrPosterId;

    /**
     * 二维码ID
     * {@link com.facishare.marketing.provider.entity.qr.QRCodeEntity}
     */
    private Integer qrCodeId;

    /**
     * 企业账号
     */
    private String ea;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 最终效果图apath
     */
    private String apath;

    /**
     * 最终效果图缩略图apath
     */
    private String thumbnailApath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 内容JSON（邀约海报时含名字）
     */
    private String content;

}
