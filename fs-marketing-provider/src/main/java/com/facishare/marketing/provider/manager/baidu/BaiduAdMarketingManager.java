package com.facishare.marketing.provider.manager.baidu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.result.baidu.QueryAccountInfoResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.api.vo.baidu.QueryAccountInfoVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.baidu.Filter;
import com.facishare.marketing.provider.baidu.*;
import com.facishare.marketing.provider.baidu.adgroup.AdGroupManager;
import com.facishare.marketing.provider.baidu.adgroup.GetAdGroupResultData;
import com.facishare.marketing.provider.baidu.campaign.GetCampaignResultData;
import com.facishare.marketing.provider.baidu.feed.*;
import com.facishare.marketing.provider.baidu.keyword.AdKeywordManager;
import com.facishare.marketing.provider.baidu.keyword.AdKeywordResultData;
import com.facishare.marketing.provider.baidu.report.*;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupBO;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupFeedBO;
import com.facishare.marketing.provider.dao.baidu.*;
import com.facishare.marketing.provider.dto.AdReportDataDTO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.baidu.*;
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg;
import com.facishare.marketing.provider.innerResult.UtmMarketingEventResult;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.util.CampaignInitDateUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.util.Make;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;

/**
 * Created by wangzhenyi on 2021/11/18 3:57 下午
 */
@Service("baiduAdMarketingManager")
@Slf4j
public class BaiduAdMarketingManager implements AdMarketingManager {
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private BaiduDataStatusDAO baiduDataStatusDAO;
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private BaiduCampaignDataDAO baiduCampaignDataDAO;
    @Autowired
    private BaiduCampaignDAO campaignDAO;
    @Autowired
    private CampaignApiManager campaignApiManager;
    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;
    @Autowired
    private ReportApiManager reportApiManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AdGroupManager adGroupManager;
    @Autowired
    private AdGroupDAO adGroupDAO;
    @Autowired
    private AdKeywordManager adKeywordManager;
    @Autowired
    private AdKeywordDAO adKeywordDAO;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private UtmDataManger utmDataManger;
    @Autowired
    private RefreshDataManager refreshDataManager;

    @Autowired
    private AdTokenManager adTokenManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @ReloadableProperty("baidu_campaign_list")
    private String campaignList;
    @ReloadableProperty("baidu_campaign_list_en")
    private String campaignListEN;

    @ReloadableProperty("baidu_campaign_to_keyword")
    private String baiduCampaignToKeyword;
    @ReloadableProperty("baidu_campaign_to_keyword_en")
    private String baiduCampaignToKeywordEN;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private BaiduHttpManager baiduHttpManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private BaiduProjectFeedDAO baiduProjectFeedDAO;

    @Autowired
    private BaiduCampaignFeedDAO baiduCampaignFeedDAO;

    @Autowired
    private BaiduAdGroupFeedDAO baiduAdGroupFeedDAO;

    @Autowired
    private BaiduAdGroupDataDAO baiduAdGroupDataDAO;

    @Autowired
    private BaiduAdGroupFeedDataDAO baiduAdGroupFeedDataDAO;
    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;
    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Override
    public Result<List<QueryAccountInfoResult>> queryAccountInfo(QueryAccountInfoVO vo) {
        List<QueryAccountInfoResult> resultList = Lists.newArrayList();
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEaAndSource(vo.getEa(), vo.getAdAccountId(), AdSourceEnum.getSourceByValue(vo.getSource()), true);
        if(dataPermissionManager.getNewDataPermissionSetting(vo.getEa())){
            List<String> websiteIds = dataPermissionManager.findAccessibleAdvertiseIds(vo.getEa(), vo.getFsUserId());
            if(CollectionUtils.isEmpty(websiteIds)){
                QueryAccountInfoResult result = new QueryAccountInfoResult();
                result.setEa(vo.getEa());
                result.setStatus(AccountStatusEnum.NOT_BIND_ACCOUNT.getStatus());
                resultList.add(result);
                return Result.newSuccess(resultList);
            }
            if(!websiteIds.contains(defaultAllChannel)){
                //过滤可见范围
                adAccountEntityList = adAccountEntityList.stream().filter(adAccountEntity -> websiteIds.contains(adAccountEntity.getId())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            QueryAccountInfoResult result = new QueryAccountInfoResult();
            result.setEa(vo.getEa());
            result.setStatus(AccountStatusEnum.NOT_BIND_ACCOUNT.getStatus());
            resultList.add(result);
            return Result.newSuccess(resultList);
        }


        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            BaiduDataStatusEntity statusEntity = baiduDataStatusDAO.queryRefreshStatus(vo.getEa(), adAccountEntity.getId(), adAccountEntity.getSource());
            QueryAccountInfoResult result = new QueryAccountInfoResult();
            if (statusEntity == null || statusEntity.getRefreshStatus() == null) {
                result.setRefreshStatus(DataRefreshStatusEnum.NONE.getStatus());
            } else {
                result.setRefreshStatus(statusEntity.getRefreshStatus());
                result.setRefreshSuccessTime(statusEntity.getRefreshSuccessTime() == null ? null : statusEntity.getRefreshSuccessTime().getTime());
                result.setRefreshTime(statusEntity.getRefreshTime() == null ? null : statusEntity.getRefreshTime().getTime());
            }
            result.setId(adAccountEntity.getId());
            result.setStatus(adAccountEntity.getStatus());
            result.setEa(vo.getEa());

            QueryAccountInfoResult.AccountInfo accountInfo = new QueryAccountInfoResult.AccountInfo();
            accountInfo.setAccountId(adAccountEntity.getAccountId());
            accountInfo.setUsername(adAccountEntity.getUsername());
            accountInfo.setBalance(adAccountEntity.getPcBalance() + adAccountEntity.getBalance());
            accountInfo.setBudget(adAccountEntity.getBudget());
            if (adAccountEntity.getCost() != null) {
                accountInfo.setCost(Math.round(adAccountEntity.getCost() * 100d) / 100d);
            }
            accountInfo.setBudgetType(adAccountEntity.getBudgetType());
            accountInfo.setUserStat(adAccountEntity.getUserStat());

            result.setAccountInfo(accountInfo);
            resultList.add(result);
        }
        return Result.newSuccess(resultList);
    }


    @Override
    public SHErrorCode isValidAccount(AdAccountEntity adAccountEntity) {
        RequestResult<GetAccountInfoResultData> requestResult = accountApiManager.getAccountInfo(adAccountEntity.getUsername().trim(), adAccountEntity.getPassword().trim(), adAccountEntity.getToken().trim(), adTokenManager.getBaiduAccessToken(adAccountEntity));
        if (Objects.isNull(requestResult)) {
            return SHErrorCode.BAIDU_API_REQUEST_ERROR;
        }
        return campaignDataManager.queryAccountInvalidCode(requestResult);
    }


    @Override
    public Result<GetDataOverviewResult> getDataOverview(GetDataOverviewVO vo) {
        GetDataOverviewResult dataResult = new GetDataOverviewResult();
        BigDecimal cost = BigDecimal.ZERO;
        Long pv = 0L;
        Long click = 0L;
        BigDecimal clickPrice = BigDecimal.ZERO;
        List<String> marketingEventIds = null;
        if (vo.getDataType() == null || TypeEnum.BAIDU_SEARCH_CAMPAIGN.getCode().equals(vo.getDataType())) {
            CampaignDataOverviewDTOEntity overviewDTOEntity = baiduCampaignDataDAO.queryCampaignDataOverview(vo.getEa(), vo.getAdAccountId(), vo.getStartTime(), vo.getEndTime(), vo.getMarketingEventId(), vo.getKeyword());
            if (overviewDTOEntity != null) {
                click = overviewDTOEntity.getClick();
                if (overviewDTOEntity.getCost() != null) {
                    cost = BigDecimal.valueOf(overviewDTOEntity.getCost());
                }
                pv = overviewDTOEntity.getPv();
                if (overviewDTOEntity.getClick() != null && overviewDTOEntity.getClick() > 0 && overviewDTOEntity.getCost() != null) {
                    clickPrice = BigDecimal.valueOf(overviewDTOEntity.getCost()).divide(BigDecimal.valueOf(click), 2, RoundingMode.HALF_UP);
                }
            }
            marketingEventIds = campaignDAO.getMarketingEventIdsByEaAndAdAccountId(vo.getEa(), vo.getAdAccountId(), AdSourceEnum.SOURCE_BAIDU.getSource(), vo.getKeyword());
        } else if (TypeEnum.BAIDU_SEARCH_AD_GROUP.getCode().equals(vo.getDataType())) {
            BaiduAdGroupBO baiduAdGroupBO = baiduAdGroupDataDAO.queryOverviewData(vo.getEa(), vo.getAdAccountId(), vo.getStartTime(), vo.getEndTime(), vo.getKeyword());
            if (baiduAdGroupBO != null) {
                pv = baiduAdGroupBO.getPv();
                click = baiduAdGroupBO.getClick();
                cost = baiduAdGroupBO.getCost();
                if (baiduAdGroupBO.getClick() != null && baiduAdGroupBO.getClick() > 0 && baiduAdGroupBO.getCost() != null) {
                    clickPrice = baiduAdGroupBO.getCost().divide(BigDecimal.valueOf(baiduAdGroupBO.getClick()), 2, RoundingMode.HALF_UP);
                }
            }
            marketingEventIds = adGroupDAO.getAllMarketingEventIdList(vo.getEa(), vo.getAdAccountId(), vo.getKeyword());
        }  else if (TypeEnum.BAIDU_FEED_AD_GROUP.getCode().equals(vo.getDataType())) {
            BaiduAdGroupFeedBO baiduAdGroupFeedBO = baiduAdGroupFeedDataDAO.queryOverviewData(vo.getEa(), vo.getAdAccountId(), vo.getStartTime(), vo.getEndTime(), vo.getKeyword());
            if (baiduAdGroupFeedBO != null) {
                pv = baiduAdGroupFeedBO.getPv();
                click = baiduAdGroupFeedBO.getClick();
                cost = baiduAdGroupFeedBO.getCost();
                if (baiduAdGroupFeedBO.getClick() != null && baiduAdGroupFeedBO.getClick() > 0 && baiduAdGroupFeedBO.getCost() != null) {
                    clickPrice = baiduAdGroupFeedBO.getCost().divide(BigDecimal.valueOf(baiduAdGroupFeedBO.getClick()), 2, RoundingMode.HALF_UP);
                }
            }
            marketingEventIds = baiduAdGroupFeedDAO.getAllMarketingEventIdList(vo.getEa(), vo.getAdAccountId(), vo.getKeyword());
        }
        dataResult.setPv(pv);
        dataResult.setClick(click);
        dataResult.setCost(cost == null ? 0D : cost.doubleValue());
        dataResult.setClickPrice(clickPrice == null ? 0D : clickPrice.doubleValue());
        if (CollectionUtils.isEmpty(marketingEventIds)) {
            dataResult.setLeads(0);
            return Result.newSuccess(dataResult);
        }
        int clueCount = campaignDataManager.getLeadsCountByMarketingEvent(vo.getEa(), marketingEventIds, vo.getStartTime().getTime(), vo.getEndTime().getTime(), null);
        dataResult.setLeads(clueCount);
        return Result.newSuccess(dataResult);
    }

    @Override
    public boolean refreshAccountInfo(AdAccountEntity accountInfo) {
        RequestResult<GetAccountInfoResultData> requestResult = accountApiManager.getAccountInfo(accountInfo.getUsername(), accountInfo.getPassword(), accountInfo.getToken(), adTokenManager.getBaiduAccessToken(accountInfo));
        log.info("CampaignDataManager refreshAccount getAccountInfo accountInfo: {} requestResult:{}", accountInfo, requestResult);
        if (requestResult != null && requestResult.isSuccess() && CollectionUtils.isNotEmpty(requestResult.getData())) {
            GetAccountInfoResultData resultData = requestResult.getData().get(0);
            accountInfo.setUserStat(resultData.getUserStat());
            accountInfo.setCost(resultData.getCost());
            accountInfo.setBudget(resultData.getBudget());
            accountInfo.setBudgetType(resultData.getBudgetType());
            accountInfo.setBalance(resultData.getBalance());
            accountInfo.setPcBalance(resultData.getPcBalance());
            accountInfo.setMobileBalance(resultData.getMobileBalance());
            adAccountManager.updateAccountRefreshData(accountInfo);
            return true;
        } else {
            log.warn("CampaignDataManager refreshAccount getAccountInfo failed,accountInfo: {} requestResult:{}", accountInfo, requestResult);
        }
        return false;
    }

    @Override
    public boolean refreshCampaignInfo(AdAccountEntity accountInfo) {
        try {
            RequestResult<GetCampaignResultData> requestResult = campaignApiManager.getCampaign(null, 1, accountInfo.getUsername(), accountInfo.getPassword(), accountInfo.getToken(), adTokenManager.getBaiduAccessToken(accountInfo));
            if (requestResult == null || !requestResult.isSuccess()) {
                log.info("BaiduAdMarketingManager refreshCampaignInfo getCampaign fail, requestResult:{}", requestResult);
                return false;
            }

            if (CollectionUtils.isEmpty(requestResult.getData())) {
                log.info("CampaignDataManager refreshCampaignInfo requestResult.getData is null");
                return false;
            }
            log.info("CampaignDataManager refreshCampaignInfo requestResult:{}", requestResult);
            requestResult.getData().forEach(campaignResultData -> {
                try {
                    BaiduCampaignEntity campaignEntity = baiduCampaignDAO.queryCampaignByCampaignId(accountInfo.getEa(), accountInfo.getId(), campaignResultData.getCampaignId());
                    if (campaignEntity == null) {
                        campaignEntity = new BaiduCampaignEntity();
                        campaignEntity.setId(UUIDUtil.getUUID());
                        campaignEntity.setEa(accountInfo.getEa());
                        campaignEntity.setAdAccountId(accountInfo.getId());
                        campaignEntity.setCampaignId(campaignResultData.getCampaignId());
                        campaignEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
                        baiduCampaignDAO.addCampaign(campaignEntity);
                    }
                    campaignEntity.setCampaignName(campaignResultData.getCampaignName());
                    campaignEntity.setBudget(campaignResultData.getBudget());
                    campaignEntity.setCampaignType(campaignResultData.getCampaignType());
                    campaignEntity.setDevice(campaignResultData.getDevice());
                    campaignEntity.setRefreshTime(new Date());
                    campaignEntity.setStatus(campaignResultData.getStatus());
                    if (campaignEntity.getAdAccountId() == null) {
                        campaignEntity.setAdAccountId(accountInfo.getId());
                    }

                    baiduCampaignDAO.updateCampaignForRefresh(campaignEntity);
                } catch (Exception e) {
                    log.info("CampaignDataManager refreshCampaignInfo campaignResultData: {}, exception: ", campaignResultData, e);
                }
            });
            syncCampaignToMarketingEventObj(accountInfo.getEa(), accountInfo, AdSourceEnum.SOURCE_BAIDU.getSource());
            return true;
        } catch (Exception e) {
            log.info("CampaignDataManager refreshCampaignInfo accountInfo: {}, exception: ", accountInfo, e);
        }
        return false;
    }

    public boolean refreshAdGroupData(AdAccountEntity adAccountEntity, int day) {
        try {
            int count = adGroupDAO.count(adAccountEntity.getEa(), adAccountEntity.getId());
            if (count <= 0) {
                log.info("refreshAdGroupData ad group is empty, account: {}", adAccountEntity);
                return true;
            }
            List<String> initDateList = CampaignInitDateUtil.initDateList(day);
            int pageSize = 500;
            String token = adTokenManager.getBaiduAccessToken(adAccountEntity);
            String userName = adAccountEntity.getUsername();
            GetReportDataRequest getReportDataRequest = new GetReportDataRequest();
            getReportDataRequest.setReportType(NewReportTypeEnum.AD_GROUP_REPORT.getType());
            getReportDataRequest.setTimeUnit("DAY");
            getReportDataRequest.setStartDate(initDateList.get(0));
            getReportDataRequest.setEndDate(initDateList.get(day - 1));
            getReportDataRequest.setColumns(Lists.newArrayList("adGroupId", "date", "impression", "click", "cost"));
            getReportDataRequest.setStartRow(0);
            getReportDataRequest.setRowCount(pageSize);
            List<GetReportDataResult> reportDataResultList = reportApiManager.getBaiduReportData(userName, token, getReportDataRequest);
            if (CollectionUtils.isEmpty(reportDataResultList)) {
                log.warn("refreshAdGroupData accountInfo :{}, getReportDataRequest:{}", adAccountEntity, getReportDataRequest);
                return false;
            }
            saveAdGroupData(adAccountEntity.getEa(), adAccountEntity.getId(), reportDataResultList);
            GetReportDataResult getReportDataResult = reportDataResultList.get(0);
            Integer totalRowCount = getReportDataResult.getTotalRowCount();
            if (totalRowCount != null && totalRowCount > pageSize) {
                int totalPage = totalRowCount % pageSize == 0 ? totalRowCount / pageSize : totalRowCount / pageSize + 1;
                for (int i = 1; i < totalPage; i++) {
                    getReportDataRequest.setStartRow(i * pageSize);
                    reportDataResultList = reportApiManager.getBaiduReportData(userName, token, getReportDataRequest);
                    saveAdGroupData(adAccountEntity.getEa(), adAccountEntity.getId(), reportDataResultList);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("refreshAdGroupData accountInfo :{}, exception:", adAccountEntity, e);
        }
        return false;
    }

    public boolean refreshAdGroupFeedData(AdAccountEntity adAccountEntity, int day) {
        try {
            int count = baiduAdGroupFeedDAO.count(adAccountEntity.getEa(), adAccountEntity.getId());
            if (count <= 0) {
                log.info("refreshAdGroupFeedData ad group feed is empty, account: {}", adAccountEntity);
                return true;
            }
            List<String> initDateList = CampaignInitDateUtil.initDateList(day);
            int pageSize = 500;
            String token = adTokenManager.getBaiduAccessToken(adAccountEntity);
            String userName = adAccountEntity.getUsername();
            GetReportDataRequest getReportDataRequest = new GetReportDataRequest();
            getReportDataRequest.setReportType(NewReportTypeEnum.AD_GROUP_FEED_REPORT.getType());
            getReportDataRequest.setTimeUnit("DAY");
            getReportDataRequest.setStartDate(initDateList.get(0));
            getReportDataRequest.setEndDate(initDateList.get(day - 1));
            getReportDataRequest.setColumns(Lists.newArrayList("adGroupId", "date", "impression", "click", "cost"));
            getReportDataRequest.setStartRow(0);
            getReportDataRequest.setRowCount(pageSize);
            List<GetReportDataResult> reportDataResultList = reportApiManager.getBaiduReportData(userName, token, getReportDataRequest);
            if (CollectionUtils.isEmpty(reportDataResultList)) {
                log.warn("refreshAdGroupData accountInfo :{}, getReportDataRequest:{}", adAccountEntity, getReportDataRequest);
                return false;
            }
            saveAdGroupFeedData(adAccountEntity.getEa(), adAccountEntity.getId(), reportDataResultList);
            GetReportDataResult getReportDataResult = reportDataResultList.get(0);
            Integer totalRowCount = getReportDataResult.getTotalRowCount();
            if (totalRowCount != null && totalRowCount > pageSize) {
                int totalPage = totalRowCount % pageSize == 0 ? totalRowCount / pageSize : totalRowCount / pageSize + 1;
                for (int i = 1; i < totalPage; i++) {
                    getReportDataRequest.setStartRow(i * pageSize);
                    reportDataResultList = reportApiManager.getBaiduReportData(userName, token, getReportDataRequest);
                    saveAdGroupFeedData(adAccountEntity.getEa(), adAccountEntity.getId(), reportDataResultList);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("refreshAdGroupFeedData accountInfo :{}, exception:", adAccountEntity, e);
        }
        return false;
    }

    private void saveAdGroupFeedData(String ea, String adAccountId, List<GetReportDataResult> reportDataResultList) {
        if (CollectionUtils.isEmpty(reportDataResultList)) {
            return;
        }
        GetReportDataResult getReportDataResult = reportDataResultList.get(0);
        List<Map<String, Object>> rowList = getReportDataResult.getRowList();
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        for (List<Map<String, Object>> partition : Lists.partition(rowList, 100)) {
            Set<Long> adGroupIdSet = Sets.newHashSet();
            Set<Date> actionDateSet = Sets.newHashSet();
            for (Map<String, Object> row : partition) {
                long adGroupFeedId = ((Double) row.get("adGroupId")).longValue();
                adGroupIdSet.add(adGroupFeedId);
                String date = (String) row.get("date");
                actionDateSet.add(DateUtil.parse( date + " 00:00:00"));
            }
            List<BaiduAdGroupFeedDataEntity> baiduAdGroupFeedDataEntityList = baiduAdGroupFeedDataDAO.queryByAdGroupFeedIdListAndActionDateList(ea, adAccountId, Lists.newArrayList(adGroupIdSet), Lists.newArrayList(actionDateSet));
            Map<String, BaiduAdGroupFeedDataEntity> keyToAdGroupFeedDataEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(baiduAdGroupFeedDataEntityList)) {
                keyToAdGroupFeedDataEntityMap = baiduAdGroupFeedDataEntityList.stream().collect(Collectors.toMap(e -> e.getAdGroupFeedId() + "-" + DateUtil.format2(e.getActionDate()), e -> e, (v1, v2) -> v1));
            }
            List<BaiduAdGroupFeedDataEntity> updateList = Lists.newArrayList();
            List<BaiduAdGroupFeedDataEntity> insertList = Lists.newArrayList();
            for (Map<String, Object> row : partition) {
                long pv = ((Double) row.get("impression")).longValue();
                long click = ((Double) row.get("click")).longValue();
                BigDecimal cost = new BigDecimal(row.get("cost").toString());
                long adGroupFeedId = ((Double) row.get("adGroupId")).longValue();
                String date = (String) row.get("date");
                String key = adGroupFeedId + "-" + date;
                BaiduAdGroupFeedDataEntity existDataEntity = keyToAdGroupFeedDataEntityMap.get(key);
                if (existDataEntity != null) {
                    BaiduAdGroupFeedDataEntity forUpdate = BeanUtil.copy(existDataEntity, BaiduAdGroupFeedDataEntity.class);
                    forUpdate.setPv(pv);
                    forUpdate.setClick(click);
                    forUpdate.setCost(cost);
                    updateList.add(forUpdate);
                } else {
                    BaiduAdGroupFeedDataEntity forInsert = new BaiduAdGroupFeedDataEntity();
                    forInsert.setEa(ea);
                    forInsert.setId(UUIDUtil.getUUID());
                    forInsert.setActionDate(DateUtil.parse(date + " 00:00:00"));
                    forInsert.setAdGroupFeedId(adGroupFeedId);
                    forInsert.setAdAccountId(adAccountId);
                    forInsert.setPv(pv);
                    forInsert.setClick(click);
                    forInsert.setCost(cost);
                    insertList.add(forInsert);
                }
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                baiduAdGroupFeedDataDAO.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                baiduAdGroupFeedDataDAO.batchUpdateDataById(ea, adAccountId, updateList);
            }
            List<BaiduAdGroupFeedDataEntity> allList = Lists.newArrayList(insertList);
            allList.addAll(updateList);
            buildAdvertisingDetailsObjByAdGroupFeedData(ea, adAccountId, allList);
        }
    }

    private void buildAdvertisingDetailsObjByAdGroupFeedData(String ea, String adAccountId, List<BaiduAdGroupFeedDataEntity> baiduAdGroupFeedDataEntityList) {
        baiduAdGroupFeedDataEntityList = baiduAdGroupFeedDataEntityList.stream().filter(e -> e.getPv() != null || e.getClick() != null || e.getCost() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baiduAdGroupFeedDataEntityList)) {
            return;
        }
        List<Long> adGroupFeedIdList = baiduAdGroupFeedDataEntityList.stream().map(BaiduAdGroupFeedDataEntity::getAdGroupFeedId).collect(Collectors.toList());
        List<BaiduAdGroupFeedEntity> adGroupFeedEntityList = baiduAdGroupFeedDAO.getByAdGroupFeedIdList(ea, adAccountId, adGroupFeedIdList);
        Map<Long, String> adGroupFeedIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adGroupFeedEntityList)) {
            adGroupFeedEntityList.forEach(e -> adGroupFeedIdToMarketingEventIdMap.put(e.getAdgroupFeedId(), e.getMarketingEventId()));
        }
        List<CreateAdvertisingDetailObjArg> argList = Lists.newArrayList();
        for (BaiduAdGroupFeedDataEntity entity : baiduAdGroupFeedDataEntityList) {
            CreateAdvertisingDetailObjArg arg = new CreateAdvertisingDetailObjArg();
            arg.setEa(entity.getEa());
            arg.setLaunchDate(DateUtil.format2(entity.getActionDate()));
            arg.setMarketingEventId(adGroupFeedIdToMarketingEventIdMap.get(entity.getAdGroupFeedId()));
            arg.setAdAccountId(entity.getAdAccountId());
            arg.setCampaignOrAdGroupId(entity.getAdGroupFeedId());
            arg.setShow(entity.getPv());
            arg.setClick(entity.getClick());
            arg.setCost(entity.getCost().doubleValue());
            argList.add(arg);
        }
        advertisingDetailsObjManager.tryUpdateOrCreateObj(argList);
    }

    @Override
    public boolean refreshCampaignData(AdAccountEntity accountInfo, int day) {
        try {
            List<String> initDateList = CampaignInitDateUtil.initDateList(day);
            GetRealTimeDataRequest body = new GetRealTimeDataRequest();
            RealTimeRequestType realTimeRequestType = new RealTimeRequestType();
            realTimeRequestType.setPerformanceData(Lists.newArrayList("impression", "click", "cost"));
            realTimeRequestType.setStartDate(initDateList.get(0));
            realTimeRequestType.setEndDate(initDateList.get(day - 1));
            realTimeRequestType.setLevelOfDetails(3);
            realTimeRequestType.setReportType(ReportTypeEnum.CAMPAIGN.getType());
            realTimeRequestType.setStatRange(2);
            realTimeRequestType.setUnitOfTime(5);
            realTimeRequestType.setNumber(1000);
            realTimeRequestType.setDevice(0);
            body.setRealTimeRequestType(realTimeRequestType);
            RequestResult<RealTimeResultType> realTimeData = reportApiManager.getRealTimeData(body, accountInfo.getUsername(), accountInfo.getPassword(), accountInfo.getToken(), adTokenManager.getBaiduAccessToken(accountInfo));
            if (realTimeData == null || !realTimeData.isSuccess()) {
                log.info("CampaignDataManager refreshCampaignData accountInfo :{}, realTimeData:{}", accountInfo, realTimeData);
                return false;
            }
            List<Long> campaignIdList = realTimeData.getData().stream().map(RealTimeResultType::getId).distinct().collect(Collectors.toList());
            Map<String, CampaignInitDateUtil.DataUnit> campaignOcpcReportToDataUnitMap = getOcpcReportData(accountInfo, campaignIdList, initDateList.get(0), initDateList.get(day - 1));

            List<CampaignInitDateUtil.DataUnit> dataUnits = Lists.newArrayList();
            realTimeData.getData().forEach(realTimeResult -> {
                try {
                    CampaignInitDateUtil.DataUnit dataUnit = new CampaignInitDateUtil.DataUnit(realTimeResult.getId(), DateUtil.parse(realTimeResult.getDate() + " 00:00:00"));
                    CampaignInitDateUtil.DataUnit ocpcDateUnit = campaignOcpcReportToDataUnitMap.get(realTimeResult.getId() + "-" + realTimeResult.getDate());
                    dataUnit.setPv(Long.valueOf(realTimeResult.getKpis().get(0)));
                    dataUnit.setClick(Long.valueOf(realTimeResult.getKpis().get(1)));
                    dataUnit.setCost(Double.valueOf(realTimeResult.getKpis().get(2)));
                    dataUnit.setOcpcConversions(ocpcDateUnit == null ? 0L : ocpcDateUnit.getOcpcConversions());
                    dataUnit.setDeepOcpcConversions(ocpcDateUnit == null ? 0L : ocpcDateUnit.getDeepOcpcConversions());
                    dataUnits.add(dataUnit);
                } catch (Exception e) {
                    log.error("CampaignDataManager refreshCampaignData accountInfo: {}, realTimeResult :{}, exception:", accountInfo, realTimeResult, e);
                }
            });
            saveCampaignData(accountInfo.getEa(), accountInfo.getId(), dataUnits);
            return true;
        } catch (Exception e) {
            log.error("CampaignDataManager refreshCampaignData accountInfo :{}, exception:", accountInfo, e);
        }
        return false;
    }

    private Map<String, CampaignInitDateUtil.DataUnit> getOcpcReportData(AdAccountEntity adAccountEntity, List<Long> campaignIdList, String startDate, String endDate) {
        Map<String, CampaignInitDateUtil.DataUnit> campaignOcpcReportToDataUnitMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return campaignOcpcReportToDataUnitMap;
        }
        GetReportDataRequest request = new GetReportDataRequest();
        request.setReportType(NewReportTypeEnum.OCPC_REPORT.getType());
        request.setTimeUnit("DAY");
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        List<Filter> filters = Lists.newArrayList();
        Filter filter = new Filter();
        filter.setColumn("campaignId");
        filter.setOperator(PaasAndCrmOperatorEnum.IN.getCrmOperator());
        filter.setValues(campaignIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        filters.add(filter);
        request.setFilters(filters);
        List<GetReportDataResult> results = reportApiManager.getBaiduReportData(adAccountEntity.getUsername(), adTokenManager.getBaiduAccessToken(adAccountEntity), request);
        log.info("getBaiduReportData account: {} request: {} result:{}", adAccountEntity, request, JsonUtil.toJson(results));
        for (GetReportDataResult reportDataResult : results) {
            for (Map<String, Object> rowMap : reportDataResult.getRowList()) {
                Object campaignIdObj = rowMap.get("campaignId");
                Object dateObj = rowMap.get("date");
                if (campaignIdObj == null || dateObj == null) {
                    continue;
                }
                Object ocpcConversionsObj = rowMap.get("ocpcConversions");
                Object deepOCPCConversionsObj = rowMap.get("deepOCPCConversions");
                long ocpcConversions = ocpcConversionsObj == null ? 0L : new BigDecimal(ocpcConversionsObj.toString()).longValue();
                long deepOcpcConversions = deepOCPCConversionsObj == null ? 0L : new BigDecimal(deepOCPCConversionsObj.toString()).longValue();
                CampaignInitDateUtil.DataUnit dataUnit = new CampaignInitDateUtil.DataUnit();
                dataUnit.setOcpcConversions(ocpcConversions);
                dataUnit.setDeepOcpcConversions(deepOcpcConversions);
                long campaignId = new BigDecimal(campaignIdObj.toString()).longValue();
                campaignOcpcReportToDataUnitMap.put(campaignId + "-" + dateObj, dataUnit);
            }
        }

        return campaignOcpcReportToDataUnitMap;
    }

    @Override
    public boolean refreshMarketingEventLeads(AdAccountEntity accountInfo, Integer day, List<Date> dateList) {
        List<BaiduCampaignEntity> campaignList = baiduCampaignDAO.listRelateMarketingEventCampaign(accountInfo.getEa(), accountInfo.getId());
        if (CollectionUtils.isEmpty(campaignList)) {
            return true;
        }
        List<List<BaiduCampaignEntity>> partitionList = Lists.partition(campaignList, 200);
        for (List<BaiduCampaignEntity> subCampaignList : partitionList) {
            Map<String, Map<String, CampaignInitDateUtil.DataUnit>> marketingEventDataMap = Maps.newHashMap();
            try {
                subCampaignList.forEach(campaign -> {
                    marketingEventDataMap.put(campaign.getMarketingEventId(), CollectionUtils.isNotEmpty(dateList) ? CampaignInitDateUtil.initDateMap(dateList, campaign.getCampaignId()) : CampaignInitDateUtil.initDateMap(day, campaign.getCampaignId()));
                });

                List<String> timeList = Lists.newArrayList();
                Date startTime = CollectionUtils.isNotEmpty(dateList) ? DateUtil.getSomeDay(new Date(), -30) : DateUtil.getSomeDay(new Date(), -day);
                Date endTime = new Date();
                timeList.add(DateUtil.getTimesMorning(startTime) + "");
                timeList.add(DateUtil.getTimesMorning(endTime) + "");
                int totalCount = crmV2Manager.getCrmObjectEntityTotalCount(accountInfo.getEa(), LeadsFieldContants.API_NAME, Lists.newArrayList(marketingEventDataMap.keySet()), timeList);
                int pageSize = 1000;
                int totalPage = totalCount / pageSize + totalCount % pageSize;
                for (int pageNum = 0; pageNum < totalPage; pageNum++) {
                    SearchQuery searchQuery = new SearchQuery();
                    searchQuery.setLimit(pageSize);
                    searchQuery.setOffset(pageNum * pageSize);
                    searchQuery.addFilter(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), Lists.newArrayList(marketingEventDataMap.keySet()), OperatorConstants.IN);
                    searchQuery.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), timeList, OperatorConstants.BETWEEN);
                    Page<ObjectData> page = crmV2Manager.getList(accountInfo.getEa(), -10000, LeadsFieldContants.API_NAME, searchQuery);
                    List<ObjectData> dataList = page.getDataList();
                    if (CollectionUtils.isEmpty(dataList)) {
                        log.info("BaiduAdMarketingManager refreshMarketingEventLeads crmV2Manager.getList fail, dataList is null");
                        return true;
                    }

                    for (ObjectData objectData : dataList) {
                        LeadsData leadsData = LeadsData.wrap(objectData);
                        try {
                            String marketingEventId = leadsData.getString(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName());
                            String createTime = DateUtil.parse(new Date(leadsData.getCreateTime()), DateUtil.DATE_FORMAT_DAY);
                            Map<String, CampaignInitDateUtil.DataUnit> dataUnitMap = marketingEventDataMap.get(marketingEventId);
                            if (dataUnitMap == null) {
                                continue;
                            }
                            CampaignInitDateUtil.DataUnit dataUnit = dataUnitMap.get(createTime);
                            if (dataUnit == null) {
                                continue;
                            }
                            dataUnit.increaseLeads();
                        } catch (Exception e) {
                            log.error("BaiduAdMarketingManager queryLeads handler objectData:{}, exception:", objectData, e);
                        }
                    }
                    for (Map<String, CampaignInitDateUtil.DataUnit> dataUnitMap : marketingEventDataMap.values()) {
                        saveCampaignData(accountInfo.getEa(), accountInfo.getId(), Lists.newArrayList(dataUnitMap.values()));
                    }
                }
                return true;
            } catch (Exception e) {
                log.error("BaiduAdMarketingManager queryLeads crmV2Manager.getList, exception:", e);
            }
        }
        return false;
    }


    @Override
    public void refreshAllData(String ea, String adAccountId, String source) {
        RequestResult<GetAccountInfoResultData> accountInfo = null;
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("refreshBaiduData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        int currentYear = DateUtil.getCurrentYear();
        if (currentYear >= 2023 && StringUtils.isBlank(adAccountEntity.getRefreshToken())) {
            log.info("百度广告账号没有完成新版鉴权:{}", adAccountEntity);
            return;
        }
        if (!adAccountEntity.getStatus().equals(AccountStatusEnum.NORMAL.getStatus()) && !adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
            log.info("syncCampaignDataToCRM account status is not normal accountEntity:{}", adAccountEntity);
            return;
        }

        try {
            accountInfo = accountApiManager.getAccountInfo(adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
            if (campaignDataManager.queryAccountInvalidCode(accountInfo) != SHErrorCode.SUCCESS) {
                log.info("refreshBaiduData baidu account is invalid accountInfo:{}", adAccountEntity);
                return;
            }
        } catch (Exception e) {
            log.info("refreshAllData getAccountInfo exception ea:{} adAccountId:{} source:{} e:", ea, adAccountId, source, e);
            return;
        }

        //同步数据到本地
        refreshBaiduCampaignData(adAccountEntity, source);
        //同步数据到CRM
        syncCampaignToMarketingEventObj(ea, adAccountEntity, source);
        if (adCommonManager.isSyncBaiduAdGroup(ea)) {
            //同步广告计划
            refreshAdGroup(adAccountEntity, source);
            syncAdGroupToMarketingEventObj(adAccountEntity);
        }
        // 刷新市场活动的ocpc投放字段
        refreshMarketingEventOOCPCLaunchByAccountId(ea, adAccountEntity);
        // 同步百度信息流广告——智投项目
        refreshProjectFeed(adAccountEntity);
        syncProjectFeedToMarketingEventObj(adAccountEntity);
        // 同步百度信息流广告——计划
        refreshCampaignFeed(adAccountEntity);
        syncCampaignFeedToMarketingEventObj(adAccountEntity);
        // 同步百度信息流广告——推广单元
        refreshAdGroupFeed(adAccountEntity);
        syncAdGroupFeedToMarketingEventObj(adAccountEntity);
    }

    @Override
    public void refreshKeywordData(String ea, String adAccountId, String source) {
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("refreshKeywordData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }
        if (!adCommonManager.isSyncAdKeyword(ea)) {
            log.info("baidu isSyncAdKeyword ea: {} is not sync keyword:", ea);
            return;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        if (!adAccountEntity.getStatus().equals(AccountStatusEnum.NORMAL.getStatus()) && !adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
            log.info("refreshKeywordData account status is not normal accountEntity:{}", adAccountEntity);
            return;
        }

        refreshKeyword(adAccountEntity, source);
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(ea, adAccountEntity.getId(), source);     //同步关键词
        //同步关键词投放计划
        refreshDataManager.syncKeywordServingPlanV2(ea, adAccountId, source);
        //同步前一天的数据
        String lastDayDate = DateUtil.parse(DateUtil.getSomeDay(new Date(), -1), DateUtil.DATE_FORMAT_DAY);
        /**
         * 注意：关键词投放明细的展点消不一定和推广计划的展点消数据对得上，原因有两个：
         * 1. 关键词投放明细只有每天凌晨3点同步，推广计划的展点消是每小时同步一次
         * 2. 百度有的关键词好像是自己预设进去的，这种关键词的展点消依旧会记在推广计划上，但是这种关键词我们从百度拉取不到，会导致数据对不上，例如纷享百度账号的关键词: ~自动定向
         * 关键词投放明细只能用来看词的展点消，不能用来统计，如果要统计看广告投放明细对象
         */
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, source, lastDayDate);
    }

    public void syncAdGroupToMarketingEventObj(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        int pageSize = 200;
        List<AdGroupEntity> adGroupEntityList = adGroupDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        String lastId;
        while(CollectionUtils.isNotEmpty(adGroupEntityList)) {
            refreshDataManager.batchSyncBaiduAdGroupToMarketingEventObj(ea, adAccountEntity, adGroupEntityList);
            lastId = adGroupEntityList.get(adGroupEntityList.size() - 1).getId();
            adGroupEntityList = adGroupDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public void syncProjectFeedToMarketingEventObj(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        int pageSize = 200;
        List<BaiduProjectFeedEntity> projectFeedEntityList = baiduProjectFeedDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        String lastId = null;
        while(CollectionUtils.isNotEmpty(projectFeedEntityList)) {
            refreshDataManager.batchSyncProjectFeedToMarketingEventObj(ea, adAccountEntity, projectFeedEntityList);
            lastId = projectFeedEntityList.get(projectFeedEntityList.size() - 1).getId();
            projectFeedEntityList = baiduProjectFeedDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public void syncCampaignFeedToMarketingEventObj(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        int pageSize = 200;
        List<BaiduCampaignFeedEntity> campaignFeedEntityList = baiduCampaignFeedDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        String lastId = null;
        while(CollectionUtils.isNotEmpty(campaignFeedEntityList)) {
            refreshDataManager.batchSyncBaiduCampaignFeedToMarketingEventObj(ea, adAccountEntity, campaignFeedEntityList);
            lastId = campaignFeedEntityList.get(campaignFeedEntityList.size() - 1).getId();
            campaignFeedEntityList = baiduCampaignFeedDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public void syncAdGroupFeedToMarketingEventObj(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        int pageSize = 200;
        List<BaiduAdGroupFeedEntity> adGroupFeedEntityList = baiduAdGroupFeedDAO.scanByAdAccountId(ea, adAccountId, null, pageSize);
        String lastId = null;
        while(CollectionUtils.isNotEmpty(adGroupFeedEntityList)) {
            refreshDataManager.batchSyncBaiduAdGroupFeedToMarketingEventObj(ea, adAccountEntity, adGroupFeedEntityList);
            lastId = adGroupFeedEntityList.get(adGroupFeedEntityList.size() - 1).getId();
            adGroupFeedEntityList = baiduAdGroupFeedDAO.scanByAdAccountId(ea, adAccountId, lastId, pageSize);
        }
    }

    public void refreshProjectFeed(AdAccountEntity adAccountEntity) {
        String token =  adTokenManager.getBaiduAccessToken(adAccountEntity);
        GetProjectFeedBody getProjectFeedBody = new GetProjectFeedBody();
        getProjectFeedBody.setProjectFeedFields(Lists.newArrayList("projectFeedId", "projectFeedName", "subject", "status", "pause", "bidMode", "addTime"));
        RequestResult<GetProjectFeedResult> result = campaignApiManager.getProjectFeed(getProjectFeedBody, adAccountEntity.getUsername(), token);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            log.info("refreshProjectFeed data is null, ea: {} adAccountId: {}", adAccountEntity.getEa(), adAccountEntity.getId());
            return ;
        }
        String ea = adAccountEntity.getEa();
        List<GetProjectFeedResult> latestProjectFeedList = result.getData();
        List<Long> projectFeedIds = latestProjectFeedList.stream().map(GetProjectFeedResult::getProjectFeedId).collect(Collectors.toList());
        List<BaiduProjectFeedEntity> existBaiduProjectFeedEntityList = baiduProjectFeedDAO.getByProjectFeedIdList(ea, adAccountEntity.getId(), projectFeedIds);
        Map<Long, BaiduProjectFeedEntity> existProjectFeedIdToEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(existBaiduProjectFeedEntityList)) {
            existBaiduProjectFeedEntityList.forEach(e -> existProjectFeedIdToEntityMap.put(e.getProjectFeedId(), e));
        }
        List<BaiduProjectFeedEntity> insertList = Lists.newArrayList();
        List<BaiduProjectFeedEntity> updateList = Lists.newArrayList();
        for (GetProjectFeedResult projectFeedResult : latestProjectFeedList) {
            BaiduProjectFeedEntity existProjectFeedEntity = existProjectFeedIdToEntityMap.get(projectFeedResult.getProjectFeedId());
            if (existProjectFeedEntity != null) {
                if (isProjectFeedChange(existProjectFeedEntity, projectFeedResult)) {
                    BaiduProjectFeedEntity updateProjectFeedEntity = BeanUtil.copy(projectFeedResult, BaiduProjectFeedEntity.class);
                    updateProjectFeedEntity.setEa(ea);
                    updateProjectFeedEntity.setAdAccountId(existProjectFeedEntity.getAdAccountId());
                    updateProjectFeedEntity.setId(existProjectFeedEntity.getId());
                    updateList.add(updateProjectFeedEntity);
                }
                continue;
            }
            BaiduProjectFeedEntity insertProjectFeedEntity = BeanUtil.copy(projectFeedResult, BaiduProjectFeedEntity.class);
            insertProjectFeedEntity.setAdAccountId(adAccountEntity.getId());
            insertProjectFeedEntity.setEa(ea);
            insertProjectFeedEntity.setId(UUIDUtil.getUUID());
            insertList.add(insertProjectFeedEntity);
        }
        log.info("refreshProjectFeed, ea: {} adAccountId: {} insertSize: {} updateSize: {}", adAccountEntity.getEa(), adAccountEntity.getId(), insertList.size(), updateList.size());
        for (List<BaiduProjectFeedEntity> baiduProjectFeedEntityList : Lists.partition(insertList, 100)) {
            baiduProjectFeedDAO.batchInsert(baiduProjectFeedEntityList);
        }
        for (List<BaiduProjectFeedEntity> baiduProjectFeedEntityList : Lists.partition(updateList, 100)) {
            baiduProjectFeedDAO.batchUpdate(ea, adAccountEntity.getId(), baiduProjectFeedEntityList);
        }
    }

    public void refreshCampaignFeed(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        String token =  adTokenManager.getBaiduAccessToken(adAccountEntity);
        GetCampaignFeedBody getCampaignFeedBody = new GetCampaignFeedBody();
        getCampaignFeedBody.setCampaignFeedFields(Lists.newArrayList("addtime", "projectFeedId", "campaignFeedId", "campaignFeedName", "subject", "budget", "starttime", "endtime", "status", "campaignType", "pause"));
        RequestResult<GetCampaignFeedResult> result = campaignApiManager.getCampaignFeed(getCampaignFeedBody, adAccountEntity.getUsername(), token);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            log.info("refreshCampaignFeed data is null, ea: {} adAccountId: {}", adAccountEntity.getEa(), adAccountEntity.getId());
            return;
        }
        List<GetCampaignFeedResult> latestCampaignFeedList = result.getData();
        List<Long> campaignFeedIds = latestCampaignFeedList.stream().map(GetCampaignFeedResult::getCampaignFeedId).collect(Collectors.toList());
        List<BaiduCampaignFeedEntity> existBaiduCampaignFeedEntityList = baiduCampaignFeedDAO.getByCampaignFeedIdList(ea, adAccountId, campaignFeedIds);
        Map<Long, BaiduCampaignFeedEntity> existCampaignFeedIdToEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(existBaiduCampaignFeedEntityList)) {
            existBaiduCampaignFeedEntityList.forEach(e -> existCampaignFeedIdToEntityMap.put(e.getCampaignFeedId(), e));
        }
        List<BaiduCampaignFeedEntity> insertList = Lists.newArrayList();
        List<BaiduCampaignFeedEntity> updateList = Lists.newArrayList();
        for (GetCampaignFeedResult campaignFeedResult : latestCampaignFeedList) {
            BaiduCampaignFeedEntity existCampaignFeedEntity = existCampaignFeedIdToEntityMap.get(campaignFeedResult.getCampaignFeedId());
            if (existCampaignFeedEntity != null) {
                if (isCampaignFeedChange(existCampaignFeedEntity, campaignFeedResult)) {
                    BaiduCampaignFeedEntity updateCampaignFeedEntity = BeanUtil.copy(campaignFeedResult, BaiduCampaignFeedEntity.class);
                    updateCampaignFeedEntity.setEa(ea);
                    updateCampaignFeedEntity.setAdAccountId(existCampaignFeedEntity.getAdAccountId());
                    updateCampaignFeedEntity.setId(existCampaignFeedEntity.getId());
                    updateList.add(updateCampaignFeedEntity);
                }
                continue;
            }
            BaiduCampaignFeedEntity insertCampaignFeedEntity = BeanUtil.copy(campaignFeedResult, BaiduCampaignFeedEntity.class);
            insertCampaignFeedEntity.setAdAccountId(adAccountId);
            insertCampaignFeedEntity.setEa(ea);
            insertCampaignFeedEntity.setId(UUIDUtil.getUUID());
            insertList.add(insertCampaignFeedEntity);
        }
        log.info("refreshCampaignFeed, ea: {} adAccountId: {} insertSize: {} updateSize: {}", adAccountEntity.getEa(), adAccountEntity.getId(), insertList.size(), updateList.size());
        for (List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList : Lists.partition(insertList, 100)) {
            baiduCampaignFeedDAO.batchInsert(baiduCampaignFeedEntityList);
        }
        for (List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList : Lists.partition(updateList, 100)) {
            baiduCampaignFeedDAO.batchUpdate(ea, adAccountId, baiduCampaignFeedEntityList);
        }
    }

    public void refreshAdGroupFeed(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        List<Long> allCampaignFeedIdList = baiduCampaignFeedDAO.getAllCampaignFeedIdList(ea, adAccountId);
        if (CollectionUtils.isEmpty(allCampaignFeedIdList)) {
            log.info("refreshAdGroupFeed campaign feed is empty, ea: {} adAccountId: {}", ea, adAccountId);
            return;
        }
        for (List<Long> campaignFeedIdList : Lists.partition(allCampaignFeedIdList, 50)) {
            String token =  adTokenManager.getBaiduAccessToken(adAccountEntity);
            GetAdGroupFeedBody getAdGroupFeedBody = new GetAdGroupFeedBody();
            getAdGroupFeedBody.setAdgroupFeedFields(Lists.newArrayList("adgroupFeedId", "campaignFeedId", "adgroupFeedName", "pause", "addtime", "modtime"));
            getAdGroupFeedBody.setIdType(1);
            getAdGroupFeedBody.setIds(campaignFeedIdList);
            RequestResult<GetAdGroupFeedResult> result = campaignApiManager.getAdgroupFeed(getAdGroupFeedBody, adAccountEntity.getUsername(), token);
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                log.info("refreshAdGroupFeed campaign feed do not hava ad adgroup feed, ea: {} adAccountId: {} campaignIdList: {}", ea, adAccountId, campaignFeedIdList);
                continue;
            }
            List<GetAdGroupFeedResult> latestAdGroupFeedList = result.getData();
            List<Long> adGroupFeedIds = latestAdGroupFeedList.stream().map(GetAdGroupFeedResult::getAdgroupFeedId).collect(Collectors.toList());
            List<BaiduAdGroupFeedEntity> existBaiduAdGroupFeedEntityList = baiduAdGroupFeedDAO.getByAdGroupFeedIdList(ea, adAccountId, adGroupFeedIds);
            Map<Long, BaiduAdGroupFeedEntity> existAdGroupFeedIdToEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(existBaiduAdGroupFeedEntityList)) {
                existBaiduAdGroupFeedEntityList.forEach(e -> existAdGroupFeedIdToEntityMap.put(e.getAdgroupFeedId(), e));
            }
            List<BaiduAdGroupFeedEntity> insertList = Lists.newArrayList();
            List<BaiduAdGroupFeedEntity> updateList = Lists.newArrayList();
            for (GetAdGroupFeedResult adGroupFeedResult : latestAdGroupFeedList) {
                BaiduAdGroupFeedEntity existAdGroupFeedEntity = existAdGroupFeedIdToEntityMap.get(adGroupFeedResult.getAdgroupFeedId());
                if (existAdGroupFeedEntity != null) {
                    if (isAdGroupFeedChange(existAdGroupFeedEntity, adGroupFeedResult)) {
                        BaiduAdGroupFeedEntity updateAdGroupFeedEntity = BeanUtil.copy(adGroupFeedResult, BaiduAdGroupFeedEntity.class);
                        updateAdGroupFeedEntity.setEa(ea);
                        updateAdGroupFeedEntity.setAdAccountId(existAdGroupFeedEntity.getAdAccountId());
                        updateAdGroupFeedEntity.setId(existAdGroupFeedEntity.getId());
                        updateList.add(updateAdGroupFeedEntity);
                    }
                    continue;
                }
                BaiduAdGroupFeedEntity insertAdGroupFeedEntity = BeanUtil.copy(adGroupFeedResult, BaiduAdGroupFeedEntity.class);
                insertAdGroupFeedEntity.setAdAccountId(adAccountId);
                insertAdGroupFeedEntity.setEa(ea);
                insertAdGroupFeedEntity.setId(UUIDUtil.getUUID());
                insertList.add(insertAdGroupFeedEntity);
            }
            log.info("refreshAdGroupFeed, ea: {} adAccountId: {} insertSize: {} updateSize: {}", adAccountEntity.getEa(), adAccountEntity.getId(), insertList.size(), updateList.size());
            for (List<BaiduAdGroupFeedEntity> baiduAdGroupFeedEntityList : Lists.partition(insertList, 100)) {
                baiduAdGroupFeedDAO.batchInsert(baiduAdGroupFeedEntityList);
            }
            for (List<BaiduAdGroupFeedEntity> baiduAdGroupFeedEntityList : Lists.partition(updateList, 100)) {
                baiduAdGroupFeedDAO.batchUpdate(ea, adAccountId, baiduAdGroupFeedEntityList);
            }
        }
    }

    private boolean isAdGroupFeedChange(BaiduAdGroupFeedEntity existAdGroupFeedEntity, GetAdGroupFeedResult adGroupFeedResult) {
        if (!existAdGroupFeedEntity.getAdgroupFeedName().equals(adGroupFeedResult.getAdgroupFeedName())) {
            return true;
        }
        if (!Objects.equals(existAdGroupFeedEntity.getPause(), adGroupFeedResult.getPause())) {
            return true;
        }
        if (!Objects.equals(existAdGroupFeedEntity.getStatus(), adGroupFeedResult.getStatus())) {
            return true;
        }
        if (!Objects.equals(existAdGroupFeedEntity.getAddTime(), adGroupFeedResult.getAddTime())) {
            return true;
        }
        return !Objects.equals(existAdGroupFeedEntity.getModifyTime(), adGroupFeedResult.getModifyTime());
    }

    private boolean isCampaignFeedChange(BaiduCampaignFeedEntity oldCampaignFeedEntity, GetCampaignFeedResult campaignFeedResult) {
        if (!oldCampaignFeedEntity.getCampaignFeedName().equals(campaignFeedResult.getCampaignFeedName())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getSubject(), campaignFeedResult.getSubject())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getBudget(), campaignFeedResult.getBudget())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getAddTime(), campaignFeedResult.getAddTime())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getStartTime(), campaignFeedResult.getStartTime())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getEndTime(), campaignFeedResult.getEndTime())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getStatus(), campaignFeedResult.getStatus())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getCampaignType(), campaignFeedResult.getCampaignType())) {
            return true;
        }
        if (!Objects.equals(oldCampaignFeedEntity.getPause(), campaignFeedResult.getPause())) {
            return true;
        }
        return false;
    }

    private static boolean isProjectFeedChange(BaiduProjectFeedEntity oldProjectFeedEntity, GetProjectFeedResult projectFeedResult) {
        if (!oldProjectFeedEntity.getProjectFeedName().equals(projectFeedResult.getProjectFeedName())) {
            return true;
        }
        if (!Objects.equals(oldProjectFeedEntity.getSubject(), projectFeedResult.getSubject())) {
            return true;
        }
        if (!Objects.equals(oldProjectFeedEntity.getStatus(), projectFeedResult.getStatus())) {
            return true;
        }
        if (!Objects.equals(oldProjectFeedEntity.getPause(), projectFeedResult.getPause())) {
            return true;
        }
        if (!Objects.equals(oldProjectFeedEntity.getBidMode(), projectFeedResult.getBidMode())) {
            return true;
        }
        return !Objects.equals(oldProjectFeedEntity.getAddTime(), projectFeedResult.getAddTime());
    }

    private void saveAdGroupData(String ea, String adAccountId, List<GetReportDataResult> reportDataResultList) {
        if (CollectionUtils.isEmpty(reportDataResultList)) {
            return;
        }
        List<Map<String, Object>> rowList = reportDataResultList.get(0).getRowList();
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        for (List<Map<String, Object>> partition : Lists.partition(rowList, 100)) {
            Set<Long> adGroupIdSet = Sets.newHashSet();
            Set<Date> actionDateSet = Sets.newHashSet();
            for (Map<String, Object> row : partition) {
                long adGroupId = ((Double) row.get("adGroupId")).longValue();
                adGroupIdSet.add(adGroupId);
                String date = (String) row.get("date");
                actionDateSet.add(DateUtil.parse( date + " 00:00:00"));
            }
            List<BaiduAdGroupDataEntity> baiduAdGroupDataEntityList = baiduAdGroupDataDAO.queryByAdGroupIdListAndActionDateList(ea, adAccountId, Lists.newArrayList(adGroupIdSet), Lists.newArrayList(actionDateSet));
            Map<String, BaiduAdGroupDataEntity> keyToAdGroupDataEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(baiduAdGroupDataEntityList)) {
                keyToAdGroupDataEntityMap = baiduAdGroupDataEntityList.stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "-" + DateUtil.format2(e.getActionDate()), e -> e, (v1, v2) -> v1));
            }
            List<BaiduAdGroupDataEntity> updateList = Lists.newArrayList();
            List<BaiduAdGroupDataEntity> insertList = Lists.newArrayList();
            for (Map<String, Object> row : partition) {
                long pv = ((Double) row.get("impression")).longValue();
                long click = ((Double) row.get("click")).longValue();
                BigDecimal cost = new BigDecimal(row.get("cost").toString());
                long adGroupId = ((Double) row.get("adGroupId")).longValue();
                String date = (String) row.get("date");
                String key = adGroupId + "-" + date;
                BaiduAdGroupDataEntity existDataEntity = keyToAdGroupDataEntityMap.get(key);
                if (existDataEntity != null) {
                    BaiduAdGroupDataEntity forUpdate = BeanUtil.copy(existDataEntity, BaiduAdGroupDataEntity.class);
                    forUpdate.setPv(pv);
                    forUpdate.setClick(click);
                    forUpdate.setCost(cost);
                    updateList.add(forUpdate);
                } else {
                    BaiduAdGroupDataEntity forInsert = new BaiduAdGroupDataEntity();
                    forInsert.setEa(ea);
                    forInsert.setId(UUIDUtil.getUUID());
                    forInsert.setActionDate(DateUtil.parse(date + " 00:00:00"));
                    forInsert.setAdGroupId(adGroupId);
                    forInsert.setAdAccountId(adAccountId);
                    forInsert.setPv(pv);
                    forInsert.setClick(click);
                    forInsert.setCost(cost);
                    insertList.add(forInsert);
                }
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                baiduAdGroupDataDAO.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                baiduAdGroupDataDAO.batchUpdateDataById(ea, adAccountId, updateList);
            }
            List<BaiduAdGroupDataEntity> allList = Lists.newArrayList(insertList);
            allList.addAll(updateList);
            buildAdvertisingDetailsObjByAdGroupData(ea, adAccountId, allList);
        }
    }

    private void buildAdvertisingDetailsObjByAdGroupData(String ea, String adAccountId, List<BaiduAdGroupDataEntity> baiduAdGroupDataEntityList) {
        baiduAdGroupDataEntityList = baiduAdGroupDataEntityList.stream().filter(e -> e.getPv() != null || e.getClick() != null || e.getCost() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baiduAdGroupDataEntityList)) {
            return;
        }
        List<Long> adGroupIdList = baiduAdGroupDataEntityList.stream().map(BaiduAdGroupDataEntity::getAdGroupId).collect(Collectors.toList());
        List<AdGroupEntity> adGroupEntityList = adGroupDAO.queryByAdgroupIdList(ea, adAccountId, adGroupIdList);
        Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adGroupEntityList)) {
            adGroupEntityList.forEach(e -> adGroupIdToMarketingEventIdMap.put(e.getAdGroupId(), e.getMarketingEventId()));
        }
        List<CreateAdvertisingDetailObjArg> argList = Lists.newArrayList();
        for (BaiduAdGroupDataEntity entity : baiduAdGroupDataEntityList) {
            CreateAdvertisingDetailObjArg arg = new CreateAdvertisingDetailObjArg();
            arg.setEa(entity.getEa());
            arg.setLaunchDate(DateUtil.format2(entity.getActionDate()));
            arg.setMarketingEventId(adGroupIdToMarketingEventIdMap.get(entity.getAdGroupId()));
            arg.setAdAccountId(entity.getAdAccountId());
            arg.setCampaignOrAdGroupId(entity.getAdGroupId());
            arg.setShow(entity.getPv());
            arg.setClick(entity.getClick());
            arg.setCost(entity.getCost().doubleValue());
            argList.add(arg);
        }
        advertisingDetailsObjManager.tryUpdateOrCreateObj(argList);
    }

    private void saveCampaignData(String ea, String adAccountId, List<CampaignInitDateUtil.DataUnit> dataUnits) {
        if (CollectionUtils.isEmpty(dataUnits)) {
            return;
        }
       // List<BaiduCampaignDataEntity> campaignDataEntityList = Lists.newArrayList();
        dataUnits.forEach(dataUnit -> {
            try {
                BaiduCampaignDataEntity dataEntity = baiduCampaignDataDAO.queryCampaignDataByDate(ea, adAccountId, dataUnit.getCampaignId(), dataUnit.getActionDate());
                if (dataEntity == null) {
                    baiduCampaignDataDAO.insertCampaignDataIgnore(UUIDUtil.getUUID(), ea, adAccountId, dataUnit.getCampaignId(), dataUnit.getActionDate());
                    dataEntity = baiduCampaignDataDAO.queryCampaignDataByDate(ea, adAccountId, dataUnit.getCampaignId(), dataUnit.getActionDate());
                    dataEntity.setClick(dataUnit.getClick());
                    dataEntity.setPv(dataUnit.getPv());
                    dataEntity.setCost(dataUnit.getCost());
                    dataEntity.setOcpcConversions(dataUnit.getOcpcConversions());
                    dataEntity.setDeepOcpcConversions(dataUnit.getDeepOcpcConversions());
                }
                baiduCampaignDataDAO.updateCampaignRefreshData(dataEntity.getId(), dataUnit.getPv(), dataUnit.getClick(), dataUnit.getCost(), dataUnit.getLeads(), adAccountId, dataUnit.getOcpcConversions(), dataUnit.getDeepOcpcConversions());
                //campaignDataEntityList.add(dataEntity);
            } catch (Exception e) {
                log.error("BaiduAdMarketingManager saveCampaignData failed, dataUnit={}, exception:", dataUnit, e);
            }
        });
        // 百度广告不在同步广告计划维度的广告投放明细
        //buildAdvertisingDetailsObj(ea, campaignDataEntityList);
    }

    private void buildAdvertisingDetailsObj(String ea, List<BaiduCampaignDataEntity> campaignDataEntityList) {
        campaignDataEntityList = campaignDataEntityList.stream().filter(e -> e.getPv() != null || e.getClick() != null || e.getCost() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignDataEntityList)) {
            return;
        }
        List<Long> campaignIdList = campaignDataEntityList.stream().map(BaiduCampaignDataEntity::getCampaignId).collect(Collectors.toList());
        List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryMarketingEventIdByCampaignIdList(ea, campaignIdList);
        Map<Long, String> campaignIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(baiduCampaignEntityList)) {
            baiduCampaignEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).forEach(e -> campaignIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
        }
        List<CreateAdvertisingDetailObjArg> argList = Lists.newArrayList();
        for (BaiduCampaignDataEntity entity : campaignDataEntityList) {
            CreateAdvertisingDetailObjArg arg = new CreateAdvertisingDetailObjArg();
            arg.setEa(entity.getEa());
            arg.setLaunchDate(DateUtil.format2(entity.getActionDate()));
            arg.setMarketingEventId(campaignIdToMarketingEventIdMap.get(entity.getCampaignId()));
            arg.setAdAccountId(entity.getAdAccountId());
            arg.setCampaignOrAdGroupId(entity.getCampaignId());
            arg.setShow(entity.getPv());
            arg.setClick(entity.getClick());
            arg.setCost(entity.getCost());
            arg.setOcpcConversions(entity.getOcpcConversions());
            arg.setDeepOcpcConversions(entity.getDeepOcpcConversions());
            argList.add(arg);
        }
        advertisingDetailsObjManager.tryUpdateOrCreateObj(argList);
    }

    public void reindexAdvertisingDetailsObj(String ea) {
        int totalCount = baiduCampaignDataDAO.countByEa(ea);
        int count = 0;
        String lastId = null;
        int pageSize = 2000;
        while (count < totalCount) {
            long t1 = System.currentTimeMillis();
            List<BaiduCampaignDataEntity> list = baiduCampaignDataDAO.scanById(ea, lastId, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            count += list.size();
            lastId = list.get(list.size() - 1).getId();
            buildAdvertisingDetailsObj(ea, list);
            log.info("baidu reindexAdvertisingDetailsObj ea: {}, totalCount: {}, count:{} 耗时: {}ms", ea, totalCount, count, System.currentTimeMillis() - t1);
        }
    }

    /**
     * 同步指定企业广告数据
     *
     * @param adAccountEntity
     */
    private void refreshBaiduCampaignData(AdAccountEntity adAccountEntity, String source) {
        RequestResult<GetCampaignResultData> requestResult = null;
        try {
            requestResult = campaignApiManager
                    .getCampaign(null, 1, adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
            if (requestResult == null || !requestResult.isSuccess() || CollectionUtils.isEmpty(requestResult.getData())) {
                log.info("refreshAdDataByEa failed get campaign data null ea:{} requestResult:{}", adAccountEntity.getEa(), requestResult);
                return;
            }
        } catch (Exception e) {
            log.info("refreshBaiduCampaignData exception adAccountEntity:{} source:{} e:", adAccountEntity, source, e);
            return;
        }

        syncCampaignData(adAccountEntity, source, requestResult.getData());
    }

    public void syncCampaignData(AdAccountEntity adAccountEntity, String source, List<GetCampaignResultData> campaignResultDataList) {
        PageUtil pageUtil = new PageUtil(campaignResultDataList, 100);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<GetCampaignResultData> currentPage = pageUtil.getPagedList(i);
            List<Long> campaignIds = currentPage.stream().filter(e -> e.getCampaignId() != null).map(GetCampaignResultData::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(campaignIds)) {
                continue;
            }
            List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryCampaignByCampaignByIds(adAccountEntity.getEa(), adAccountEntity.getId(), source, campaignIds);
            Map<Long, BaiduCampaignEntity> existCampaignMap = null;
            if (CollectionUtils.isNotEmpty(baiduCampaignEntityList)) {
                existCampaignMap = baiduCampaignEntityList.stream().collect(Collectors.toMap(BaiduCampaignEntity::getCampaignId, Function.identity(), (v1, v2) -> v2));
            }
            List<GetCampaignResultData> addCampaignDataList = Lists.newArrayList();  //新增广告推广
            List<BaiduCampaignEntity> updateCampaignDataList = Lists.newArrayList(); //更新广告推广

            for (GetCampaignResultData campaignResultData : currentPage) {
                if (existCampaignMap == null || existCampaignMap.get(campaignResultData.getCampaignId()) == null) {
                    addCampaignDataList.add(campaignResultData);
                } else {
                    BaiduCampaignEntity updateEntity = existCampaignMap.get(campaignResultData.getCampaignId());
                    updateEntity.setCampaignName(campaignResultData.getCampaignName());
                    if (campaignResultData.getBudget() == null) {
                        campaignResultData.setBudget(0.0);
                    }
                    updateEntity.setBudget(campaignResultData.getBudget());
                    updateEntity.setCampaignType(campaignResultData.getCampaignType());
                    updateEntity.setDevice(campaignResultData.getDevice());
                    updateEntity.setRefreshTime(new Date());
                    updateEntity.setStatus(campaignResultData.getStatus());
                    updateCampaignDataList.add(updateEntity);
                }
            }

            //拉取不到的广告计划，设置为已删除状态
            List<Long> queryBaiduCampaignIds = campaignResultDataList.stream().map(GetCampaignResultData::getCampaignId).collect(Collectors.toList());
            setBaiduDeleteCampaignDataStatus(queryBaiduCampaignIds, adAccountEntity);

            //同步新增推广计划
            syncCampaignData(adAccountEntity.getEa(), adAccountEntity.getId(), addCampaignDataList, source);
            //同步更新推广计划
            syncUpdateCampaignData(adAccountEntity.getEa(), adAccountEntity.getId(), updateCampaignDataList);
            log.info("refreshBaiduCampaignData add ea:{} source:{} campaign size:{} update size:{}", adAccountEntity.getEa(), source, addCampaignDataList.size(), updateCampaignDataList.size());
        }
    }


    private void setBaiduDeleteCampaignDataStatus(List<Long> baiduCampaignIds, AdAccountEntity adAccountEntity) {
        if (CollectionUtils.isEmpty(baiduCampaignIds)) {
            return;
        }
        List<String> ids = baiduCampaignDAO.queryNotQueryCampaignData(adAccountEntity.getEa(), baiduCampaignIds, adAccountEntity.getId());
        if (CollectionUtils.isNotEmpty(ids)) {
            baiduCampaignDAO.updateCampaignStatusByIds(adAccountEntity.getEa(), ids, CampaignStatusEnum.delete.getStatus());
            log.info("setBaiduDeleteCampaignDataStatus ea:{} to set delete campaignIds:{}", adAccountEntity.getEa(), ids);
        }
    }


    /**
     * 新增广告推广
     *
     * @param ea
     * @param campaignDataList
     * @param source
     */
    private void syncCampaignData(String ea, String adAccountId, List<GetCampaignResultData> campaignDataList, String source) {
        if (CollectionUtils.isEmpty(campaignDataList)) {
            return;
        }

        List<BaiduCampaignEntity> batchAddList = Lists.newArrayList();
        campaignDataList.forEach(campaignResultData -> {
            BaiduCampaignEntity campaignEntity = new BaiduCampaignEntity();
            campaignEntity.setId(UUIDUtil.getUUID());
            campaignEntity.setEa(ea);
            campaignEntity.setAdAccountId(adAccountId);
            campaignEntity.setCampaignId(campaignResultData.getCampaignId());
            campaignEntity.setCampaignName(campaignResultData.getCampaignName());
            campaignEntity.setBudget(campaignResultData.getBudget());
            campaignEntity.setCampaignType(campaignResultData.getCampaignType());
            campaignEntity.setSource(source);
            campaignEntity.setStatus(campaignResultData.getStatus());
            campaignEntity.setDevice(campaignResultData.getDevice());
            campaignEntity.setRefreshTime(new Date());
            batchAddList.add(campaignEntity);
        });
        baiduCampaignDAO.batchAddCampaign(batchAddList);
    }


    /**
     * 更新广告推广
     *
     * @param campaignDataList
     */
    private void syncUpdateCampaignData(String ea, String adAccountId, List<BaiduCampaignEntity> campaignDataList) {
        if (CollectionUtils.isEmpty(campaignDataList)) {
            return;
        }
        //1,更新本地数据
        baiduCampaignDAO.batchUpdateCampaign(ea, adAccountId, campaignDataList);
    }


    /**
     * 同步推广单元数据
     *
     * @param adAccountEntity
     */
    private void refreshAdGroup(AdAccountEntity adAccountEntity, String source) {
        int campaignTotalCount = baiduCampaignDAO.queryCampaignTotalCount(adAccountEntity.getEa(), adAccountEntity.getId(), source);
        if (campaignTotalCount == 0) {
            return;
        }
        int pageSize = AdGroupManager.MAX_CAMPAIGIN_ID;
        int totalPageCount = campaignTotalCount / pageSize;
        if (campaignTotalCount % pageSize != 0) {
            totalPageCount++;
        }

        for (int i = 0; i < totalPageCount; i++) {
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, AdGroupManager.MAX_CAMPAIGIN_ID);
            List<Long> campaignIds = baiduCampaignDAO.pageCampaignIds(adAccountEntity.getEa(), adAccountEntity.getId(), source, page);
            RequestResult<GetAdGroupResultData> adGroupResultData = adGroupManager.getAdGroupResultDataList(campaignIds, BaiduIdTypeEnum.CAMPAIGN.getType(), adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
            if (adGroupResultData != null && CollectionUtils.isNotEmpty(adGroupResultData.getData())) {
                syncAdGroupByCampaignData(adAccountEntity.getEa(), adAccountEntity.getId(), adGroupResultData.getData(), source);
            }
        }
    }

    public void syncAdGroupByCampaignData(String ea, String adAccountId, List<GetAdGroupResultData> adGroupResultData, String source) {
        if (CollectionUtils.isEmpty(adGroupResultData)) {
            return;
        }
        Map<Long, List<GetAdGroupResultData>> partitionAdgroupMap = getPartitionAdgroupMap(adGroupResultData);
        if (partitionAdgroupMap == null) {
            return;
        }

        List<GetAdGroupResultData> addAdGroupData = Lists.newArrayList();
        List<AdGroupEntity> updateAdgroupList = Lists.newArrayList();
        for (Map.Entry<Long, List<GetAdGroupResultData>> entry : partitionAdgroupMap.entrySet()) {
            Long campaignId = entry.getKey();
            List<GetAdGroupResultData> adgroupData = entry.getValue();
            List<Long> adgroupIds = adgroupData.stream().map(GetAdGroupResultData::getAdgroupId).collect(Collectors.toList());
            List<AdGroupEntity> adGroupEntityList = adGroupDAO.queryAdgroupByAdgroupIds(ea, adAccountId, campaignId, adgroupIds, source);
            if (CollectionUtils.isEmpty(adGroupEntityList)) {
                addAdGroupData.addAll(adgroupData);
            } else {
                Map<Long, AdGroupEntity> adGroupEntityMap = adGroupEntityList.stream().collect(Collectors.toMap(AdGroupEntity::getAdGroupId, Function.identity(), (v1, v2) -> v2));
                adgroupData.forEach(getAdGroupResultData -> {
                    if (adGroupEntityMap.get(getAdGroupResultData.getAdgroupId()) == null) {
                        addAdGroupData.add(getAdGroupResultData);
                    } else {
                        AdGroupEntity entity = adGroupEntityMap.get(getAdGroupResultData.getAdgroupId());
                        entity.setAdGroupName(getAdGroupResultData.getAdgroupName());
                        entity.setStatus(getAdGroupResultData.getStatus());
                        updateAdgroupList.add(entity);
                    }
                });
            }
        }

        //新增推广单元
        batchAddAdgroup(ea, adAccountId, addAdGroupData, source);
        //更新推广单元
        batchUpdateAdGroup(ea, adAccountId, updateAdgroupList);
        log.info("syncAdGroupByCampaignData ea:{} add size:{} update size:{}", ea, addAdGroupData.size(), updateAdgroupList.size());
        //新增推广单元同步到CRM
    }


    private Map<Long, List<GetAdGroupResultData>> getPartitionAdgroupMap(List<GetAdGroupResultData> adGroupResultData) {
        if (CollectionUtils.isEmpty(adGroupResultData)) {
            return null;
        }

        Map<Long, List<GetAdGroupResultData>> adgroupMap = new HashMap<>();
        adGroupResultData.forEach(adgroupData -> {
            if (adgroupMap.get(adgroupData.getCampaignId()) == null) {
                adgroupMap.put(adgroupData.getCampaignId(), new ArrayList<>());
            }
            adgroupMap.get(adgroupData.getCampaignId()).add(adgroupData);
        });

        return adgroupMap;
    }


    private void batchAddAdgroup(String ea, String adAccountId, List<GetAdGroupResultData> addAdGroupData, String source) {
        if (CollectionUtils.isEmpty(addAdGroupData)) {
            return;
        }

        PageUtil pageUtil = new PageUtil(addAdGroupData, 100);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<GetAdGroupResultData> addGroupData = pageUtil.getPagedList(i);
            List<AdGroupEntity> addAdGroupEntityList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(addGroupData)) {
                addGroupData.forEach(getAdGroupResultData -> {
                    AdGroupEntity entity = new AdGroupEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    entity.setAdAccountId(adAccountId);
                    entity.setSource(source);
                    entity.setCampaignId(getAdGroupResultData.getCampaignId());
                    entity.setAdGroupId(getAdGroupResultData.getAdgroupId());
                    entity.setAdGroupName(getAdGroupResultData.getAdgroupName());
                    entity.setStatus(getAdGroupResultData.getStatus());
                    addAdGroupEntityList.add(entity);
                });
            }

            if (CollectionUtils.isNotEmpty(addAdGroupEntityList)) {
                adGroupDAO.batchInsert(addAdGroupEntityList);
            }
        }
    }


    private void batchUpdateAdGroup(String ea, String adAccountId, List<AdGroupEntity> updateAdgroupList) {
        if (CollectionUtils.isEmpty(updateAdgroupList)) {
            return;
        }

        PageUtil pageUtil = new PageUtil(updateAdgroupList, 20);
        for (int i = 1; i < pageUtil.getPageCount(); i++) {
            List<AdGroupEntity> updateEntityList = pageUtil.getPagedList(i);
            if (CollectionUtils.isNotEmpty(updateEntityList)) {
                adGroupDAO.batchUpdate(updateEntityList, ea, adAccountId);
            }
        }
    }


    /**
     * 更新关键词信息
     *
     * @param adAccountEntity
     */
    private void refreshKeyword(AdAccountEntity adAccountEntity, String source) {
        if (adAccountEntity == null) {
            return;
        }
        int totalCount = adGroupDAO.getRefreshAdgroupTotalCount(adAccountEntity.getEa(), adAccountEntity.getId(), source);
        if (totalCount == 0) {
            return;
        }
        int pageSize = AdKeywordManager.MAX_ADGROUPD_ID;
        int totalPageCount = totalCount / pageSize;
        if (totalCount % pageSize != 0) {
            totalPageCount++;
        }
        String ea = adAccountEntity.getEa();
        for (int i = 0; i < totalPageCount; i++) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, AdKeywordManager.MAX_ADGROUPD_ID);
            List<Long> adGroupIds = adGroupDAO.pageRefreshAdgroupIds(ea, adAccountEntity.getId(), source, page);
            Optional<RequestResult<AdKeywordResultData>> adKeywordDataResultOpt = adKeywordManager.getKeywordResultDataList(adGroupIds,
                    BaiduIdTypeEnum.AD_GROUP.getType(), adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
            if (adKeywordDataResultOpt.isPresent()) {
                syncKeywordByAdGroupData(adAccountEntity.getEa(), adAccountEntity.getId(), adKeywordDataResultOpt.get().getData());
            }
        }
    }

    public List<AdKeywordEntity> syncKeywordByKeywordIds(String ea, String adAccountId, List<Long> keywordIdList) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return Lists.newArrayList();
        }
        try {
            AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
            if (adAccountEntity == null) {
                return Lists.newArrayList();
            }
            Optional<RequestResult<AdKeywordResultData>> adKeywordDataResultOpt = adKeywordManager.getKeywordResultDataList(keywordIdList,
                    BaiduIdTypeEnum.KEYWORD.getType(), adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
            adKeywordDataResultOpt.ifPresent(adKeywordResultDataRequestResult -> syncKeywordByAdGroupData(adAccountEntity.getEa(), adAccountEntity.getId(), adKeywordResultDataRequestResult.getData()));
            List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, keywordIdList);
            refreshDataManager.batchSyncKeywordToMarketingKeywordObj(ea, adAccountId, adKeywordEntityList);
            return adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, keywordIdList);
        } catch (Exception e) {
            log.error("syncKeywordByKeywordIds, ea: {} adAccountId: {} keywordIds: {}", ea, adAccountId, keywordIdList, e);
            return Lists.newArrayList();
        }
    }

    private void syncKeywordByAdGroupData(String ea, String adAccountId, List<AdKeywordResultData> keywordResultData) {
        if (CollectionUtils.isEmpty(keywordResultData)) {
            return;
        }

        List<AdKeywordResultData> addKeywordList = Lists.newArrayList();
        List<AdKeywordEntity> updateKeywordList = Lists.newArrayList();
        PageUtil pageUtil = new PageUtil(keywordResultData, 500);

        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<AdKeywordResultData> currentPageData = pageUtil.getPagedList(i);
            List<Long> keywordIds = currentPageData.stream().map(AdKeywordResultData::getKeywordId).collect(Collectors.toList());
            List<AdKeywordEntity> keywordEntityList = adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, keywordIds);
            if (CollectionUtils.isEmpty(keywordEntityList)) {
                addKeywordList.addAll(currentPageData);
            } else {
                Map<Long, AdKeywordEntity> existKeywordMap = keywordEntityList.stream().collect(Collectors.toMap(AdKeywordEntity::getKeywordId, Function.identity(), (k1, k2) -> k1));
                currentPageData.forEach(keywordData -> {
                    if (existKeywordMap.get(keywordData.getKeywordId()) == null) {
                        addKeywordList.add(keywordData);
                    } else {
                        AdKeywordEntity entity = existKeywordMap.get(keywordData.getKeywordId());
                        entity.setKeyword(keywordData.getKeyword());
                        entity.setStatus(keywordData.getStatus());
                        updateKeywordList.add(entity);
                    }
                });
            }
        }

        batchAddKeyword(ea, adAccountId, addKeywordList);
        batchUpdateKeyword(ea, adAccountId, updateKeywordList);
    }


    private void batchAddKeyword(String ea, String adAccountId, List<AdKeywordResultData> keywordList) {
        if (CollectionUtils.isEmpty(keywordList)) {
            return;
        }
        PageUtil pageUtil = new PageUtil(keywordList, 500);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<AdKeywordResultData> addData = pageUtil.getPagedList(i);
            List<AdKeywordEntity> addKeywordEntityList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(addData)) {
                addData.forEach(adKeywordResultData -> {
                    AdKeywordEntity entity = new AdKeywordEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    entity.setAdAccountId(adAccountId);
                    entity.setCampaignId(adKeywordResultData.getCampaignId());
                    entity.setAdgroupId(adKeywordResultData.getAdgroupId());
                    entity.setKeywordId(adKeywordResultData.getKeywordId());
                    entity.setKeyword(adKeywordResultData.getKeyword());
                    entity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
                    entity.setStatus(adKeywordResultData.getStatus());
                    addKeywordEntityList.add(entity);
                });
            }
            if (CollectionUtils.isNotEmpty(addKeywordEntityList)) {
                adKeywordDAO.batchInsert(
                        addKeywordEntityList.stream()
                                .filter(e -> (e.getCampaignId() != null && e.getAdgroupId() != null && e.getAdId() == null) || e.getCampaignId() == null && e.getAdgroupId() == null && e.getAdId() != null)
                                .collect(Collectors.toList())
                );
            }
        }
    }


    private void batchUpdateKeyword(String ea, String adAccountId, List<AdKeywordEntity> keywordList) {
        if (CollectionUtils.isEmpty(keywordList)) {
            return;
        }
        PageUtil pageUtil = new PageUtil(keywordList, 100);
        for (int i = 1; i < pageUtil.getPageCount(); i++) {
            List<AdKeywordEntity> currentList = pageUtil.getPagedList(i);
            if (CollectionUtils.isNotEmpty(currentList)) {
                adKeywordDAO.batchUpdate(currentList, ea, adAccountId);
            }
        }
    }


    public void syncCampaignToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, String source) {
        int campaignTotalCount = baiduCampaignDAO.queryRefereshCampaignTotalCount(ea, adAccountEntity.getId(), source);
        if (campaignTotalCount == 0) {
            return;
        }
        int pageSize = 100;
        int totalPageCount = campaignTotalCount / pageSize;
        if (campaignTotalCount % pageSize != 0) {
            totalPageCount++;
        }
        for (int i = 0; i < totalPageCount; i++) {
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, pageSize);
            List<BaiduCampaignEntity> campaignData = baiduCampaignDAO.pageCampaignEntityData(ea, adAccountEntity.getId(), source, page);
            List<AdCampaignEntity> adCampaignEntities = Lists.newArrayList();
            adCampaignEntities.addAll(campaignData);
            refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntities, source);
        }
    }

    public void syncCampaignMember(String ea, String leadId, String source, String campaignName, String keyword,
                                   boolean isDuplicateSearch, String unitId, String accountId, Long keywordId, String marketingEventId) {
        Long unitIdValue = null, accountIdValue = null;
        if(StringUtils.isNumeric(unitId)) {
            unitIdValue = Long.parseLong(unitId);
        }
        if(StringUtils.isNumeric(accountId)) {
            accountIdValue = Long.parseLong(accountId);
        }
        Optional<String> optional = syncLeadROIFieldAndGetMarketingEventId(ea, leadId, source, campaignName, keyword, isDuplicateSearch, unitIdValue, accountIdValue, keywordId, marketingEventId);
        if (!optional.isPresent()) {
            return;
        }

        marketingEventId = optional.get();
        Optional<CampaignMergeDataEntity> optionalMergeDataEntity = buildCampaignMemberObjData(ea, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
        if (!optionalMergeDataEntity.isPresent()) {
            return;
        }

        CampaignMergeDataEntity campaignMergeDataEntity = optionalMergeDataEntity.get();
        Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
        String campaignMemberId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
        if (campaignMemberId != null) {
            campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberId);
            campaignMergeDataManager.addCampaignDataOnlyUnLock(optionalMergeDataEntity.get());
        }
    }

    private Optional<String> syncLeadROIFieldAndGetMarketingEventId(String ea, String leadId, String source, String campaignName, String keyword, Boolean allowDuplicate, Long unitId, Long accountId, Long keywordId, String marketingEventId) {
        log.info("syncLeadROIField ea:{} leadId:{}, source:{}, campaignName:{}, keyword:{}", ea, leadId, source, campaignName, keyword);
        if (StringUtils.isEmpty(campaignName)) {
            return Optional.empty();
        }
        if (AdSourceEnum.getBySource(source) == null) {
            return Optional.empty();
        }
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            return Optional.empty();
        }
        AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());

        //同步市场活动
//        Optional<String> marketingEventIdOpt = utmDataManger.utmCreateMarketingEvent(ea, campaignName, source, mappingEntity, true);
        if (StringUtils.isBlank(marketingEventId)) {
            Optional<UtmMarketingEventResult> marketingEventIdOpt = utmDataManger.getOrCreateMarketingEventObj(ea, accountId, unitId, campaignName, source, mappingEntity, true);
            if (!marketingEventIdOpt.isPresent()) {
                log.info("syncLeadROIField create marketingEvent failed ea:{} leadId:{} campaignName:{} keyword:{}", ea, leadId, campaignName, keyword);
                return Optional.empty();
            }
            marketingEventId = marketingEventIdOpt.get().getMarketingEventId();
        }
        //同步关键词
        String marketingKeywordId = null;
        //同步关键词计划
        String marketingKeywordPlanId = null;
        if (StringUtils.isNotBlank(keyword)) {
            Optional<String> marketingKeywordOpt = utmDataManger.utmCreateMarketingKeyword(ea, keyword, true);
            if (marketingKeywordOpt.isPresent()) {
                marketingKeywordId = marketingKeywordOpt.get();
                boolean isRelateAdGroupDimensionality = adCommonManager.isRelateAdGroupDimensionality(ea, marketingEventId);
                Optional<String> marketingKeywordPlanOpt = utmDataManger.utmCreateMarketingKeywordPlan(ea, marketingEventId, campaignName, marketingKeywordId, keyword, true, isRelateAdGroupDimensionality, keywordId);
                if (marketingKeywordPlanOpt.isPresent()) {
                    marketingKeywordPlanId = marketingKeywordPlanOpt.get();
                }
            }
        }
        marketingPromotionSourceObjManager.updateMarketingPromotionSource(ea, leadId, marketingEventId, marketingKeywordId);

        if (StringUtils.isNotEmpty(marketingEventId) && StringUtils.isNotEmpty(marketingKeywordId) && StringUtils.isNotEmpty(marketingKeywordPlanId)) {
            //更新线索ROI
            utmDataManger.updateLeadROIData(ea, -10000, leadId, marketingKeywordId, marketingKeywordPlanId, marketingEventId, allowDuplicate, campaignName, keyword);
        } else {
            //更新线索市场活动
            ActionEditArg actionEditArg = new ActionEditArg();
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectMap.put("object_describe_id", CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectMap.put("_id", leadId);
            objectMap.put("marketing_event_id", marketingEventId);
            if (jetbrick.util.StringUtils.isNotBlank(keyword)) {
                objectMap.put("utm_term__c", keyword);
            }
            if (jetbrick.util.StringUtils.isNotBlank(campaignName)) {
                objectMap.put("utm_campaign__c", campaignName);
            }
            int ei = eieaConverter.enterpriseAccountToId(ea);
            objectMap.put("tenant_id", ei);
            actionEditArg.setObjectData(ObjectData.convert(objectMap));
            if (allowDuplicate != null) {
                ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
                optionInfo.setIsDuplicateSearch(allowDuplicate);
                actionEditArg.setOptionInfo(optionInfo);
            }
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.CRM_LEAD.getName(), true, true, actionEditArg);
            if (!editResult.isSuccess()) {
                return Optional.empty();
            }
        }
        return Optional.of(marketingEventId);
    }

    // 构建活动成员对象
    private Optional<CampaignMergeDataEntity> buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return Optional.empty();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error apiName: {} objectId: {}", campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        }
        if (objectData == null) {
            return Optional.empty();
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = campaignMergeDataManager.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.AD_SYNC.getType());

        return Optional.of(campaignMergeDataEntity);
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity
            campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error apiName: {} objectId: {}", campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId(), e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshPrototypeRoomAccountData(String ea, int totalCost) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_BAIDU.getSource());
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return;
        }
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            initCampaignAndKeyword(adAccountEntity);
            int campaignTotalCount = baiduCampaignDAO.countByAdAccountId(ea, adAccountEntity.getId());
            int pageSize = 1000;
            String lastId = null;
            int count = 0;
            while (count < campaignTotalCount) {
                List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.scanByAdAccountId(ea, adAccountEntity.getId(), lastId, pageSize);
                if (CollectionUtils.isEmpty(baiduCampaignEntityList)) {
                    break;
                }
                int size = baiduCampaignEntityList.size();
                count += size;
                lastId = baiduCampaignEntityList.get(size - 1).getId();
                createCampaignDataAndKeywordData(adAccountEntity, baiduCampaignEntityList, totalCost);
            }
        }
    }

    private void createCampaignDataAndKeywordData(AdAccountEntity adAccountEntity, List<BaiduCampaignEntity> baiduCampaignEntityList, int totalCost) {
        String ea = adAccountEntity.getEa();
        List<BaiduCampaignDataEntity> dataEntityList = Lists.newArrayList();
        Random random = new Random();
        int campaignSize = baiduCampaignEntityList.size();
        int avgCost = totalCost / campaignSize;
        for (int i = 0; i < campaignSize; i++) {
            BaiduCampaignEntity campaignEntity = baiduCampaignEntityList.get(i);
            Date now = new Date();
            Date actionDate = DateUtil.plusDay(now, -1);
            BaiduCampaignDataEntity dataEntity = new BaiduCampaignDataEntity();
            dataEntity.setId(UUIDUtil.getUUID());
            dataEntity.setEa(ea);
            dataEntity.setCampaignId(campaignEntity.getCampaignId());
            dataEntity.setActionDate(actionDate);
            int cost;
            if (i == campaignSize - 1) {
                cost = totalCost;
            } else {
                cost = random.nextInt(avgCost);
                totalCost -= cost;
            }
            cost = Math.max(0, cost);
            dataEntity.setCost((double) cost);
            // 每日展现数 = 每日消费数 * (1 ~ 10)
            long pv = (long) cost * (random.nextInt(11) + 1);
            dataEntity.setPv(pv);
            // 每日点击数 = 昨日展现数 * (0.1 ~ 10%)
            double click =  pv * (random.nextInt(10) + 0.1) * 0.01;
            dataEntity.setClick((long) click);
            dataEntity.setLeads(0);
            dataEntity.setCreateTime(now);
            dataEntity.setUpdateTime(now);
            dataEntity.setAdAccountId(campaignEntity.getAdAccountId());
            dataEntityList.add(dataEntity);
        }
        int count = baiduCampaignDataDAO.batchInsert(dataEntityList);
        buildAdvertisingDetailsObj(ea, dataEntityList);
        if (count > 0) {
            buildTermServingLinesData(adAccountEntity, baiduCampaignEntityList, dataEntityList);
        }
    }

    private void buildTermServingLinesData(AdAccountEntity adAccountEntity, List<BaiduCampaignEntity> baiduCampaignEntityList, List<BaiduCampaignDataEntity> baiduCampaignDataEntityList) {
        String ea = adAccountEntity.getEa();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        List<Long> campaignIdList = baiduCampaignEntityList.stream().map(BaiduCampaignEntity::getCampaignId).collect(Collectors.toList());
        List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.queryByCampaignIdList(ea, campaignIdList);
        Map<Long, List<AdKeywordEntity>> campaignIdToKeywordList = adKeywordEntityList.stream().collect(Collectors.groupingBy(AdKeywordEntity::getCampaignId));
        Map<Long, String> campaignIdToMarketingEventIdMap = baiduCampaignEntityList.stream().collect(Collectors.toMap(BaiduCampaignEntity::getCampaignId, BaiduCampaignEntity::getMarketingEventId, (v1, v2) -> v1));
        for (BaiduCampaignDataEntity campaignDataEntity : baiduCampaignDataEntityList) {
            List<AdKeywordEntity> campaignKeywordList = campaignIdToKeywordList.get(campaignDataEntity.getCampaignId());
            if (CollectionUtils.isEmpty(campaignKeywordList)) {
                continue;
            }
            List<String> marketingKeywordIdList = campaignKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingKeywordId())).map(AdKeywordEntity::getMarketingKeywordId).distinct().collect(Collectors.toList());
            String marketingEventId = campaignIdToMarketingEventIdMap.get(campaignDataEntity.getCampaignId());
            Page<ObjectData> pageMarketingKeywordPlanDataPage = refreshDataManager.queryCrmMarketingKeywordPlanByUserName(adAccountEntity.getEa(), adAccountEntity.getUsername(), marketingEventId, marketingKeywordIdList, marketingKeywordIdList.size());
            if (pageMarketingKeywordPlanDataPage == null || CollectionUtils.isEmpty(pageMarketingKeywordPlanDataPage.getDataList())) {
                log.warn("广告样板间,关键词投放计划为空 adAccountEntity : {}, marketingEventId: {}, keywordIdList: {}", adAccountEntity, marketingEventId,  marketingKeywordIdList);
                continue;
            }
            Random random = new Random();
            long keywordTotalPv =  campaignDataEntity.getPv();
            long keywordTotalClick = campaignDataEntity.getClick();
            double keywordTotalCost = campaignDataEntity.getCost();
            int marketingKeywordPlanSize = pageMarketingKeywordPlanDataPage.getDataList().size();
            for (int j = 0; j < marketingKeywordPlanSize; j++) {
                ObjectData objectData = pageMarketingKeywordPlanDataPage.getDataList().get(j);
                AdReportDataDTO adReportDataDTO = new AdReportDataDTO();
                adReportDataDTO.setLaunchDate(campaignDataEntity.getActionDate());
                adReportDataDTO.setKeywordServingPlanId(objectData.getId());
                adReportDataDTO.setMarketingEventId(marketingEventId);
                adReportDataDTO.setMarketingKeywordId(objectData.getString("marketing_keyword_id"));
                if (j == marketingKeywordPlanSize - 1) {
                    adReportDataDTO.setImpression(keywordTotalPv);
                    adReportDataDTO.setClick(keywordTotalClick);
                    adReportDataDTO.setCost(keywordTotalCost);
                } else {
                    int keywordClick = 0;
                    if (keywordTotalClick > 0) {
                        keywordClick = random.nextInt((int) keywordTotalClick);
                    }
                    int keywordPv = 0;
                    if (keywordTotalPv > 0) {
                        keywordPv = random.nextInt((int) keywordTotalPv);
                    }
                    int keywordCost = 0;
                    if (keywordTotalCost > 0) {
                        keywordCost = random.nextInt(Double.valueOf(keywordTotalCost).intValue());
                    }
                    keywordTotalPv -= keywordPv;
                    keywordTotalClick -= keywordClick;
                    keywordTotalCost -= keywordCost;
                    adReportDataDTO.setImpression((long) keywordPv);
                    adReportDataDTO.setClick((long) keywordClick);
                    adReportDataDTO.setCost((double) keywordCost);
                }
                refreshDataManager.createTermServingLinesObj(ei, adReportDataDTO, adAccountEntity.getUsername());
            }
        }
    }

    private void initCampaignAndKeyword(AdAccountEntity adAccountEntity) {

        List<String> existCampaignNameList = baiduCampaignDAO.getAllNameByAdAccountId(adAccountEntity.getEa(), adAccountEntity.getId());
        if (existCampaignNameList == null) {
            existCampaignNameList = Lists.newArrayList();
        }
        Set<String> existCampaignNameSet = Sets.newHashSet(existCampaignNameList);
//        String finalCampaignList = I18nUtil.getSuitedLangText(campaignList, campaignListEN);
        String finalCampaignList = null;
        String lang = marketingEventCommonSettingService.getLang(adAccountEntity.getEa());
        int langType = org.apache.commons.lang3.StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
        if(langType == 1){
            finalCampaignList = campaignList;
        }else {
            finalCampaignList = campaignListEN;
        }
        List<String> campaignNameList = JSONArray.parseArray(finalCampaignList, String.class);
        List<BaiduCampaignEntity> campaignEntityList = Lists.newArrayList();
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
//        String finalCampaignToKeyword = I18nUtil.getSuitedLangText(baiduCampaignToKeyword, baiduCampaignToKeywordEN);
        String finalCampaignToKeyword = null;
        if(langType == 1){
            finalCampaignToKeyword = baiduCampaignToKeyword;
        }else {
            finalCampaignToKeyword = baiduCampaignToKeywordEN;
        }
        JSONObject campaignNameToKeywordList = JSON.parseObject(finalCampaignToKeyword);
        List<AdKeywordEntity> adKeywordEntityList = Lists.newArrayList();
        for (String campaignName : campaignNameList) {
            if (existCampaignNameSet.contains(campaignName)) {
                continue;
            }
            BaiduCampaignEntity baiduCampaignEntity = new BaiduCampaignEntity();
            baiduCampaignEntity.setId(UUIDUtil.getUUID());
            baiduCampaignEntity.setEa(ea);
            baiduCampaignEntity.setCampaignId(Long.parseLong(redisManager.getPrimaryId()));
            baiduCampaignEntity.setCampaignName(campaignName);
            baiduCampaignEntity.setBudget(null);
            baiduCampaignEntity.setCampaignType(0);
            baiduCampaignEntity.setStatus(CampaignStatusEnum.validity.getStatus());
            baiduCampaignEntity.setDevice(0);
            Date now = new Date();
            baiduCampaignEntity.setRefreshTime(now);
            baiduCampaignEntity.setCreateTime(now);
            baiduCampaignEntity.setUpdateTime(now);
            baiduCampaignEntity.setSource(adAccountEntity.getSource());
            baiduCampaignEntity.setAdAccountId(adAccountId);
            campaignEntityList.add(baiduCampaignEntity);
            JSONArray keywordNameList = campaignNameToKeywordList.getJSONArray(campaignName);
            if (CollectionUtils.isEmpty(keywordNameList)) {
                continue;
            }
            for (Object keywordObj : keywordNameList) {
                String keyword = (String) keywordObj;
                AdKeywordEntity entity = new AdKeywordEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setAdAccountId(adAccountId);
                entity.setCampaignId(baiduCampaignEntity.getCampaignId());
                entity.setAdgroupId(Long.parseLong(redisManager.getPrimaryId()));
                entity.setKeywordId(Long.parseLong(redisManager.getPrimaryId()));
                entity.setKeyword(keyword);
                entity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
                entity.setStatus(BaiduKeywordStatusEnum.VALID.getType());
                adKeywordEntityList.add(entity);
            }
        }
        if (CollectionUtils.isEmpty(campaignEntityList)) {
            return ;
        }
        baiduCampaignDAO.batchAddCampaign(campaignEntityList);
        List<AdCampaignEntity> adCampaignEntityList = Lists.newArrayList(campaignEntityList);
        refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntityList, adAccountEntity.getSource());

        if (CollectionUtils.isNotEmpty(adKeywordEntityList)) {
            adKeywordDAO.batchInsert(adKeywordEntityList);
            refreshDataManager.syncAdKeywordToMarketingKeywordObj(ea, adAccountEntity.getId(), AdSourceEnum.SOURCE_BAIDU.getSource());
            refreshDataManager.syncKeywordServingPlanV2(ea, adAccountEntity.getId(), AdSourceEnum.SOURCE_BAIDU.getSource());
        }
    }

    // 更新市场活动的ocpc投放标识
    public void refreshMarketingEventOOCPCLaunchByEa(String ea) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEaAndSource(ea, null, AdSourceEnum.SOURCE_BAIDU.getSource(), false);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            log.info("不存在百度账号， ea: {}", ea);
            return;
        }

        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            refreshMarketingEventOOCPCLaunchByAccountId(ea, adAccountEntity);
        }
    }

    public void refreshMarketingEventOOCPCLaunchByAccountId(String ea, AdAccountEntity adAccountEntity) {
        int campaignTotalCount = baiduCampaignDAO.countByAdAccountId(ea, adAccountEntity.getId());
        if (campaignTotalCount <= 0) {
            return;
        }
        String lastId = null;
        int count = 0;
        String accessToken = adTokenManager.getBaiduAccessToken(adAccountEntity);
        if (StringUtils.isBlank(accessToken)) {
            return;
        }
        // 获取该账号下的oCPC出价策略
        List<GetOcpcTargetPackageResult> ocpcTargetPackageResultList = getOcpcTargetPackage(adAccountEntity, accessToken);
        // 包含在出价策略中的campaignId集合
        Set<Long> hasOcpcCampaignIdSet = ocpcTargetPackageResultList.stream().map(GetOcpcTargetPackageResult::getScopeList).flatMap(List::stream).map(GetOcpcTargetPackageResult.TargetPackageBindInfo::getLevelId).collect(Collectors.toSet());
        int pageSize = 1000;
        while (count < campaignTotalCount) {
            List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.scanByAdAccountId(ea, adAccountEntity.getId(), lastId, pageSize);
            if (CollectionUtils.isEmpty(baiduCampaignEntityList)) {
                break;
            }
            int size = baiduCampaignEntityList.size();
            count += size;
            lastId = baiduCampaignEntityList.get(size - 1).getId();
            Map<String, Long> marketingEventIdToCampaignIdMap = baiduCampaignEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).collect(Collectors.toMap(AdCampaignEntity::getMarketingEventId, AdCampaignEntity::getCampaignId, (v1, v2) -> v1));
            List<String> marketingEventIdList = baiduCampaignEntityList.stream().map(BaiduCampaignEntity::getMarketingEventId).filter(Objects::nonNull).collect(Collectors.toList());
            List<ObjectData> marketingEventObjectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "name", CrmV2MarketingEventFieldEnum.OCPC_LAUNCH.getFieldName()), marketingEventIdList);
            for (ObjectData marketingEventObjectData : marketingEventObjectDataList) {
                String marketingEventId = marketingEventObjectData.getId();
                String ocpcLaunch = marketingEventObjectData.getString(CrmV2MarketingEventFieldEnum.OCPC_LAUNCH.getFieldName());
                Long campaignId = marketingEventIdToCampaignIdMap.get(marketingEventId);
                boolean hasOcpc = campaignId != null && hasOcpcCampaignIdSet.contains(campaignId);
                String latestOcpcLaunch = hasOcpc ? "yes" : "no";
                if (!latestOcpcLaunch.equals(ocpcLaunch)) {
                    ObjectData forUpdateObjectData = new ObjectData();
                    forUpdateObjectData.put("_id", marketingEventId);
                    forUpdateObjectData.put(CrmV2MarketingEventFieldEnum.OCPC_LAUNCH.getFieldName(), latestOcpcLaunch);
                    com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), forUpdateObjectData,false, false);
                    log.info("更新ocpc投放， ea: {} arg: {} result: {}", ea, forUpdateObjectData, result);
                }
            }
        }
    }

    private List<GetOcpcTargetPackageResult> getOcpcTargetPackage(AdAccountEntity adAccountEntity, String accessToken) {
        GetOcpcTargetPackageRequest ocpcTargetPackageRequest = new GetOcpcTargetPackageRequest();
        ocpcTargetPackageRequest.setLevel(2);
        ocpcTargetPackageRequest.setTargetPackageTypeFields(Lists.newArrayList("targetPackageId", "targetPackageName", "ocpcBid", "ocpcBidType", "scope", "assistTransTypes", "packageStatus", "ocpcDeepCpa"));
        return baiduHttpManager.getOcpcTargetPackageList(adAccountEntity.getUsername(), accessToken, ocpcTargetPackageRequest);
    }
}
