package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName DailyPosterEntity
 * @Description
 * <AUTHOR>
 * @Date 2019/2/25 2:55 PM
 */
@Data
@Entity
public class DailyPosterEntity implements Serializable {
    private String id;
    private String content;
    private String bgApath;
    private String author;
    private String year;
    private String month;
    private String day;
    private Date createTime;
    private Date updateTime;
}
