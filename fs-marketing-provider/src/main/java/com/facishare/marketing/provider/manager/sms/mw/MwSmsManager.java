package com.facishare.marketing.provider.manager.sms.mw;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.common.enums.AssociateIdTypeEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.MarketingUserActionType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwAccountTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwTemplateActionEnum;
import com.facishare.marketing.common.util.StringReplaceUntils;
import com.facishare.marketing.provider.dao.sms.mw.MwAccountDao;
import com.facishare.marketing.provider.entity.sms.mw.MwAccountEntity;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendEntity;
import com.facishare.marketing.provider.innerArg.mw.MultiMtArg;
import com.facishare.marketing.provider.innerData.sms.UploadOrModifyTemplateArg;
import com.facishare.marketing.provider.innerResult.mw.ReportResults;
import com.facishare.marketing.provider.innerResult.mw.SendRequestResult;
import com.facishare.marketing.provider.innerResult.sms.mw.TemplateQueryResult;
import com.facishare.marketing.provider.innerResult.sms.mw.TemplateUploadOrModifyResult;
import com.facishare.marketing.provider.manager.ActionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by ranluch on 2019/2/13.
 */
@Service
@Slf4j
public class MwSmsManager {

    public static final int RETSIZE_MAX = 500; //单次拉取的状态报告最大数量

    // 请求路径
    public static String REQUEST_PATH = "/sms/v2/std/";
    //模板请求路径
    public static String TEMPLATE_REQUSET_PATH = "/sms/v3/std/tpl/";
    public static String TEMPLATE_ADDRESS = "*************:9275";
    /**
     * json解析器
     */
    private Gson gson = new Gson();
    // http请求失败
    public static int ERROR_310099 = -310099;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private MwAccountDao mwAccountDao;
    
    @Autowired
    private ActionManager actionManager;
    
    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;
    
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    
    @Autowired
    private MwSmsSendDao mwSmsSendDao;

    /**
     * @param svrtype 梦网的短信签名编码
     * @param mobile  手机号，必填
     * @param content 短信内容，必填
     * @param custid  自定义流水号，必填
     * @param exdata  额外信息，选填
     * @return SendRequestResult
     * @description 相同内容群发
     * 今后content请携带签名 2021/07/07
     */
    public SendRequestResult batchSend(String svrtype, String mobile, String content, String custid, String exdata, String userid, String pwd, String masterIpAddress) {
        log.info("MwSmsManager batchSend, svrtype={}, content={}, custid={}, exdata={}", svrtype, content, custid, exdata);

        Map<String, Object> message = Maps.newHashMap();
        boolean initResult = initCommonParas(message, userid, pwd);
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager batchSend initCommonParas failed, message={}", message);
            return null;
        }

        try {
            message.put("svrtype", svrtype);
            message.put("mobile", mobile);
            message.put("content", URLEncoder.encode(StringReplaceUntils.replaceWrongUnicode(content, ""), "GBK"));
            if (StringUtils.isNotBlank(custid)) {
                message.put("custid", custid);
            }

            if (StringUtils.isNotBlank(exdata)) {
                message.put("exdata", exdata);
            }

            StringBuilder messageBuilder = new StringBuilder();

            // 相同内容群发
            int returnInt = sendSmsRequest("batch_send", message, messageBuilder, masterIpAddress);

            // returnInt为0,代表提交成功;returnInt不为0，代表提交失败
            if (returnInt == 0) {
                // 提交成功
                String result = messageBuilder.toString();
                // 处理返回结果
                if (StringUtils.isNotBlank(result)) {
                    SendRequestResult requestResult = gson.fromJson(result, SendRequestResult.class);
                    log.info("MwSmsManager batchSend, requestResult={}", requestResult);
                    return requestResult;
                }
            } else {
                // 提交失败
                log.info("MwSmsManager batchSend failed, returnInt={}", returnInt);
                return null;
            }
        } catch (Exception e) {
            log.info("MwSmsManager batchSend failed, exception={}", e.toString());
        }
        return null;
    }

    /**
     * @param multiMtList 个性化短信对象
     * @return SendRequestResult
     * @description 个性化群发
     * 今后content请携带签名 2021/07/07
     */
    public SendRequestResult multiSend(List<MultiMtArg> multiMtList, String userid, String pwd, String masterIpAddress) {
        if (CollectionUtils.isEmpty(multiMtList)) {
            log.info("MwSmsManager multiSend, multiMtList is empty");
            return null;
        }
        log.info("MwSmsManager multiSend, multiMtList={} userid:{}  pwd:{} masterIpAddress:{}", multiMtList, userid, pwd, masterIpAddress);
        Map<String, Object> message = Maps.newHashMap();
        boolean initResult = initCommonParas(message, userid, pwd);
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager multiSend initCommonParas failed, message={}", message);
            return null;
        }

        try {
            for (MultiMtArg multiMt : multiMtList) {
                // 对短信内容进行编码 urlencode（GBK明文）
                multiMt.setContent(URLEncoder.encode(StringReplaceUntils.replaceWrongUnicode(multiMt.getContent(), ""), "GBK"));
            }
            // 设置个性化详情
            message.put("multimt", multiMtList);

            StringBuilder messageBuilder = new StringBuilder();

            // 动态内容群发
            int returnInt = sendSmsRequest("multi_send", message, messageBuilder, masterIpAddress);

            // returnInt为0,代表提交成功;returnInt不为0，代表提交失败
            if (returnInt == 0) {
                // 提交成功
                String result = messageBuilder.toString();
                // 处理返回结果
                if (StringUtils.isNotBlank(result)) {
                    SendRequestResult requestResult = gson.fromJson(result, SendRequestResult.class);
                    log.info("MwSmsManager multiSend, requestResult={}", requestResult);
                    return requestResult;
                } else {
                    log.info("MwSmsManager multiSend failed, result is null, message={}", message);
                }
            } else {
                // 提交失败
                log.info("MwSmsManager multiSend failed, returnInt={}", returnInt);
                return null;
            }
        } catch (Exception e) {
            log.info("MwSmsManager multiSend failed, exception={}", e.toString());
        }
        return null;
    }

    // 今后content请携带签名 2021/07/07
    public Optional<TemplateUploadOrModifyResult> sendUploadOrModifyTemplateReq(UploadOrModifyTemplateArg arg){
        log.info("UploadOrModifyTemplateArg:{}", arg);
        Map<String, Object> message = Maps.newHashMap();
        // MwAccountEntity accountEntity = mwAccountDao.queryAccountByType(MwAccountTypeEnum.PRODUCTION.getType());
        boolean initResult = initCommonParas(message, arg.getMwUserId(), arg.getPassword());
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager multiSend initCommonParas failed, message={}", message);
            return Optional.empty();
        }
        message.put("type", "1");
        message.put("expire", "365");
        message.put("content", arg.getContent());
        message.put("name", arg.getName());
        message.put("custid", arg.getCustid());

        String requestUrl = "http://" + TEMPLATE_ADDRESS + TEMPLATE_REQUSET_PATH;
        if (arg.getActionType() == MwTemplateActionEnum.UPLOAD_TEMPLATE.getType()) {
            requestUrl = requestUrl + "tpl_upload";
        }else {
            requestUrl = requestUrl + "tpl_modify";
        }
        log.info("requestUrl:{} message:{}", requestUrl, message);
        TemplateUploadOrModifyResult result =  httpManager.executePostHttp(message, requestUrl, new TypeToken<TemplateUploadOrModifyResult>(){});
        log.info("sendUploadOrModifyTemplateReq result:{}", result);
        return Optional.ofNullable(result);
    }

    /*
    public Optional<TemplateManageResult> sendManageTempalteReq(List<String> tplid, String optype){
        Map<String, Object> message = Maps.newHashMap();
        MwAccountEntity accountEntity = mwAccountDao.queryAccountByType(MwAccountTypeEnum.PRODUCTION.getType());
        boolean initResult = initCommonParas(message, accountEntity.getMwUser(), accountEntity.getMwPwd());
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager multiSend initCommonParas failed, message={}", message);
            return Optional.empty();
        }

        message.put("expire", "365");
        message.put("tplid", String.join(",", tplid));
        message.put("optype", optype);
        String requestUrl = "http://" + TEMPLATE_ADDRESS + TEMPLATE_REQUSET_PATH + "tpl_mgr";
        return httpManager.executePostHttp(message, requestUrl, new TypeToken<TemplateManageResult>(){});
    }
     */

    public Optional<TemplateQueryResult> sendQueryTemplateReq(List<String> tplid){
        Map<String, Object> message = Maps.newHashMap();
        MwAccountEntity accountEntity = mwAccountDao.queryAccountByTypeAndIndustry(MwAccountTypeEnum.PRODUCTION.getType(), 0);
        boolean initResult = initCommonParas(message, accountEntity.getMwUser(), accountEntity.getMwPwd());
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager multiSend initCommonParas failed, message={}", message);
            return Optional.empty();
        }

        String tplids =  String.join(",", tplid);
        message.put("tplid", tplids);
        String requestUrl = "http://" + TEMPLATE_ADDRESS + TEMPLATE_REQUSET_PATH + "tplsts_query";
        TemplateQueryResult result = httpManager.executePostHttp(message, requestUrl, new TypeToken<TemplateQueryResult>(){});
        return Optional.ofNullable(result);
    }

    /**
     * @param retsize 每次请求想要获取的状态报告的最大条数。
     * @return 0:成功;非0:返回错误代码
     * @description 状态报告
     */
    public ReportResults getRpt(int retsize, String userid, String pwd, String masterIpAddress) {
        log.info("MwSmsManager getRpt, retsize={}", retsize);
        if (retsize > RETSIZE_MAX) {
            retsize = RETSIZE_MAX;
        }
        Map<String, Object> message = Maps.newHashMap();
        boolean initResult = initCommonParas(message, userid, pwd);
        if (!initResult) { //基础参数初始化失败
            log.info("MwSmsManager getRpt initCommonParas failed, message={}", message);
            return null;
        }

        try {
            message.put("retsize", retsize); // 每次拉取的最大数量
            StringBuilder messageBuilder = new StringBuilder();
            int returnInt = sendSmsRequest("get_rpt", message, messageBuilder, masterIpAddress);

            // returnInt为0,代表提交成功;returnInt不为0，代表提交失败
            if (returnInt == 0) {
                // 提交成功
                String result = messageBuilder.toString();
                // 处理返回结果
                if (StringUtils.isNotBlank(result)) {
                    // 解析JSON
                    ReportResults reportResults = gson.fromJson(result, ReportResults.class);
                    log.info("MwSmsManager multiSend, reportResults={}", reportResults);
                    return reportResults;
                } else {
                    // 获取状态报告失败
                    log.info("MwSmsManager getRpt failed, result is null, message={}", message);
                    return null;
                }
            } else {
                // 提交失败，返回错误码
                log.info("MwSmsManager getRpt failed, message={}", message);
                return null;
            }
        } catch (Exception e) {
            // 获取状态报告失败
            log.info("MwSmsManager getRpt failed, exception={}", e.toString());
            return null;
        }
    }

    /**
     * @param methodName     方法名
     * @param message        短信请求实体类
     * @param messageBuilder 请求网关的返回值
     * @return 0:提交网关成功; 非0：提交网关失败,值为错误码
     * @description 短连接发送的方法
     */
    private int sendSmsRequest(String methodName, Map<String, Object> message, StringBuilder messageBuilder, String availableAddress) {
        int failCode = ERROR_310099;
        try {
            //通过账号密码获取可用的IP
            //String availableAddress = new CheckAddress().getAddressByUserID(userid, pwd, (String) message.get("timestamp"));
            //String availableAddress = masterIpAddress;
            //未获取到IP
            if (StringUtils.isBlank(availableAddress)) {
                return failCode;
            }

            String requestUrl = "http://" + availableAddress + REQUEST_PATH + methodName;
            String result =  httpManager.executePostHttpReturnString(message, requestUrl);
          //  String result = HttpUtil.post(requestUrl, null, message, "application/json", "application/json");
            // Message为-310099,则请求失败，否则请求成功。短连接请求失败后，不重新请求。
            log.info("sendSmsRequest resultObj:{}", result);
            if (String.valueOf(ERROR_310099).equals(result)) {
                // 返回错误码 请求失败
                return failCode;
            } else {
                // 请求成功
                messageBuilder.append(result);
                // 请求成功，返回0
                return 0;
            }
        } catch (Exception e) {
            log.info("MwSmsManager sendSmsRequest failed, exception={}", e.toString());
            return failCode;
        }

    }

    private boolean initCommonParas(Map<String, Object> message, String userid, String pwd) {
        if (message == null) {
            message = Maps.newHashMap();
        }
        try {
            message.put("userid", userid);
            // 设置时间戳
            SimpleDateFormat sdf = new SimpleDateFormat("MMddHHmmss");
            String timestamp = sdf.format(Calendar.getInstance().getTime());
            message.put("timestamp", timestamp);
            String encryptPwd = encryptPwd(userid, pwd, timestamp);
            if (StringUtils.isBlank(encryptPwd)) {
                log.info("MwSmsManager encryptPwd failed, encryptPwd is null");
                return false;
            }
            message.put("pwd", encryptPwd);
        } catch (Exception e) {
            log.info("MwSmsManager encryptPwd failed, exception={}", e.toString());
            return false;
        }

        return true;
    }

    /**
     * @param userid    用户账号
     * @param pwd       用户原始密码
     * @param timestamp 时间戳
     * @return 加密后的密码
     * @description 对密码进行加密
     */
    public String encryptPwd(String userid, String pwd, String timestamp) {
        // 加密后的字符串
        String encryptPwd = null;
        try {
            String passwordStr = userid.toUpperCase() + "00000000" + pwd + timestamp;
            // 对密码进行加密
            encryptPwd = getMD5Str(passwordStr);
        } catch (Exception e) {
            log.info("MwSmsManager encryptPwd failed, exception={}", e.toString());
        }
        // 返回加密字符串
        return encryptPwd;
    }

    /**
     * @param str 需要加密的字符串
     * @return 返回加密后的字符串
     * @description MD5加密方法
     */
    private static String getMD5Str(String str) {
        MessageDigest messageDigest = null;
        // 加密前的准备
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(str.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException e) {
            log.info("MwSmsManager getMD5Str failed, NoSuchAlgorithmException={}", e.toString());
            //初始化加密类失败，返回null。
            return null;
        } catch (UnsupportedEncodingException e) {
            log.info("MwSmsManager getMD5Str failed, UnsupportedEncodingException={}", e.toString());
            //初始化加密类失败，返回null。
            return null;
        }

        byte[] byteArray = messageDigest.digest();

        // 加密后的字符串
        StringBuffer md5StrBuff = new StringBuffer();

        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
            }
        }

        return md5StrBuff.toString();
    }
    
    public void generateSmsReceivedMQ(String smsSendId, String phone){
        try {
            if (Strings.isNullOrEmpty(smsSendId)){
                return;
            }
            MwSmsSendEntity mwSmsSend = mwSmsSendDao.getSMSSendById(smsSendId);
            if (mwSmsSend == null){
                return;
            }
            String ea = mwSmsSend.getEa();
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType(), smsSendId);
            if (externalConfigEntity == null){
                return;
            }
            Optional<String> optionalBrowserUserId = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, phone);
            if (optionalBrowserUserId.isPresent()){
                RecordActionArg recordActionArg = new RecordActionArg();
                recordActionArg.setActionType(MarketingUserActionType.SMS_RECEIVED.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.SMS.getType());
                recordActionArg.setObjectId(smsSendId);
                recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                recordActionArg.setFingerPrint(optionalBrowserUserId.get());
                recordActionArg.setEa(ea);
                recordActionArg.setMarketingActivityId(externalConfigEntity.getMarketingActivityId());
                recordActionArg.setMarketingEventId(externalConfigEntity.getMarketingEventId());
                actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
            }
        }catch (Exception e){
            log.warn("Exception", e);
        }
        
    }
}
