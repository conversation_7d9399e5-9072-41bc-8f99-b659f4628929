package com.facishare.marketing.provider.entity.marketingplugin;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/9/8 14:29
 */
@Data
@Entity
public class CouponTemplateEntity implements Serializable {

    private String id;  //主键id

    private String ea;  //企业ea

    private String stockName; //优惠券名称

    private String comment; //备注

    private String stockType; //优惠券类型

    private String goodsName; //适用商品名称

    private Integer transactionMinimum; //消费门槛

    private Integer discountAmount; // 优惠金额

    private Integer discountPercent;//折扣比例

    private Integer exchangePrice;//单品换购价

    private String description; //使用说明

    private Integer hideLink; //是否隐藏链接 0:隐藏 1:不隐藏

    private Integer expiredTip; //是否开启过期提醒  0:开启 1: 不开启

    private Integer expiredDays; // 过期前多少天提醒

    private String merchantName; //商户名称

    private String merchantLogoUrl; //商户logo

    private String backgroundColor;//背景颜色

    private String couponImageUrl; //券详情图片

    private String couponCodeMode;//券code 模式

    private Integer status; // 模板状态  0:正常 1:删除

    private Date createTime; //创建时间

    private Date updateTime; //更新时间

    private String operator; //操作员
}
