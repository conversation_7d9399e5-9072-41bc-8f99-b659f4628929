package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/12/25
 **/
public interface LiveUserStatusDAO {

    @Select(" SELECT * FROM live_user_status WHERE  live_id = #{liveId}")
    List<LiveUserStatusEntity> queryLiveUserStatusByLiveId(@Param("liveId") Integer liveId);

    @Select("SELECT COUNT(*) FROM live_user_status WHERE  live_id = #{liveId} AND interactive_count > 0")
    int queryLiveChatUserCountByLiveId(@Param("liveId")Integer liveId);

    @Select(" SELECT * FROM live_user_status WHERE  live_id = #{liveId} AND phone = #{phone}")
    LiveUserStatusEntity getLiveUserStatusByLiveIdAndPhone(@Param("liveId") Integer liveId, @Param("phone") String phone);

    @Select("<script>"
        + " SELECT * FROM live_user_status WHERE live_id = #{liveId} AND phone IN "
        + "<foreach collection = 'phones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<LiveUserStatusEntity> queryLiveUserStatusByLiveIdAndPhone(@Param("liveId") Integer liveId, @Param("phones") List<String> phones);

    @Select("<script>"
            + " SELECT * FROM live_user_status WHERE xiaoetong_live_id = #{liveId} AND phone IN "
            + "<foreach collection = 'phones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<LiveUserStatusEntity> queryXiaoetongLiveUserStatusByLiveIdAndPhone(@Param("liveId") String liveId, @Param("phones") List<String> phones);

    @Select(" SELECT * FROM live_user_status WHERE  xiaoetong_live_id = #{xiaoetongLiveId}")
    List<LiveUserStatusEntity> queryXiaoetongLiveUserStatusByLiveId(@Param("xiaoetongLiveId") String xiaoetongLiveId);

    @Select("<script>"
            + " SELECT * FROM live_user_status WHERE live_id = #{liveId} AND  view_status = 1 AND phone IN "
            + "<foreach collection = 'phones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<LiveUserStatusEntity> getViewHistoryPhones(@Param("liveId") Integer liveId, @Param("phones") List<String> phones);

    @Select("<script>"
            + " SELECT * FROM live_user_status WHERE live_id = #{liveId} AND replay_status = 1 AND phone IN "
            + "<foreach collection = 'phones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<LiveUserStatusEntity> getRecordHistoryPhones(@Param("liveId") Integer liveId, @Param("phones") List<String> phones);

    @Select("<script>"
        + " SELECT * FROM live_user_status WHERE live_id = #{liveId} "
        + "<choose>\n"
        + " <when test=\"type != null and type == 0\">\n"
        + "  AND view_status = 1\n"
        + "  </when>"
        + " <when test=\"type != null and type == 1\">\n"
        + "  AND replay_status = 1\n"
        + "  </when>"
        + "<otherwise>\n"
        + "</otherwise>\n"
        + "</choose> "
        + "</script>")
    List<LiveUserStatusEntity> queryLiveUserPerformedStatusByType(@Param("liveId") Integer liveId, @Param("type") Integer type);

    @Update("<script>"
        + " UPDATE live_user_status SET "
        + "<choose>\n"
        + " <when test=\"type != null and type == 0\">\n"
        + "  view_status = 1,\n"
        + "  </when>"
        + " <when test=\"type != null and type == 1\">\n"
        + "  replay_status = 1,\n"
        + "  </when>"
        + "<otherwise>\n"
        + "</otherwise>\n"
        + "</choose> "
        + " update_time = now() "
        + " WHERE live_id = #{liveId} AND phone IN "
        + "<foreach collection = 'phones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    void updateLiveUserStatusByLiveIdAndPhone(@Param("liveId") Integer liveId, @Param("phones") List<String> phones, @Param("type") Integer type);

    @Update("<script>"
        + " UPDATE live_user_status SET "
        + "  <if test=\"interactiveCount != null\">\n"
        + "  interactive_count = interactive_count + #{interactiveCount},\n"
        + "  </if>\n"
        + "  update_time = now() WHERE live_id = #{liveId} AND phone = #{phone}\n"
        + "</script>")
    void updateLiveUserViewOrInteractiveData(@Param("liveId") Integer liveId, @Param("phone") String phone, @Param("interactiveCount") Integer interactiveCount);

    @Update("<script>"
        + " UPDATE live_user_status SET "
        + "<choose>\n"
        + " <when test=\"type != null and type == 0\">\n"
        + "  view_time = #{time},\n"
        + "  </when>"
        + " <when test=\"type != null and type == 1\">\n"
        + "  replay_time = #{time},\n"
        + "  </when>"
        + "<otherwise>\n"
        + "</otherwise>\n"
        + "</choose> "
        + " update_time = now() "
        + " WHERE live_id = #{liveId} AND phone = #{phone} "
        + "</script>")
    void updateLiveUserViewOrReplayTime(@Param("liveId") Integer liveId, @Param("phone") String phone, @Param("time") Integer time, @Param("type") Integer type);

    @Update("<script>"
        + " UPDATE live_user_status SET "
        + "<choose>\n"
        + " <when test=\"type != null and type == 0\">\n"
        + "  last_view_time = #{time},\n"
        + "  </when>"
        + " <when test=\"type != null and type == 1\">\n"
        + "  last_replay_time = #{time},\n"
        + "  </when>"
        + "<otherwise>\n"
        + "</otherwise>\n"
        + "</choose> "
        + " update_time = now() "
        + " WHERE live_id = #{liveId} AND phone = #{phone} "
        + "</script>")
    void updateLiveUserViewOrReplayLastTime(@Param("liveId") Integer liveId, @Param("phone") String phone, @Param("time") Date time, @Param("type") Integer type );

    @Insert("INSERT INTO live_user_status (\n"
        + "        \"id\",\n"
        + "        \"live_id\",\n"
        + "        \"phone\",\n"
        + "        \"view_status\",\n"
        + "        \"replay_status\",\n"
        + "        \"view_time\",\n"
        + "        \"replay_time\",\n"
        + "        \"interactive_count\",\n"
        + "        \"type\",\n"
        + "        \"xiaoetong_live_id\",\n"
        + "        \"create_time\",\n"
        + "        \"update_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.liveId},\n"
        + "        #{obj.phone},\n"
        + "        #{obj.viewStatus},\n"
        + "        #{obj.replayStatus},\n"
        + "        #{obj.viewTime},\n"
        + "        #{obj.replayTime},\n"
        + "        #{obj.interactiveCount},\n"
        + "        #{obj.type},\n"
        + "        #{obj.xiaoetongLiveId},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    void insertLiveUserStatus(@Param("obj") LiveUserStatusEntity liveUserStatusEntity);


    @Insert("<script> "
            + "  INSERT INTO live_user_status(id, ea, live_id, phone, view_status, replay_status, view_time, replay_time, interactive_count, type, xiaoetong_live_id, outer_user_id, create_time, update_time) VALUES\n"
            + "  <foreach collection='liveUserStatusEntityList' item='item' separator=','>"
            + "    (#{item.id}, #{item.ea} ,#{item.liveId}, #{item.phone}, #{item.viewStatus}, #{item.replayStatus}, #{item.viewTime}, #{item.replayTime}, #{item.interactiveCount}, #{item.type}, #{item.xiaoetongLiveId}, #{item.outerUserId}, NOW(), NOW())"
            + "  </foreach>"
            + "  ON CONFLICT DO NOTHING;"
            + "</script>")
    int batchInsert(@Param("liveUserStatusEntityList") Collection<LiveUserStatusEntity> liveUserStatusEntityList);

    @Update("<script>UPDATE live_user_status as c SET view_time=tmp.viewTime, update_time=now() FROM (values"
            + "<foreach separator=',' collection='liveUserStatusEntityList' item='item'>"
            +   "(#{item.id}, #{item.viewTime}, #{item.phone})"
            + "</foreach>"
            + ") as tmp(id, viewTime, phone) WHERE c.id=tmp.id AND c.phone=tmp.phone"
            + "</script>")
    int batchUpdate(@Param("liveUserStatusEntityList")Collection<LiveUserStatusEntity> liveUserStatusEntityList);



    @Update("<script>UPDATE live_user_status as c SET view_time=tmp.viewTime, replay_time=tmp.replayTime,view_status=tmp.viewStatus,replay_status=tmp.replayStatus,interactive_count = tmp.interactiveCount ,   update_time=now() FROM (values"
            + "<foreach separator=',' collection='liveUserStatusEntityList' item='item'>"
            +   "(#{item.id},#{item.viewStatus}, #{item.viewTime},#{item.replayStatus},#{item.replayTime},#{item.interactiveCount}, #{item.phone})"
            + "</foreach>"
            + ") as tmp (id,viewStatus, viewTime, replayStatus,replayTime,interactiveCount, phone) WHERE c.id=tmp.id AND c.phone=tmp.phone"
            + "</script>")
    int batchUpdatePolyvData(@Param("liveUserStatusEntityList")Collection<LiveUserStatusEntity> liveUserStatusEntityList);

    @Select("<script>" +
            "SELECT distinct C.*\n" +
            "FROM live_user_status AS C\n" +
            "LEFT JOIN marketing_live AS B on (B.xiaoetong_live_id = C.xiaoetong_live_id or B.live_id = C.live_id) and C.phone = #{phone} \n" +
            "LEFT JOIN user_marketing_action_statistic as A on A.properties ->> 'marketingEventId'= B.marketing_event_id\n" +
            "WHERE A.ea = #{ea} and A.user_marketing_id  = #{userMarketingId} and A.properties ->> 'objectId' = #{objectId} and A.properties ->> 'actionType' = #{actionType}" +
            "</script>")
    LiveUserStatusEntity getLiveUserStatusEntityByObjectInfo(@Param("ea") String ea, @Param("userMarketingId") String userMarketingId, @Param("objectId") String objectId, @Param("actionType") String actionType, @Param("phone") String phone);

    @Select("select * from live_user_status where ea is null limit 1000")
    List<LiveUserStatusEntity> queryByNullEa();

    @Update("<script>UPDATE live_user_status as c SET ea=tmp.ea, update_time=now() FROM (values"
            + "<foreach separator=',' collection='liveUserStatusEntityList' item='item'>"
            +   "(#{item.id}, #{item.ea})"
            + "</foreach>"
            + ") as tmp(id, ea) WHERE c.id=tmp.id"
            + "</script>")
    int batchUpdateEa(@Param("liveUserStatusEntityList")Collection<LiveUserStatusEntity> liveUserStatusEntityList);
}
