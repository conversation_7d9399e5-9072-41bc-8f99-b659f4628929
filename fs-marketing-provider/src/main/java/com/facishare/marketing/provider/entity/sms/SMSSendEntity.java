package com.facishare.marketing.provider.entity.sms;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/25 14:39
 * @描述
 */
@Data
@Entity
public class SMSSendEntity implements Serializable {
    private String id;
    // 公司账号
    private String ea;
        // 发送短信用户UserID
    private Integer userId;
    // 发送短信人的姓名
    private String creator;
    // 短信签名id
    private String sinatureId;
    // 模板id
    private String templateId;
    // 发送状态
    private Integer status;
    //实际发送人数
    private Integer actualSenderCount;
    //需要发送人数
    private Integer toSenderCount;

    //消费短信数量
    private Integer consumerCount;
    /**
     * @Link
     */
    private Integer type;
    //定时发送时间
    private Date scheduleTime;
    private Date createTime;
    private Date updateTime;

    private String hostName;

    // 返回的错误码，用来展示对应的错误提示
    private String resultCode;
}
