package com.facishare.marketing.provider.entity.sms;

import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

/**
 * Created by zhengh on 2018/12/20.
 */
@Data
@Entity
public class SignatureEntity {
    private String id;
    private String ea;
    private Integer userId;
    private String creator;
    private String appId;
    private Integer applyId;
    private String content;
    private String certificate;
    private String remark;
    private Integer type;
    private Integer status;
    private String reply;
    private Date createTime;
    private Date updateTime;
    private Integer modifyCnt;
    private Date modifyTime; // 只当modifyCnt次数更新的时候才更新，用于控制每个月签名的修改次数
}
