/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.ListBriefMarketingEventsArg;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.hexagon.HexagonStatisticArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPersonalNoticeSendArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.data.material.ObjectTypeAndIdData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.conference.GetConferenceStatisticDataResult;
import com.facishare.marketing.api.result.live.LiveBriefStatisticsResult;
import com.facishare.marketing.api.result.marketingEvent.MarketingEventSimpleResult;
import com.facishare.marketing.api.result.marketingEvent.MultiVenueMarketingEventResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventCommonSettingResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.exception.MarketingWithoutExceptionInfoException;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.ListUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.dto.MarketingActivityLeadSourceCountDTO;
import com.facishare.marketing.provider.dto.MarketingEventActivityDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.innerArg.crm.AggregateParameterArg;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.cusomerDev.sbt.SbtFormDataObject;
import com.facishare.marketing.provider.manager.feed.MaterailDataManagerFactory;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.CrmApiResult;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.rest.DaiLiTongService;
import com.facishare.marketing.provider.remote.rest.arg.AddObjectOptionsArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataActionServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.statistic.outapi.result.CommonStatisticResult;
import com.facishare.marketing.statistic.outapi.service.ContentMarketingEventStatisticService;
import com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService;
import com.facishare.marketing.statistic.outapi.service.MarketingEventStatisticService;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.AggregateQueryResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 * MarketingEventManager
 */
@Service
@Slf4j
public class MarketingEventManager {
    @Autowired
    private ContentMarketingEventMarketingUserGroupRelationDAO contentMarketingEventMarketingUserGroupRelationDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private MaterailDataManagerFactory materailDataManagerFactory;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MarketingActivityStatisticService marketingActivityStatisticService;
    @Autowired
    private ContentMarketingEventStatisticService contentMarketingEventStatisticService;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private MarketingEventStatisticService marketingEventStatisticService;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Autowired
    private MetadataActionServiceManager metadataActionServiceManager;

    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private DaiLiTongService daiLiTongService;


    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    public List<MarketingEventsBriefResult> listMarketingEvents(Integer ei, Integer fsUserId, ListBriefMarketingEventsArg arg) {
        arg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, true),
            new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, true)));
        return this.listMarketingEvents(ei, fsUserId, arg, null).getData();
    }

    public List<MarketingEventsBriefResult> listMarketingEventsV2(Integer ei, Integer fsUserId, ListBriefMarketingEventsArg arg) {
        return this.listMarketingEvents(ei, fsUserId, arg, null).getData();
    }

    public int getMarketingEventsTotalCount(Integer ei, Integer fsUserId, ListBriefMarketingEventsArg arg){
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = this.buildSearchQuery(eieaConverter.enterpriseIdToAccount(ei), arg);
        params.setSearchTemplateId(arg.getSearchTemplateId());
        params.setSearchTemplateType(arg.getSearchTemplateType());
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, params);
        return total == null ? 0 : total.intValue();
    }

    public PageResult<MarketingEventsBriefResult> listMarketingEventsByPager(Integer ei, Integer fsUserId, ListBriefMarketingEventsArg arg, PageArg pageArg){
        List<MarketingEventsBriefResult> marketingEventsBriefResults = Lists.newArrayList();
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = this.buildSearchQuery(eieaConverter.enterpriseIdToAccount(ei),arg);
        searchQuery.setLimit(pageArg.getLimit());
        searchQuery.setOffset(pageArg.getOffset());
        params.setSearchTemplateId(arg.getSearchTemplateId());
        params.setSearchTemplateType(arg.getSearchTemplateType());
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        Result<Page<ObjectData>> result = metadataControllerServiceManager.listResults(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, params);
        if (!result.isSuccess() && result.getCode() == CrmErrorCode.NO_PERMISSION) {
            throw new MarketingWithoutExceptionInfoException(result.getCode(), result.getMessage());
        } else if (!result.isSuccess() && result.getCode() != CrmErrorCode.NO_PERMISSION) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        Page<ObjectData> dataPage = result.getData();
        List<com.fxiaoke.crmrestapi.common.data.MarketingEventData> marketingEventDataList = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(dataPage.getDataList());
        List<MarketingEventsBriefResult> marketingEventsBriefResultList = convert2MarketingEventsBriefResults(marketingEventDataList);
        fillInOtherData(marketingEventsBriefResultList, result.getData().getDataList());
        marketingEventsBriefResults.addAll(marketingEventsBriefResultList);

        return new PageResult<>(result.getData().getTotal(), marketingEventsBriefResults);
    }

    public PageResult<MarketingEventsBriefResult> listMarketingEvents(Integer ei, Integer fsUserId, ListBriefMarketingEventsArg arg, PageArg pageArg) {
        List<MarketingEventsBriefResult> marketingEventsBriefResults = Lists.newArrayList();
        Integer perSize = 100;
        Integer offset;
        Integer endOffset;
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = this.buildSearchQuery(eieaConverter.enterpriseIdToAccount(ei),arg);
        params.setSearchTemplateId(arg.getSearchTemplateId());
        params.setSearchTemplateType(arg.getSearchTemplateType());
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        List<String> fieldProjection = Lists.newArrayList("_id", "begin_time","end_time", "biz_status", "event_type", "location", "name", "actual_cost", "created_by", "create_time", "owner", "tenant_id", "life_status","cover", "event_form");
        params.setFieldProjection(fieldProjection);
        if (pageArg == null) {
            endOffset = 100;
            offset = 0;
        } else {
            endOffset = pageArg.getOffset() + pageArg.getPageSize();
            offset = pageArg.getOffset();
        }
        Integer totalCount = null;
        for (; offset < endOffset; offset += perSize) {
            searchQuery.setLimit(endOffset - offset <= perSize ? endOffset - offset : perSize);
            searchQuery.setOffset(offset);
            params.setSearchQuery(searchQuery);
            Result<Page<ObjectData>> result = metadataControllerServiceManager.listResults(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, params);
            if (!result.isSuccess() && result.getCode() == CrmErrorCode.NO_PERMISSION) {
                throw new MarketingWithoutExceptionInfoException(result.getCode(), result.getMessage());
            } else if (!result.isSuccess() && result.getCode() != CrmErrorCode.NO_PERMISSION) {
                throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
            }
            Page<ObjectData> dataPage = result.getData();
            if (totalCount == null) {
                totalCount = dataPage.getTotal();
                if (pageArg == null) {
                    if (arg.getQueryLimitCount() != null) {
                        if (arg.getQueryLimitCount() <= totalCount){
                            totalCount = arg.getQueryLimitCount();
                        }
                    }
                    endOffset = totalCount;
                }
            }
            List<com.fxiaoke.crmrestapi.common.data.MarketingEventData> marketingEventDataList = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(dataPage.getDataList());
            List<MarketingEventsBriefResult> marketingEventsBriefResultList = convert2MarketingEventsBriefResults(marketingEventDataList);
            fillInOtherData(marketingEventsBriefResultList, result.getData().getDataList());
            marketingEventsBriefResults.addAll(marketingEventsBriefResultList);
        }
        return new PageResult<>(totalCount, marketingEventsBriefResults);
    }

    public void fillInOtherData(List<MarketingEventsBriefResult> marketingEventsBriefResultList, List<ObjectData> objectDataList){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(marketingEventsBriefResultList) || org.apache.commons.collections4.CollectionUtils.isEmpty(objectDataList)){
            return;
        }

        Map<String, ObjectData> ObjectIdEntityMap = new HashMap<>();
        for (ObjectData objectData : objectDataList){
            ObjectIdEntityMap.put(objectData.getId(), objectData);
        }
        Long tenantId = objectDataList.get(0).getTenantId();
        String ea = eieaConverter.enterpriseIdToAccount(tenantId.intValue());
        for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResultList){
            ObjectData objectData = ObjectIdEntityMap.get(marketingEventsBriefResult.getId());
            if (objectData == null){
                continue;
            }
            marketingEventsBriefResult.setCreateBy(objectData.getCreateBy());
            marketingEventsBriefResult.setLocation(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()));
            marketingEventsBriefResult.setCreateTime(objectData.getCreateTime());
            if (ea.equals(SbtFormDataObject.EA)){
                marketingEventsBriefResult.setLocation(objectData.getString(SbtFormDataObject.MARKETING_EVEN_LOCATION));
            }
        }
    }

    private SearchQuery buildSearchQuery(String ea, ListBriefMarketingEventsArg arg ) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setSearchSource("es");
        //处理市场活动选择器问题
        boolean hasLifeStatus = false;
        List<String> lifeStatusValue = null;
        boolean isMobileDisplay = false;
        List<String> isMobileDisplayValue = null;
        if (arg.getFilterData() != null && arg.getFilterData().getQuery() != null) {
            //对象选择器进行处理
            FilterData filterData = arg.getFilterData();
            Iterator<Filter> iterator = filterData.getQuery().getFilters().iterator();
            Filter tempFilter;
            while(iterator.hasNext()){
                tempFilter = iterator.next();
                if("tag".equals(tempFilter.getFieldName())){
                    tempFilter.setValueType(11);
                    tempFilter.setFieldName("tag");
                    tempFilter.setOperator("IN".equals(tempFilter.getOperator()) ? "LIKE" : tempFilter.getOperator());
                    tempFilter.setFieldValues(tempFilter.getFieldValues());
                    tempFilter.setValueType(11);
                }
                if("life_status".equals(tempFilter.getFieldName())){
                    hasLifeStatus = true;
                    lifeStatusValue = tempFilter.getFieldValues();
                    iterator.remove();
                }
                if("is_mobile_display".equals(tempFilter.getFieldName())){
                    isMobileDisplay = true;
                    isMobileDisplayValue = tempFilter.getFieldValues();
                    iterator.remove();
                }
            }
           BeanUtil.copyPropertiesIgnoreNull(arg.getFilterData().getQuery(),searchQuery);
        }
        if (!isMobileDisplay && arg.getIsMobileDisplay() != null) {
            isMobileDisplay = true;
            isMobileDisplayValue = BooleanUtils.isTrue(arg.getIsMobileDisplay()) ? Lists.newArrayList("0") : Lists.newArrayList("1");
        }
        if (arg.getBegin() != null && arg.getEnd() != null) {
            searchQuery.addFilter(MarketingEventFieldContants.END_TIME, Lists.newArrayList(String.valueOf(arg.getBegin())), FilterOperatorEnum.GTE);
            searchQuery.addFilter(MarketingEventFieldContants.BEGIN_TIME, Lists.newArrayList(String.valueOf(arg.getEnd())), FilterOperatorEnum.LTE);
        }
        if (CollectionUtils.isNotEmpty(arg.getEventTypeList())) {
            searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, arg.getEventTypeList(), FilterOperatorEnum.IN);
            if(arg.getFilterSonData()!=null && arg.getFilterSonData()){
                searchQuery.addFilter("parent_id", Lists.newArrayList(), FilterOperatorEnum.IS);
            }
        } else if (StringUtils.isNotEmpty(arg.getEventType())) {
            searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, Lists.newArrayList(arg.getEventType()), FilterOperatorEnum.EQ);
        }else if (CollectionUtils.isNotEmpty(arg.getNlikeEventTypeList())){
            searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, arg.getNlikeEventTypeList(), "NHASANYOF");
        }

        if (CollectionUtils.isNotEmpty(arg.getNlikeEventFormList())){
            searchQuery.addFilter(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), arg.getNlikeEventFormList(), "NHASANYOF");
        }

        if (StringUtils.isNotEmpty(arg.getName())) {
            searchQuery.addFilter(MarketingEventFieldContants.NAME, Lists.newArrayList(arg.getName()), FilterOperatorEnum.LIKE);
        }
        if (StringUtils.isNotEmpty(arg.getBizStatus())) {
            String now = String.valueOf(new Date().getTime());
            switch (arg.getBizStatus()) {
                case "scheduled":
                    searchQuery.addFilter(MarketingEventFieldContants.BEGIN_TIME, Lists.newArrayList(now), FilterOperatorEnum.GT);
                    break;
                case "ongoing":
                    searchQuery.addFilter(MarketingEventFieldContants.BEGIN_TIME, Lists.newArrayList(now), FilterOperatorEnum.LT);
                    searchQuery.addFilter(MarketingEventFieldContants.END_TIME, Lists.newArrayList(now), FilterOperatorEnum.GT);
                    break;
                case "complete":
                    searchQuery.addFilter(MarketingEventFieldContants.END_TIME, Lists.newArrayList(now), FilterOperatorEnum.LT);
                    break;
                case "finish":
                    searchQuery.addFilter(MarketingEventFieldContants.BIZ_STATUS, Lists.newArrayList(arg.getBizStatus()), FilterOperatorEnum.EQ);
                    break;
            }
        }

        if (StringUtils.isNotEmpty(arg.getParentMarketingEventId())) {
            searchQuery.addFilter("parent_id", Collections.singletonList(arg.getParentMarketingEventId()), FilterOperatorEnum.EQ);
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(arg.getMarketingEventIds())){
            searchQuery.addFilter("_id", arg.getMarketingEventIds(), FilterOperatorEnum.IN);
        }
        // 开通市场活动审批后仅展示生命状态为正常的市场活动
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
        if (hasLifeStatus && marketingEventCommonSettingEntity != null) {
            if (marketingEventCommonSettingEntity.getMarketingEventAudit()) {
                searchQuery.addFilter("life_status", lifeStatusValue, FilterOperatorEnum.EQ);
            }
        }
        if (isMobileDisplay) {
            List<Wheres> wheres = Lists.newArrayList();
            Wheres where = new Wheres();
            where.addFilter("is_mobile_display", isMobileDisplayValue, FilterOperatorEnum.EQ);
            wheres.add(where);
            Wheres where1 = new Wheres();
            where1.addFilter("is_mobile_display", Lists.newArrayList(), FilterOperatorEnum.IS);
            wheres.add(where1);
            searchQuery.setWheres(wheres);
        }
        arg.getOrderByList().forEach(val -> {
            searchQuery.addOrderBy(val.getField(), val.getAscending());
        });
        if (CollectionUtils.isNotEmpty(arg.getEventFormList())) {
            searchQuery.addFilter(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), arg.getEventFormList(), FilterOperatorEnum.IN);
        }
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        return searchQuery;
    }

    public MarketingEventsResult getMarketingEventsDetail(String ea, Integer fsUserId, String marketingEventId) {
        MarketingEventsResult result = new MarketingEventsResult();
        MarketingEventData marketingEventData = this.getMarketingEventData(ea, fsUserId, marketingEventId);
        result.setMarketingEvent(marketingEventData);
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("marketing_event_id", OperatorConstants.EQ, Lists.newArrayList(marketingEventId));
        List<String> selectFieldList = Lists.newArrayList("_id");
        List<ObjectData> objectDataList =  crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.VM_WORKSPACE_OBJ.getName(), selectFieldList, query);
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            marketingEventData.setVMWorkspaceObjId(objectDataList.get(0).getId());
        }
        List<MarketingUserGroupData> marketingUserGroups = this.getMarketingUserGroups(ea, marketingEventId);
        result.setMarketingUserGroups(marketingUserGroups);

        List<MarketingActivityLeadSourceCountDTO> dtos = customizeFormDataUserDAO.countLeadSourceByMarketingEventId(marketingEventId);
        dtos.forEach(dto -> {
            if (dto.getSourceType() != null) {
                if (CustomizeFormDataUserSourceTypeEnum.MANKEEP.getType().equals(dto.getSourceType())) {
                    result.setMankeepLeadNum(dto.getCount());
                }
                if (CustomizeFormDataUserSourceTypeEnum.FULL_MARKETING.getType().equals(dto.getSourceType())) {
                    result.setNoticeLeadNum(dto.getCount());
                }
                if (CustomizeFormDataUserSourceTypeEnum.WX_OFFICIAL_ACCOUNTS_MARKETING.getType().equals(dto.getSourceType())) {
                    result.setWeChatServiceLeadNum(dto.getCount());
                }
                if (CustomizeFormDataUserSourceTypeEnum.SMS_MARKETING.getType().equals(dto.getSourceType())) {
                    result.setSmsLeadNum(dto.getCount());
                }
                if (CustomizeFormDataUserSourceTypeEnum.NO_IDENTITY_ENROLL.getType().equals(dto.getSourceType())) {
                    result.setH5LeadNum(dto.getCount());
                }
                if (CustomizeFormDataUserSourceTypeEnum.QYWX_MINIAPP.getType().equals(dto.getSourceType())) {
                    result.setQywxLeadNum(dto.getCount());
                }
            }
        });
        result.sumUpLeadCount();
        result.setUv(calMarketingEventUVStatistic(ea, marketingEventId));
        result.setPv(calMarketingEventsPV(ea, Collections.singletonList(marketingEventId)).get(marketingEventId));
        result.setEnterpriseSpreadNum(calMarketingEventEnterpriseSpreadNum(ea, marketingEventId));

        Map<String, Integer> campaignCountMap = campaignMergeDataManager.getCampaignMergeCountByMarketingIds(ea, Lists.newArrayList(marketingEventId));
        Integer totalCountNum = campaignCountMap.get(marketingEventId);
        result.setTotalNum(totalCountNum != null ? totalCountNum : 0);
        result.setLeadNum(totalCountNum != null ? totalCountNum : 0);
        return result;
    }

    public com.facishare.marketing.common.result.Result<MultiVenueMarketingEventResult> getMultiVenueMarketingEventsDetail(String ea, Integer fsUserId, String marketingEventId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        MultiVenueMarketingEventResult result = new MultiVenueMarketingEventResult();
        MarketingEventData marketingEventData = this.getMarketingEventData(ea, fsUserId, marketingEventId);
        result.setMarketingEvent(marketingEventData);
        List<MarketingUserGroupData> marketingUserGroups = this.getMarketingUserGroups(ea, marketingEventId);
        result.setMarketingUserGroups(marketingUserGroups);

        List<MarketingEventSimpleResult> allResults = Lists.newArrayList();
        // 查询父级市场活动数据
        MarketingEventSimpleResult parentResult = new MarketingEventSimpleResult();
        handleContentMarketingEvent(ea, marketingEventId, parentResult);
        allResults.add(parentResult);

        // 查询子活动数据
        List<MarketingEventData> marketingEventDataList = listMarketingEventDataByParentId(ea, fsUserId, marketingEventId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(marketingEventDataList)) {
            List<MarketingEventSimpleResult> simpleResults = marketingEventDataList.stream().map(data -> {
                String subMarketingEventId = data.getId();
                MarketingEventSimpleResult simpleResult = new MarketingEventSimpleResult();
                simpleResult.setId(subMarketingEventId);
                simpleResult.setName(data.getName());
                simpleResult.setBeginTime(data.getBeginTime());
                simpleResult.setEndTime(data.getEndTime());
                simpleResult.setEventType(data.getEventType());
                simpleResult.setEventForm(data.getEventForm());
                // pv
                simpleResult.setPv(calMarketingEventsPV(ea, Lists.newArrayList(subMarketingEventId)).get(subMarketingEventId));

                // 推广次数
                simpleResult.setSpreadCount(0);
                /*PaasQueryMarketingActivityArg arg = new PaasQueryMarketingActivityArg();
                arg.setMarketingEventId(subMarketingEventId);
                arg.setPageNumber(1);
                arg.setPageSize(20);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(ea, fsUserId, arg);
                simpleResult.setSpreadCount(marketingActivityListVo.getTotal());*/
                String eventForm = data.getEventForm();
                if (Objects.equals(eventForm, MarketingEventFormEnum.LIVE_MARKETING.getValue())) {
                    // 直播
                    MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, subMarketingEventId);
                    simpleResult.setLiveUrl(marketingLiveEntity.getShortViewUrl());
                    com.facishare.marketing.common.result.Result<LiveBriefStatisticsResult> liveStatisticsResult = liveManager.getLiveStatistics(ea, subMarketingEventId);
                    if (liveStatisticsResult.isSuccess()) {
                        LiveBriefStatisticsResult statisticsResult = liveStatisticsResult.getData();
                        simpleResult.setViewCount(statisticsResult.getViewCount());
                        simpleResult.setReplayCount(statisticsResult.getViewRecordUserCount());
                        simpleResult.setSpreadCount(statisticsResult.getSpreadCount());
                        simpleResult.setChatCount(statisticsResult.getChatUserCount());
                        simpleResult.setEnrollCount(statisticsResult.getEnrollCount());
                        simpleResult.setSignInCount(0);
                    }
                } else if (Objects.equals(eventForm, MarketingEventFormEnum.CONFERENCE_MARKETING.getValue())) {
                    // 会议
                    ActivityEntity entity = conferenceDAO.getConferenceByMarketingEventId(subMarketingEventId, ea);
                    if (Objects.nonNull(entity)) {
                        simpleResult.setUniqueId(entity.getId());
                    }
                    simpleResult.setLocation(data.getLocation());
                    com.facishare.marketing.common.result.Result<GetConferenceStatisticDataResult> confemarenceStatisticResult = conferenceManager.getConferenceStatistic(ea, subMarketingEventId);
                    if (confemarenceStatisticResult.isSuccess()) {
                        GetConferenceStatisticDataResult statisticsResult = confemarenceStatisticResult.getData();
                        simpleResult.setSignInCount(statisticsResult.getSignInCount());
                        simpleResult.setEnrollCount(statisticsResult.getEnrollCount());
                    }
                } else {
                    handleContentMarketingEvent(ea, subMarketingEventId, simpleResult);
                }
                return simpleResult;
            }).collect(Collectors.toList());
            result.setSubMarketingEvents(simpleResults);
            allResults.addAll(simpleResults);
        }
        // 统计数据
        result.setSpreadCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getSpreadCount).sum());
        result.setPv(allResults.stream().mapToInt(MarketingEventSimpleResult::getPv).sum());
        result.setEnrollCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getEnrollCount).sum());
        result.setSignInCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getSignInCount).sum());
        result.setViewCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getViewCount).sum());
        result.setReplayCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getReplayCount).sum());
        result.setChatCount(allResults.stream().mapToInt(MarketingEventSimpleResult::getChatCount).sum());
        return com.facishare.marketing.common.result.Result.newSuccess(result);
    }

    private void handleContentMarketingEvent(String ea, String marketingEventId, MarketingEventSimpleResult simpleResult){
        simpleResult.setPv(calMarketingEventsPV(ea, Lists.newArrayList(marketingEventId)).get(marketingEventId));
        simpleResult.setSpreadCount(calMarketingEventEnterpriseSpreadNum(ea, marketingEventId));
        // 报名人次
        Integer count = campaignMergeDataDAO.countCampaignMergeDataByMarketingEventId(ea, marketingEventId);
        simpleResult.setEnrollCount(count);
    }

    public Integer calMarketingEventUVStatistic(String ea, String marketingEventId) {
        com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uVMaps = marketingEventStatisticService.getUVs(ea, Sets.newHashSet(marketingEventId));
        if (uVMaps.getCode() != 0 || MapUtils.isEmpty(uVMaps.getData())) {
            return 0;
        }
        Integer result = uVMaps.getData().get(marketingEventId);
        return result != null ? result : 0;
    }

    public Map<String, Integer> calMarketingEventsPV(String ea, List<String> marketingEventIds) {
        com.facishare.marketing.statistic.common.result.Result<Map<String, CommonStatisticResult>> statisticResult = contentMarketingEventStatisticService
            .listContentMarketingEventsStatistic(ea, marketingEventIds);
        if (statisticResult.getData() == null) {
            return new HashMap<>();
        }
        return marketingEventIds.stream().collect(Collectors.toMap(marketingEventId -> marketingEventId, marketingEventId -> {
            CommonStatisticResult commonStatisticResult = statisticResult.getData().get(marketingEventId);
            if (commonStatisticResult != null) {
                return commonStatisticResult.getPv();
            }
            return 0;
        }, (v1, v2) -> v1));
    }

    public int calMarketingEventEnterpriseSpreadNum(String ea, String marketinEventId) {
        return marketingActivityExternalConfigDao.countMarketingEventEnterpriseSpreadCount(ea, marketinEventId);
    }

    public int calMarketingEventLeadNum(String marketingEventId) {
        return customizeFormDataUserDAO.countLeadByMarketingEventId(marketingEventId);
    }

    public int countLeadByMarketingEventIdAndTimePoint(String marketingEventId, Date timePoint){
        return customizeFormDataUserDAO.countLeadByMarketingEventIdAndTimePoint(marketingEventId, timePoint);
    }

    public int calMarketingEventContentNum(String ea, String marketingEventId, Set<Integer> objectTypes) {
        return contentMarketingEventMaterialRelationDAO.count(ea, marketingEventId, Lists.newArrayList(objectTypes), false);
    }

    public Map<String, Integer> batchCalMarketingEventContentNum(String ea, List<String> marketingEventIds, Set<Integer> objectTypes) {
        Map<String, Integer> countMaps = new HashMap<>();
        List<Map<String, Object>> groupCount = contentMarketingEventMaterialRelationDAO.groupCount(ea, marketingEventIds, Lists.newArrayList(objectTypes));
        if (CollectionUtils.isNotEmpty(groupCount)) {
            groupCount.forEach(e -> {
                countMaps.put(String.valueOf(e.get("id")), Integer.valueOf(e.get("count").toString()));
            });
        }
        return countMaps;
    }

    public int calMarketingEventUV(String ea, String marketingEventId) {
        int sum = 0;
        List<String> marketingActivityIds = marketingActivityExternalConfigDao.listMarketingActivityIdsByEaAndMarketingEventId(ea, marketingEventId, 0, 2000);
        if (!marketingActivityIds.isEmpty()) {
            com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uvStatisticResult = marketingActivityStatisticService.getUVs(ea, new HashSet<>(marketingActivityIds));
            Map<String, Integer> uvMap = uvStatisticResult.getData();
            if (uvMap != null) {
                for (Integer value : uvMap.values()) {
                    if (value != null) {
                        sum += value;
                    }
                }
            }
        }
        return sum;
    }

    public Map<String, Integer> batchCalMarketingEventUV(String ea, List<String> marketingEventIds){
        Map<String, Integer> uvMaps = new HashMap<>();
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return null;
        }
        List<MarketingEventActivityDTO> marketingEventActivityDTOs = marketingActivityExternalConfigDao.batchListMarketingActivityIdsByEaAndMarketingEventId(ea, marketingEventIds, 0, 2000);
        if (CollectionUtils.isEmpty(marketingEventActivityDTOs)){
            return null;
        }
        Map<String, String> marketingEventActivityMap = marketingEventActivityDTOs.stream().filter(dto -> dto.getMarketingActivityId() != null && dto.getMarketingEventId() != null).
                collect(Collectors.toMap(MarketingEventActivityDTO::getMarketingActivityId, MarketingEventActivityDTO::getMarketingEventId, (v1,v2)->v1));
        if (marketingEventActivityMap == null){
            return null;
        }
        com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uvStatisticResult = marketingActivityStatisticService.getUVs(ea, new HashSet<>(marketingEventActivityMap.keySet()));
        if (uvStatisticResult.getData() == null){
            return null;
        }

        for (Map.Entry<String, Integer> entry : uvStatisticResult.getData().entrySet()){
            String marketingActivityId = entry.getKey();
            Integer uv = entry.getValue();
            String marketingEventId = marketingEventActivityMap.get(marketingActivityId);
            if (uv != null && marketingEventId != null){
                uvMaps.merge(marketingEventId, uv, Integer::sum);
            }
        }
        return uvMaps;
    }

    public Map<String, Integer> batchCalMarketingEventUVStatistic(String ea, List<String> marketingEventIds) {
        com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uVMaps = marketingEventStatisticService.getUVs(ea, Sets.newHashSet(marketingEventIds));
        if (uVMaps.getCode() != 0 || MapUtils.isEmpty(uVMaps.getData())) {
            return Maps.newHashMap();
        }
        return uVMaps.getData();
    }

    public List<MarketingEventsBriefResult> convert2MarketingEventsBriefResults(List<com.fxiaoke.crmrestapi.common.data.MarketingEventData> marketingEventDataList) {
        List<MarketingEventsBriefResult> marketingEventsBriefResults = Lists.newArrayList();
        for (com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventData : marketingEventDataList) {
            MarketingEventsBriefResult data = new MarketingEventsBriefResult();
            data.setBeginTime(marketingEventData.getBeginTime());
            data.setEndTime(marketingEventData.getEndTime());
            data.setBizStatus(marketingEventData.getBizStatus());
            data.setEventType(marketingEventData.getEventType());
            data.setEventForm(marketingEventData.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()));
            data.setLocation(marketingEventData.getLocation());
            data.setName(marketingEventData.getName());
            data.setId(marketingEventData.getId());
            data.setActualCost(marketingEventData.getActualCost());
            data.setOwners(marketingEventData.getOwners());
            data.setLifeStatus(marketingEventData.getLifeStatus());
            if(Objects.nonNull(marketingEventData.get("cover"))){
                data.setCover((List)marketingEventData.get("cover"));
            }
            marketingEventsBriefResults.add(data);
        }
        return marketingEventsBriefResults;
    }

    public MarketingEventData getMarketingEventData(String ea, Integer fsUserId, String id) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(id);
        arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        arg.setIncludeStatistics(false);
        ObjectData objectData = metadataControllerServiceManager.detail(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, arg);
        com.fxiaoke.crmrestapi.common.data.MarketingEventData data = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(objectData);
        return convert2MarketingEventData(data);
    }

    public List<MarketingUserGroupData> getMarketingUserGroups(String ea, String marketingEventId) {
        List<ContentMarketingEventMarketingUserGroupRelationEntity> contentMarketingEventMarketingUserGroupRelationEntities = contentMarketingEventMarketingUserGroupRelationDAO
            .list(ea, marketingEventId);
        Set<String> marketingUserGroupIds = contentMarketingEventMarketingUserGroupRelationEntities.stream().map(ContentMarketingEventMarketingUserGroupRelationEntity::getMarketingUserGroupId)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(marketingUserGroupIds)) {
            return null;
        }
        List<MarketingUserGroupEntity> marketingUserGroupEntities = marketingUserGroupDao.batchGet(ea, Lists.newArrayList(marketingUserGroupIds));
        return convert2MarketingUserGroupDatas(marketingUserGroupEntities);
    }

    public com.facishare.marketing.common.result.PageResult<AbstractMaterialData> getMaterials(String ea, List<Integer> objectTypes, String marketingEventId, Integer pageNo, Integer limit, boolean needCheckMobileDisplay,String keyword) {
        if (CollectionUtils.isEmpty(objectTypes)) {
            objectTypes = ObjectTypeEnum.getAllTypes();
        }
//        // 若为会议需要过滤会议本身的微页面
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        List<String> filterIds = null;
        String evenId;
        if (activityEntity != null && org.apache.commons.lang3.StringUtils.isNotBlank(activityEntity.getActivityDetailSiteId()) && activityEntity.getDefaultContentMobileDisplay() != null && (!activityEntity.getDefaultContentMobileDisplay() || !needCheckMobileDisplay)) {
//            filterIds = Lists.newArrayList();
//            filterIds.add(activityEntity.getActivityDetailSiteId());
            evenId = activityEntity.getActivityDetailSiteId();
        } else {
            evenId = null;
        }
        //过滤视频号直播中转页面
        MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (liveEntity != null && liveEntity.getId() != null) {
            if(liveEntity.getPlatform()!=null && liveEntity.getPlatform() == 5){
                List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities1 = contentMarketingEventMaterialRelationDAO
                        .listByMarketingEventIdAndObjectType(ea, marketingEventId, objectTypes, filterIds);
                List<String> sitelist = contentMarketingEventMaterialRelationEntities1.stream().map(o -> o.getObjectId()).collect(Collectors.toList());
                List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.listByIds(sitelist);
                List<String> list = hexagonSiteEntityList.stream().filter(o -> o.getName().equals("视频号中转页面")).map(o -> o.getId()).collect(Collectors.toList());
                if(list.size()!=0){
                    filterIds = list;
                }
            }
        }

        List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO
            .listV1(ea, marketingEventId, objectTypes, filterIds, (pageNo - 1) * limit, limit, needCheckMobileDisplay,evenId,keyword);

        Integer totalCount = contentMarketingEventMaterialRelationDAO.countWithKeyword(ea, marketingEventId, objectTypes, needCheckMobileDisplay,keyword);
        Map<Integer, List<String>> classifyObjectIdsByObjectType = Maps.newHashMap();
        for (ContentMarketingEventMaterialRelationEntity entity : contentMarketingEventMaterialRelationEntities) {
            classifyObjectIdsByObjectType.computeIfAbsent(entity.getObjectType(), k -> Lists.newArrayList());
            classifyObjectIdsByObjectType.get(entity.getObjectType()).add(entity.getObjectId());
        }
        List<AbstractMaterialData> materialDataList = Lists.newArrayList();
        classifyObjectIdsByObjectType.forEach((k, v) -> {
            List<AbstractMaterialData> materialDatas = materailDataManagerFactory.get(k).get(ea, ListUtil.toArray(v, String[].class));
            materialDataList.addAll(materialDatas);
        });
        this.fillOrCreateDefaultMarketingActivityIds(ea, marketingEventId, materialDataList);


        Map<ObjectTypeAndIdData, AbstractMaterialData> objectTypeAndIdDataAbstractMaterialDataMap = materialDataList.stream()
            .collect(Collectors.toMap(val -> new ObjectTypeAndIdData(val.getObjectType(), val.getId()), val -> val));
        List<AbstractMaterialData> dataList = contentMarketingEventMaterialRelationEntities.stream()
            .map(val -> objectTypeAndIdDataAbstractMaterialDataMap.get(new ObjectTypeAndIdData(val.getObjectType(), val.getObjectId()))).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, ContentMarketingEventMaterialRelationEntity> relationMap = contentMarketingEventMaterialRelationEntities.stream().filter(Objects::nonNull).collect(Collectors.toMap(ContentMarketingEventMaterialRelationEntity::getObjectId, v->v, (v1, v2) -> v2));
        // 创建人
        List<Integer> creators = dataList.stream().filter(Objects::nonNull).map(AbstractMaterialData::getCreator).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> creatorToCreatorInfo = fsAddressBookManager.getEmployeeInfoByUserIds(ea, creators, true);
        dataList.forEach(data -> {
            if (relationMap.get(data.getId()) != null){
                data.setRelationId(relationMap.get(data.getId()).getId());
                data.setIsApplyObject(relationMap.get(data.getId()).getIsApplyObject());
                data.setIsMobileDisplay(relationMap.get(data.getId()).getIsMobileDisplay());
            }
            FsAddressBookManager.FSEmployeeMsg creatorInfo = creatorToCreatorInfo.get(data.getCreator());
            if (creatorInfo != null) {
                data.setCreatorName(StringUtils.isNotEmpty(creatorInfo.getFullName()) ? creatorInfo.getFullName() : creatorInfo.getName());
            }
        });
        return com.facishare.marketing.common.result.PageResult.newPageResult(pageNo, limit, totalCount, dataList);
    }

    public com.facishare.marketing.common.result.PageResult<AbstractMaterialData> listMaterialsByMarketingEvent(String ea, Integer objectType, String marketingEventId, String title, Integer pageNo, Integer limit){
        List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities = null;
        if (objectType == ObjectTypeEnum.ARTICLE.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getArticleContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if (objectType == ObjectTypeEnum.PRODUCT.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getProductContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getHexagonContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
            MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
            if(liveEntity != null && liveEntity.getPlatform() != null && liveEntity.getPlatform() == 5 && contentMarketingEventMaterialRelationEntities != null){
                contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationEntities.stream().filter(o -> !hexagonSiteDAO.getById( o.getObjectId()).getName().equals("视频号中转页面")).collect(Collectors.toList());
            }
        }else if (objectType == ObjectTypeEnum.QR_POSTER.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getQrcodeContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if (objectType == ObjectTypeEnum.ACTIVITY_INVITATION.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getInvitationContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if (objectType == ObjectTypeEnum.ACTIVITY.getType()){
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getActivityContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if(objectType == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getCustomizeFormDataContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }else if (objectType == ObjectTypeEnum.OUT_LINK.getType()) {
            contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.getOutLinkContentMarketingRelation(ea, marketingEventId, objectType, title, (pageNo - 1) * limit, limit);
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(contentMarketingEventMaterialRelationEntities)){
            return com.facishare.marketing.common.result.PageResult.newPageResult(pageNo, limit, 0, new ArrayList<>());
        }

        Integer totalCount = contentMarketingEventMaterialRelationDAO.count(ea, marketingEventId, ImmutableList.of(objectType), false);
        Map<Integer, List<String>> classifyObjectIdsByObjectType = Maps.newHashMap();
        for (ContentMarketingEventMaterialRelationEntity entity : contentMarketingEventMaterialRelationEntities) {
            classifyObjectIdsByObjectType.computeIfAbsent(entity.getObjectType(), k -> Lists.newArrayList());
            classifyObjectIdsByObjectType.get(entity.getObjectType()).add(entity.getObjectId());
        }
        List<AbstractMaterialData> materialDataList = Lists.newArrayList();
        classifyObjectIdsByObjectType.forEach((k, v) -> {
            List<AbstractMaterialData> materialDatas = materailDataManagerFactory.get(k).get(ea, ListUtil.toArray(v, String[].class));
            this.fillOrCreateDefaultMarketingActivityIds(ea, marketingEventId, materialDatas);
            materialDataList.addAll(materialDatas);
        });
        Map<ObjectTypeAndIdData, AbstractMaterialData> objectTypeAndIdDataAbstractMaterialDataMap = materialDataList.stream()
                .collect(Collectors.toMap(val -> new ObjectTypeAndIdData(val.getObjectType(), val.getId()), val -> val));
        List<AbstractMaterialData> dataList = contentMarketingEventMaterialRelationEntities.stream()
                .map(val -> objectTypeAndIdDataAbstractMaterialDataMap.get(new ObjectTypeAndIdData(val.getObjectType(), val.getObjectId()))).filter(Objects::nonNull).collect(Collectors.toList());
        return com.facishare.marketing.common.result.PageResult.newPageResult(pageNo, limit, totalCount, dataList);
    }

    protected void fillOrCreateDefaultMarketingActivityIds(String ea, String marketingEventId, List<AbstractMaterialData> datas) {
        for (AbstractMaterialData data : datas) {
            if (data.getObjectType() == ObjectTypeEnum.ARTICLE.getType() || data.getObjectType() == ObjectTypeEnum.PRODUCT.getType() || data.getObjectType() == ObjectTypeEnum.QR_POSTER.getType()
                || data.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                MarketingActivityPersonalNoticeSendArg marketingActivityPersonalNoticeSendArg = new MarketingActivityPersonalNoticeSendArg();
                marketingActivityPersonalNoticeSendArg.setAssociateId(data.getId());
                marketingActivityPersonalNoticeSendArg.setAssociateIdType(data.getObjectType());
                marketingActivityPersonalNoticeSendArg.setTitle(data.getTitle());
                marketingActivityPersonalNoticeSendArg.setMarketingEventId(marketingEventId);
                String marketingActivityId = marketingActivityManager.getMarketingActivityIdAndCreateIfNeed(ea, SuperUserConstants.USER_ID, marketingActivityPersonalNoticeSendArg);
                data.setDefaultMarketingActivityId(marketingActivityId);
            }
        }
    }

    private MarketingEventData convert2MarketingEventData(com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventData) {
        MarketingEventData data = new MarketingEventData();
        data.setBeginTime(marketingEventData.getBeginTime());
        data.setEndTime(marketingEventData.getEndTime());
        data.setBizStatus(marketingEventData.getBizStatus());
        data.setEventType(marketingEventData.getEventType());
        data.setLocation(marketingEventData.getLocation());
        data.setName(marketingEventData.getName());
        data.setId(marketingEventData.getId());
        data.setActualCost(marketingEventData.getActualCost());
        data.setActualIncome(marketingEventData.getActualIncome());
        data.setExpectedCost(marketingEventData.getExpectedCost());
        data.setExpectedIncome(marketingEventData.getExpectedIncome());
        data.setOwnerId(marketingEventData.getOwner());
        data.setParentId((String) marketingEventData.get(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName()));
        data.setLifeStatus(marketingEventData.getLifeStatus());
        data.setCreateTime(marketingEventData.getCreateTime());
        data.setCover(getMarketingEventCoverByData(marketingEventData));
        data.setEventForm(marketingEventData.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()));
        return data;
    }

    private String getMarketingEventCoverByData(com.fxiaoke.crmrestapi.common.data.MarketingEventData marketingEventData){
        if (marketingEventData == null){
            return null;
        }
        Object coverObj = marketingEventData.get("cover");
        if (coverObj == null){
            return null;
        }
        List<Map<String,Object>> coverList = (List<Map<String, Object>>) coverObj;
        if (CollectionUtils.isEmpty(coverList)){
            return null;
        }
        log.info("getMarketingEventCoverByData marketingEventId:{}  cover:{}", marketingEventData.getId(), (String)coverList.get(0).get("path"));
        return (String)coverList.get(0).get("path");
    }


    private List<MarketingUserGroupData> convert2MarketingUserGroupDatas(List<MarketingUserGroupEntity> marketingUserGroupEntities) {
        List<MarketingUserGroupData> marketingUserGroupDatas = Lists.newArrayList();
        for (MarketingUserGroupEntity entity : marketingUserGroupEntities) {
            MarketingUserGroupData marketingUserGroupData = new MarketingUserGroupData();
            marketingUserGroupData.setId(entity.getId());
            marketingUserGroupData.setName(entity.getName());
            marketingUserGroupDatas.add(marketingUserGroupData);
        }
        return marketingUserGroupDatas;
    }

    public List<MarketingEventData> listMarketingEventData(String ea, Integer fsUserId, List<String> ids) {
        List<MarketingEventData> resultList = Lists.newArrayList();
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        ControllerListArg arg = new ControllerListArg();
        arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        SearchQuery query = new SearchQuery();
        query.addFilter(ObjectDescribeContants.ID, ids, OperatorConstants.IN);
        arg.setSearchQuery(query);
        Page<ObjectData> objectDataPage = metadataControllerServiceManager.list(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, arg);
        if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
            objectDataPage.getDataList().forEach(objectData -> {
                com.fxiaoke.crmrestapi.common.data.MarketingEventData data = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(objectData);
                resultList.add(convert2MarketingEventData(data));
            });
        }

        return resultList;
    }

    /**
     * 查询子市场活动
     * @param ea
     * @param fsUserId
     * @param parentEventId
     * @return
     */
    public List<MarketingEventData> listMarketingEventDataByParentId(String ea, Integer fsUserId, String parentEventId) {
        List<MarketingEventData> resultList = Lists.newArrayList();
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        ControllerListArg arg = new ControllerListArg();
        arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        SearchQuery query = new SearchQuery();
        query.setLimit(200);
        query.setOffset(0);
        query.addFilter("parent_id", Lists.newArrayList(parentEventId), OperatorConstants.EQ);
        arg.setSearchQuery(query);
        try {
            Page<ObjectData> objectDataPage = metadataControllerServiceManager.list(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, arg);
            if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
                objectDataPage.getDataList().forEach(objectData -> {
                    com.fxiaoke.crmrestapi.common.data.MarketingEventData data = com.fxiaoke.crmrestapi.common.data.MarketingEventData.wrap(objectData);
                    resultList.add(convert2MarketingEventData(data));
                });
            }
        } catch (Exception e) {
            log.error("MarketingEventManager -> listMarketingEventDataByParentId error", e);
            log.warn("exception:",  e);
        }
        return resultList;
    }

    /**
     * 批量查询子活动数量
     * @param ea
     * @param fsUserId
     * @param parentEventIds
     * @return
     */
    public Map<String, Long> batchGetSubTotalByParentIds(String ea, Integer fsUserId, List<String> parentEventIds) {
        Map<String, Long> countMaps = new HashMap<>();
        try {
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(MarketingEventFieldContants.API_NAME);
            findByQueryV3Arg.setSelectFields(Lists.newArrayList("_id", "parent_id"));
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
            paasQueryArg.addFilter("parent_id", OperatorConstants.IN, parentEventIds);
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                List<ObjectData> dataList = objectDataInnerPage.getDataList();
                countMaps = dataList.stream().collect(Collectors.groupingBy(e->e.getString("parent_id"), Collectors.counting()));
            }
        } catch (Exception e) {
            log.warn("MarketingEventManager -> batchGetSubTotalByParentIds error", e);
        }
        return countMaps;
    }

    /**
     * 查询子市场活动数量
     * @param ea
     * @param fsUserId
     * @param parentEventId
     * @return
     */
    public Integer getSubTotalByParentId(String ea, Integer fsUserId, String parentEventId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        ControllerListArg arg = new ControllerListArg();
        arg.setObjectDescribeApiName(MarketingEventFieldContants.API_NAME);
        SearchQuery query = new SearchQuery();
        query.addFilter("parent_id", Lists.newArrayList(parentEventId), OperatorConstants.EQ);
        query.setLimit(1);
        query.setOffset(0);
        arg.setSearchQuery(query);
        try {
            Integer total = metadataControllerServiceManager.getTotalOnlyId(new HeaderObj(ei, fsUserId), MarketingEventFieldContants.API_NAME, arg);
            return total;
        } catch (Exception e) {
            log.error("MarketingEventManager -> getSubTotalByParentId error", e);
            log.warn("exception:",  e);
        }
        return 0;
    }

    public Map<String, Long> getSubTotalByParentIds(String ea, Integer fsUserId, List<String> parentEventIds) {
        Map<String, Long> countMaps = new HashMap<>();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("parent_id", OperatorConstants.IN, parentEventIds);
        try {
            List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, MarketingEventFieldContants.API_NAME, Lists.newArrayList("_id", "parent_id"), query);
            if (CollectionUtils.isNotEmpty(objectDataList)) {
                countMaps = objectDataList.stream().collect(Collectors.groupingBy(e->e.getString("parent_id"), Collectors.counting()));
            }
        } catch (Exception e) {
            log.error("MarketingEventManager -> getSubTotalByParentId error", e);
            log.warn("exception:",  e);
        }
        return countMaps;
    }

    /**
     * 更新当前活动的父活动id
     * @param ea
     * @param parentMarketingEventId
     * @param subMarketingEventId
     */
    public com.facishare.marketing.common.result.Result<ActionEditResult> updateParentIdToCrm(String ea, String parentMarketingEventId, String subMarketingEventId){
        if (StringUtils.isBlank(subMarketingEventId)) {
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ObjectData objectData = new ObjectData();
        objectData.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), parentMarketingEventId);
        objectData.put(CrmV2MarketingEventFieldEnum.ID.getFieldName(), subMarketingEventId);

        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(objectData);
        Result<ActionEditResult> result = metadataActionServiceManager.update(ei, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), arg);
        log.info("MarketingEventManager -> updateParentToCrm params:{}, result:{}", arg, result);
        return convertCrmResult(result);
    }

    private com.facishare.marketing.common.result.Result convertCrmResult(Result crmResult){
        return com.facishare.marketing.common.result.Result.newError(crmResult.getCode(), crmResult.getMessage(), crmResult.getData());
    }

    public void syncContentMarketingEventMaterialRelationEventType(){
        List<String> ids = contentMarketingEventMaterialRelationDAO.getEventTypeEmptyIds();
        PageUtil pageUtil = new PageUtil(ids, 500);
        Map<String, String> idMap = new HashMap<>();
        log.info("syncContentMarketingEventMaterialRelationEventType total count:{}", ids.size());
        int current = 0;
        for (int i = 1; i <= pageUtil.getPageSize(); i++){
            idMap.clear();
            List<String> currentIds = pageUtil.getPagedList(i);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentIds)){
                List<ContentMarketingEventMaterialRelationEntity> entityList = contentMarketingEventMaterialRelationDAO.getByIds(currentIds);
               for (ContentMarketingEventMaterialRelationEntity entity : entityList){
                   if (entity.getMarketingEventId() == null){
                       continue;
                   }

                   if (conferenceDAO.getConferenceByMarketingEventId(entity.getMarketingEventId(), entity.getEa()) != null){
                       idMap.put(entity.getId(), MarketingEventEnum.MEETING_SALES.getEventType());
                   }else if (marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(entity.getEa()), entity.getMarketingEventId()) != null){
                       idMap.put(entity.getId(), MarketingEventEnum.LIVE_MARKETING.getEventType());
                   }else {
                       try {
                           MarketingEventData marketingEventData = getMarketingEventData(entity.getEa(), -10000, entity.getMarketingEventId());
                           if (marketingEventData == null) {
                               idMap.put(entity.getId(), MarketingEventEnum.CONTENT_MARKETING.getEventType());
                           } else {
                               idMap.put(entity.getId(), marketingEventData.getEventType());
                           }
                       }catch (Exception e){
                           log.info("syncContentMarketingEventMaterialRelationEventType e:", e);
                           idMap.put(entity.getId(), MarketingEventEnum.CONTENT_MARKETING.getEventType());
                       }
                   }
               }

               //更新eventType
               for (ContentMarketingEventMaterialRelationEntity entity : entityList){
                   if (idMap.get(entity.getId()) != null) {
                       contentMarketingEventMaterialRelationDAO.updateEventType(entity.getId(), idMap.get(entity.getId()));
                   }
               }
                current += entityList.size();
               log.info("syncContentMarketingEventMaterialRelationEventType execute size:{} total:{}", current, ids.size());
            }
        }
        log.info("syncContentMarketingEventMaterialRelationEventType finish total:{}", ids.size());
    }

    public List<AbstractMaterialData> getContent(String ea, HexagonStatisticArg arg, ArrayList<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities) {
        Map<Integer, List<String>> classifyObjectIdsByObjectType = Maps.newHashMap();
        for (ContentMarketingEventMaterialRelationEntity entity : contentMarketingEventMaterialRelationEntities) {
            classifyObjectIdsByObjectType.computeIfAbsent(entity.getObjectType(), k -> Lists.newArrayList());
            classifyObjectIdsByObjectType.get(entity.getObjectType()).add(entity.getObjectId());
        }
        List<AbstractMaterialData> materialDataList = Lists.newArrayList();
        classifyObjectIdsByObjectType.forEach((k, v) -> {
            List<AbstractMaterialData> materialDatas = materailDataManagerFactory.get(k).get(ea, ListUtil.toArray(v, String[].class));
            materialDataList.addAll(materialDatas);
        });
        this.fillOrCreateDefaultMarketingActivityIds(ea, arg.getMarketingEvenId(), materialDataList);
        Map<ObjectTypeAndIdData, AbstractMaterialData> objectTypeAndIdDataAbstractMaterialDataMap = materialDataList.stream()
                .collect(Collectors.toMap(val -> new ObjectTypeAndIdData(val.getObjectType(), val.getId()), val -> val));
        List<AbstractMaterialData> dataList = contentMarketingEventMaterialRelationEntities.stream()
                .map(val -> objectTypeAndIdDataAbstractMaterialDataMap.get(new ObjectTypeAndIdData(val.getObjectType(), val.getObjectId()))).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, ContentMarketingEventMaterialRelationEntity> relationMap = contentMarketingEventMaterialRelationEntities.stream().filter(Objects::nonNull).collect(Collectors.toMap(ContentMarketingEventMaterialRelationEntity::getObjectId, v->v, (v1, v2) -> v2));
        // 创建人
        List<Integer> creators = dataList.stream().filter(Objects::nonNull).map(AbstractMaterialData::getCreator).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> creatorToCreatorInfo = fsAddressBookManager.getEmployeeInfoByUserIds(ea, creators, true);
        dataList.forEach(data -> {
            if (relationMap.get(data.getId()) != null){
                data.setRelationId(relationMap.get(data.getId()).getId());
                data.setIsApplyObject(relationMap.get(data.getId()).getIsApplyObject());
            }
            FsAddressBookManager.FSEmployeeMsg creatorInfo = creatorToCreatorInfo.get(data.getCreator());
            if (creatorInfo != null) {
                data.setCreatorName(com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(creatorInfo.getFullName()) ? creatorInfo.getFullName() : creatorInfo.getName());
            }
        });
        return dataList;
    }

    public void addOrUpdateAdSourceField(String ea, ObjectDescribe objectDescribe) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);

            if (!objectDescribe.getFields().containsKey("ad_source")) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"广告渠道\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"百度\",\"value\":\"1\"},{\"label\":\"腾讯\",\"value\":\"2\"},{\"label\":\"巨量引擎\",\"value\":\"3\"},{\"label\":\"360\",\"value\":\"4\"},{\"label\":\"谷歌\",\"value\":\"5\"},{\"label\":\"Linkedin\",\"value\":\"6\"},{\"label\":\"Facebook\",\"value\":\"7\"},{\"label\":\"Tiktok\",\"value\":\"8\"},{\"label\":\"其它\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"广告渠道\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"广告渠道\",\"target_api_name\":\"MarketingEventObj\",\"api_name\":\"ad_source\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"MarketingEventObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true}]");
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("add adSource Filed ea:{} result: {}", ea, result);
                // 有时候添加完字段了部分选项会没有，见鬼，所以这里不能返回
            }
            FieldDescribe fieldDescribe = objectDescribe.getFields().get("ad_source");
            List<Map<String, Object>> options = fieldDescribe.getOptions();
            Set<String> optionValueSet = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toSet());
            boolean needUpdate = false;
            if (!optionValueSet.contains("5")) {
                Map<String, Object> googleAdOption = Maps.newHashMap();
                googleAdOption.put("label", "谷歌");
                googleAdOption.put("value", "5");
                options.add(googleAdOption);
                needUpdate = true;
            }

            if (!optionValueSet.contains("6")) {
                Map<String, Object> linkedInOptional = Maps.newHashMap();
                linkedInOptional.put("label", "Linkedin");
                linkedInOptional.put("value", "6");
                options.add(linkedInOptional);
                needUpdate = true;
            }

            if (!optionValueSet.contains("7")) {
                Map<String, Object> facebookOption = Maps.newHashMap();
                facebookOption.put("label", "Facebook");
                facebookOption.put("value", "7");
                options.add(facebookOption);
                needUpdate = true;
            }

            if (!optionValueSet.contains("8")) {
                Map<String, Object> tiktokOption = Maps.newHashMap();
                tiktokOption.put("label", "Tiktok");
                tiktokOption.put("value", "8");
                options.add(tiktokOption);
                needUpdate = true;
            }

            if (needUpdate) {
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), "ad_source", fieldDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.error("addOrUpdateAdSourceField error, ea: {} result: {}", ea, describeResult);
                }
            }
        } catch (Exception e) {
            log.error("addOrUpdateAdSourceField error, ea: {} ", ea, e);
        }
    }

    public void addOCPCLaunchField(String ea, ObjectDescribe objectDescribe) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
            if (objectDescribe.getFields().containsKey("ocpc_launch")) {
                log.info("已存在ocpc投放字段， ea: {}", ea);
                return;
            }
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"ocpc投放\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"是\",\"value\":\"yes\"},{\"label\":\"否\",\"value\":\"no\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"广告渠道\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"ocpc投放\",\"target_api_name\":\"MarketingEventObj\",\"api_name\":\"ocpc_launch\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addOCPCLaunchField ea:{} result: {}", ea, result);
        } catch (Exception e) {
            log.error("addOCPCLaunchField error, ea: {}", ea, e);
        }

    }

    public void addAdCampaignId(String ea, ObjectDescribe objectDescribe) {
        try {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
            if (objectDescribe.getFields().containsKey("ad_campaign_id")) {
                log.info("已存在ad_campaign_id字段， ea: {}", ea);
                return;
            }
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"广告计划id\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"广告计划id\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"广告计划id\",\"target_api_name\":\"MarketingEventObj\",\"api_name\":\"ad_campaign_id\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addAdCampaignId ea:{} result: {}", ea, result);
        } catch (Exception e) {
            log.error("addAdCampaignId error, ea: {}", ea, e);
        }

    }

    //判断两个时间是否在同一天
    public boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
        boolean isSameMonth = cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
        boolean isSameDay = cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);

        return isSameYear && isSameMonth && isSameDay;
    }

    public Map<String, Integer> getMarketingEventIdToLeadCountMap(String ea, List<String> marketingEventIdList, Long startTime, Long endTime) {
        if (CollectionUtils.isEmpty(marketingEventIdList)) {
            return Maps.newHashMap();
        }
        AggregateParameterArg aggregateParameterArg = new AggregateParameterArg();
        AggregateParameterArg.GroupByParameter groupByParameter = new AggregateParameterArg.GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList("marketing_event_id"));
        AggregateParameterArg.AggFunction aggFunction = new AggregateParameterArg.AggFunction();
        aggFunction.setAggField("marketing_event_id");
        aggFunction.setAggFunction("count");
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunction));
        aggregateParameterArg.setGroupByParameter(groupByParameter);

        List<com.fxiaoke.crmrestapi.common.data.Filter> filters = Lists.newArrayList();
        com.fxiaoke.crmrestapi.common.data.Filter filter = new com.fxiaoke.crmrestapi.common.data.Filter();
        filter.setFieldName("marketing_event_id");
        filter.setFieldValues(marketingEventIdList);
        filter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        filters.add(filter);
        if (startTime != null) {
            com.fxiaoke.crmrestapi.common.data.Filter startTimeFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
            startTimeFilter.setFieldName("create_time");
            startTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(startTime)));
            startTimeFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.GTE);
            filters.add(startTimeFilter);
        }
        if (endTime != null) {
            com.fxiaoke.crmrestapi.common.data.Filter startTimeFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
            startTimeFilter.setFieldName("create_time");
            startTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(endTime)));
            startTimeFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.LTE);
            filters.add(startTimeFilter);
        }
        aggregateParameterArg.setFilters(filters);
        com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult> result = crmV2Manager.aggregateQuery(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), aggregateParameterArg);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData().getDataList())) {
            return Maps.newHashMap();
        }
        Map<String, Integer> resultMap = Maps.newHashMap();
        for (Map<String, Object> stringObjectMap : result.getData().getDataList()) {
            String marketingEventId = (String) stringObjectMap.get("marketing_event_id");
            int count = ((Double) stringObjectMap.get("groupbycount")).intValue();
            resultMap.put(marketingEventId, count);
        }
        return resultMap;
    }


    public void addAdvertisingTypeOption(String ea, ObjectDescribe objectDescribe) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        Map<String, FieldDescribe> fieldMap = objectDescribe.getFields();
        if (!fieldMap.containsKey(CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName())) {
            String describe = "{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"广告类型\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"展示广告\",\"value\":\"show_ad\"},{\"label\":\"搜索广告\",\"value\":\"so_ad\"},{\"label\":\"通投广告\",\"value\":\"all_ad\"},{\"label\":\"其它\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"广告类型\",\"is_need_convert\":false,\"api_name\":\"advertising_type\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"}";
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            arg.setFieldDescribe(describe);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add advertising type field, ea:{} result: {}", ea, result);
            return;
        }
        FieldDescribe fieldDescribe = fieldMap.get(CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName());
        List<Map<String, Object>> options = (List<Map<String, Object>>) (fieldDescribe.get("options"));
        if (CollectionUtils.isEmpty(options)) {
            log.error("addAdvertisingTypeOption failed channel option null ea:{}", ea);
            return;
        }
        Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toSet());
        if (optionValues.contains("all_ad")) {
            log.info("MarketingEventManager.addAdvertisingTypeOption already had option, ea: {}", ea);
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("label", AdvertisingTypeEnum.ALL_AD.getLabel());
        map.put("value", AdvertisingTypeEnum.ALL_AD.getValue());
        options.add(map);
        fieldDescribe.put("options", options);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName(), fieldDescribe);
        if (describeResult == null || !describeResult.isSuccess()) {
            log.info("addAdvertisingTypeOption failed ea:{} describeResult:{}", ea, describeResult);
        }
    }

    public void addMailChimpOption(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            log.warn("marketing event desc is not exist, ea:{}", ea);
            return;
        }
        FieldDescribe fieldDescribe = objectDescribeResult.getData().getDescribe().getFields().get("out_resources");
        if (fieldDescribe == null) {
           log.warn("out_resources field is not exist, ea:{}", ea);
           return;
        }
        boolean hasMailChimpValue = fieldDescribe.getOptions().stream().map(map -> map.get("value").toString()).filter(Objects::nonNull).anyMatch("mail_chimp"::equals);
        if (hasMailChimpValue) {
            log.info("out_resources field has mail_chimp option, ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        AddObjectOptionsArg arg = new AddObjectOptionsArg();
        AddObjectOptionsArg.Item item = new AddObjectOptionsArg.Item();
        item.setFieldApiName("out_resources");
        item.setObjectApiName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        item.setOptions(Lists.newArrayList(new AddObjectOptionsArg.Option("Mailchimp", "mail_chimp")));
        arg.setOptionItems(Lists.newArrayList(item));
        CrmApiResult<CrmApiResult> result = daiLiTongService.addObjectOptions(systemHeader, arg);
        log.info("添加Mailchimp选项, ea: {}, result: {}", ea, result);
    }
    public void addEventFormField(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("市场活动描述不存在 ea:{}", ea);
                return;
            }
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            Map<String, FieldDescribe> fieldMap = objectDescribe.getFields();
            if (!fieldMap.containsKey(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName())) {
                String describe = "{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"活动形式\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"直播营销\",\"value\":\"live_marketing\"},{\"label\":\"会议营销\",\"value\":\"conference_marketing\"},{\"label\":\"多活动组合\",\"value\":\"multivenue_marketing\"},{\"label\":\"线上营销\",\"value\":\"online_marketing\"},{\"label\":\"周期目标人群运营\",\"value\":\"periodicity\"},{\"label\":\"单次目标人群运营\",\"value\":\"once\"},{\"label\":\"广告营销\",\"value\":\"advertise\"},{\"label\":\"其它\",\"value\":\"other\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"活动形式\",\"is_need_convert\":false,\"api_name\":\"event_form\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}";
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                arg.setFieldDescribe(describe);
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                if (result == null || !result.isSuccess()) {
                    log.info("add event_form field, ea:{} result: {}", ea, result);
                }
            }
        } catch (Exception e) {
            log.error("addEventFormFieldAndUpdateEventType error, ea: {}", ea, e);
        }
    }


    public void handleAdvertiseField(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("市场活动描述不存在 ea:{}", ea);
                return;
            }
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            // 处理广告渠道选项
            addOrUpdateAdSourceField(ea, objectDescribe);
            // 处理ocpc字段
            addOCPCLaunchField(ea, objectDescribe);
            // 处理广告计划id
            addAdCampaignId(ea, objectDescribe);
            // 处理广告类型字段
            addAdvertisingTypeOption(ea, objectDescribe);

            Map<String, FieldDescribe> fieldMap = objectDescribe.getFields();

            if (!fieldMap.containsKey(CrmV2MarketingEventFieldEnum.ADVERTISE_LAUNCH_NAME.getFieldName())) {
                String describe = "{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"投放名称\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"投放名称\",\"is_need_convert\":false,\"api_name\":\"advertise_launch_name\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}";
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                arg.setFieldDescribe(describe);
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingEventObj_layout_generate_by_UDObjectServer__c\",\"label\":\"市场活动默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                if (result == null || !result.isSuccess()) {
                    log.info("addAdvertiseField add advertise_launch_name field error, ea:{} result: {}", ea, result);
                }
            }

            if (!fieldMap.containsKey(CrmV2MarketingEventFieldEnum.ADVERTISING_ACCOUNT_ID.getFieldName())) {
                String describe = "{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"广告账户id\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"广告账户id\",\"is_need_convert\":false,\"api_name\":\"advertising_account_id\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}";
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                arg.setFieldDescribe(describe);
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"MarketingEventObj_layout_generate_by_UDObjectServer__c\",\"label\":\"市场活动默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                if (result == null || !result.isSuccess()) {
                    log.info("addAdvertiseField add advertising_account_id field error, ea:{} result: {}", ea, result);
                }
            }

            if (!fieldMap.containsKey(CrmV2MarketingEventFieldEnum.DAY_BUDGET.getFieldName())) {
                String describe = "{\"describe_api_name\":\"MarketingEventObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"currency\",\"decimal_places\":4,\"default_to_zero\":true,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"package\",\"is_single\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"length\":10,\"default_value\":\"\",\"label\":\"单日预算\",\"currency_unit\":\"￥\",\"currency_type\":\"oc\",\"api_name\":\"day_budget\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}";
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                arg.setFieldDescribe(describe);
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"currency\",\"api_name\":\"MarketingEventObj_layout_generate_by_UDObjectServer__c\",\"label\":\"市场活动默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                if (result == null || !result.isSuccess()) {
                    log.info("addAdvertiseField add advertising_account_id field error, ea:{} result: {}", ea, result);
                }
            }
        } catch (Exception e) {
            log.error("handleAdvertiseField error, ea: {}", ea, e);
        }
    }


    public void addEventTypeOption(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("marketing event desc is not exist ea:{}", ea);
                return;
            }
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            Map<String, FieldDescribe> fieldMap = objectDescribe.getFields();
            FieldDescribe fieldDescribe = fieldMap.get(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName());
            if (fieldDescribe == null) {
                log.warn("event type field not exist ea:{}", ea);
                return;
            }
            List<Map<String, Object>> options = (List<Map<String, Object>>) (fieldDescribe.get("options"));
            if (CollectionUtils.isEmpty(options)) {
                log.error("event type option is null ea:{}", ea);
                return;
            }
            Set<String> optionValues = options.stream().map(map -> map.get("value")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toSet());

            boolean needUpdate = false;
            if (!optionValues.contains("live_open_class")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "公开课直播");
                map.put("value", "live_open_class");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("online_salon")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "在线沙龙会");
                map.put("value", "online_salon");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("online_activity_live")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "线上活动直播");
                map.put("value", "online_activity_live");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("offline_salon")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "线下沙龙");
                map.put("value", "offline_salon");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("expert_forum")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "专家论坛");
                map.put("value", "expert_forum");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("seminar")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "研讨会");
                map.put("value", "seminar");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("exchange_meeting")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "交流会");
                map.put("value", "exchange_meeting");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("industry_exhibition")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "行业展会");
                map.put("value", "industry_exhibition");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("product_launch")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "产品发布会");
                map.put("value", "product_launch");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("brand_series_activity")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "品牌系列活动");
                map.put("value", "brand_series_activity");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("large_scale_exhibition")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "大型展会");
                map.put("value", "large_scale_exhibition");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("enterprise_annual_meeting")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "企业年会");
                map.put("value", "enterprise_annual_meeting");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("daily_content_promotion")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "日常内容推广");
                map.put("value", "daily_content_promotion");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("signing_contract_news")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "签约喜报");
                map.put("value", "signing_contract_news");
                options.add(map);
                needUpdate = true;
            }

            if (!optionValues.contains("white_paper_release")) {
                Map<String, Object> map = new HashMap<>();
                map.put("label", "白皮书发布");
                map.put("value", "white_paper_release");
                options.add(map);
                needUpdate = true;
            }
            if (needUpdate) {
                fieldDescribe.put("options", options);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult =
                        objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), fieldDescribe);
                if (describeResult == null || !describeResult.isSuccess()) {
                    log.info("addAdvertisingTypeOption failed ea:{} describeResult:{}", ea, describeResult);
                }
            }
        } catch (Exception e) {
            log.error("addEventTypeOption error, ea: {}", ea, e);
        }
    }

    public void initEventFormField(String ea) {
        List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
        PaasQueryArg.Condition eventFormCondition = new PaasQueryArg.Condition(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), Lists.newArrayList(""), OperatorConstants.IS);
        PaasQueryArg.Condition eventTypeCondition = new PaasQueryArg.Condition(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), Lists.newArrayList(""), OperatorConstants.ISN);
        conditionList.add(eventFormCondition);
        conditionList.add(eventTypeCondition);
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);
        queryFilterArg.setQuery(paasQueryArg);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            log.info("initEventFormField all event form is init, ea: {}", ea);
            return ;
        }
        int count = 0;
        String lastId = null;
        int pageSize = 100;

        com.facishare.marketing.common.result.Result<GetMarketingEventCommonSettingResult> commonSettingResult = marketingEventCommonSettingService.getMarketingEventCommonSetting(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType(), ea);
        if (commonSettingResult == null || !commonSettingResult.isSuccess() || commonSettingResult.getData() == null) {
            log.error("initEventFormField error, ea: {} commonSettingResult is null", ea);
            return;
        }
        GetMarketingEventCommonSettingResult commonSetting = commonSettingResult.getData();
        Set<String> onlineApiName = Sets.newHashSet();
        commonSetting.getActivityTypeMapping().stream().map(ActivityTypeMapping.ActivityTypeMappingDetail::getMapping).forEach(e -> e.forEach(e2 ->onlineApiName.add(e2.getApiName())));
        while (count < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            for (ObjectData objectData : objectDataList) {
                ObjectData forUpdate = new ObjectData();
                forUpdate.put("_id", objectData.getId());
                String eventType = objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName());
                String eventForm = null;
                if ("3".equals(eventType)) {
                    eventForm = "conference_marketing";
                } else if ("live_marketing".equals(eventType)) {
                    eventForm = "live_marketing";
                } else if ("multivenue_marketing".equals(eventType)) {
                    eventForm = "multivenue_marketing";
                } else if ("once".equals(eventType)) {
                    eventForm = "once";
                } else if ("periodicity".equals(eventType)) {
                    eventForm = "periodicity";
                } else if ("advertising_marketing".equals(eventType)) {
                    eventForm = "advertise";
                }  else if (onlineApiName.contains(eventType)) {
                    eventForm = "online_marketing";
                }
                if (StringUtils.isBlank(eventForm) || eventForm.equals(objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()))) {
                    continue;
                }
                forUpdate.put(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName(), eventForm);
                crmV2Manager.editWithNotValidateParam(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), forUpdate, false, false, true);
            }
            count += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
        }
    }


    public Map<String, String> getMarketingEventTypeLabel(String ea) {
        Map<String, String> resultMap = Maps.newHashMap();
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("市场活动描述不存在 ea:{}", ea);
                return resultMap;
            }
            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            Map<String, FieldDescribe> fieldMap = objectDescribe.getFields();
            FieldDescribe fieldDescribe = fieldMap.get("event_type");
            if (fieldDescribe != null) {
                fieldDescribe.getOptions().forEach(e -> {
                    resultMap.put(e.get("value").toString(), e.get("label").toString());
                });
            }
        } catch (Exception e) {
            log.error("getMarketingEventTypeLabel error, ea: {}", ea, e);
        }
        return resultMap;
    }
}
