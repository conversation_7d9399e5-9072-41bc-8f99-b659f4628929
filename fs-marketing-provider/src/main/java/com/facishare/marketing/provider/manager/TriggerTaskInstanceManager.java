/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.arg.AccountIsApplyForKISArg;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingObjectResult;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.AddBoardCardArg;
import com.facishare.marketing.api.arg.OfficeMessageArg;
import com.facishare.marketing.api.arg.sms.GroupSenderArg;
import com.facishare.marketing.api.arg.sms.SmsVarArg;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult;
import com.facishare.marketing.api.result.sms.GroupSendResult;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.SmsRecordNodeTypeEnum;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.conference.ConferenceParamEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LiveParamEnum;
import com.facishare.marketing.common.enums.mail.*;
import com.facishare.marketing.common.enums.qywx.QywxAttachmentScenesTypeEnum;
import com.facishare.marketing.common.enums.qywx.QywxGroupSendRangeTypeEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SaveOrSendTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsGroupTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.result.MaterialWxPresentMsg;
import com.facishare.marketing.outapi.service.MaterialService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.mail.MailSendReplyDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskResultDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendGroupResultDAO;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dto.EnvContext;
import com.facishare.marketing.provider.dto.MarketingUserWithEmail;
import com.facishare.marketing.provider.dto.UserContext;
import com.facishare.marketing.provider.dto.WxNewsMessageContent;
import com.facishare.marketing.provider.dto.conference.ConferenceEnrollBaseInfoDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupResultEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.CreateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.innerArg.mail.SendEmailArg;
import com.facishare.marketing.provider.innerArg.qywx.AddNewMsgTemplateArg;
import com.facishare.marketing.provider.innerArg.qywx.QywxAgentMessageArg;
import com.facishare.marketing.provider.innerArg.qywx.SendWelcomeMessageNewArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.innerResult.mail.SendEmailResult;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsSettingManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.paas.metadata.common.MetadataConstant;
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatMessageRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.WxCustomerServiceMsgType;
import com.facishare.wechat.proxy.model.vo.BatchTemplateMessageVo;
import com.facishare.wechat.proxy.model.vo.TemplateMessageVo;
import com.facishare.wechat.proxy.service.WechatMessageService;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.enums.TriggerTaskTypeEnum.*;

@Slf4j
@Component
public class TriggerTaskInstanceManager {

    @Autowired
    private SceneTriggerDao sceneTriggerDao;
    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;
    @Autowired
    private TriggerInstanceDao triggerInstanceDao;
    @Autowired
    private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private SendService sendService;
    @Autowired
    private MaterialService materialService;
    @Value("${qywx.group.message.default.cover}")
    private String groupMessageDefaultCoverPath;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private MwSendManager mwSendManager;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Autowired
    private MarketingTriggerDao marketingTriggerDao;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @ReloadableProperty("host")
    private String host;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private WebHookManager webHookManager;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private EmployeeMsgSender employeeMsgSender;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private SceneTriggerManager sceneTriggerManager;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private MailSendReplyDAO mailSendReplyDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private WechatMessageRestService wechatMessageRestService;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MomentManager momentManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private CustomerGroupManager customerGroupManager;
    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;
    @Autowired
    private SmsTemplateManager smsTemplateManager;
    @Autowired
    private AccountService accountService;
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private QywxAttachmentsRelationDAO qywxAttachmentsRelationDAO;
    @Autowired
    private QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;
    @Autowired
    private EmailSendRecordDetailObjManager emailSendRecordDetailObjManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private OutLinkMktParamManager outLinkMktParamManager;
    @Autowired
    private MailSendTaskResultDAO mailSendTaskResultDAO;
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;

    // todo 相关线程池 -> MQ

    /**
     * 触发监听中的sop任务(即判断节点)
     *
     * @param ea
     * @param marketingUserId
     * @param paramMap
     * @return
     */
    public void startTaskInstanceByMarketingUserId(String ea, String marketingUserId, Map<String, Object> paramMap) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(marketingUserId)) {
            return;
        }
        List<TriggerTaskInstanceEntity> taskInstanceList = triggerTaskInstanceDao.listTaskInstanceByUserMarketingId(ea, marketingUserId);
        taskInstanceList.forEach(e -> {
            try {
                finishTriggerTask(ea, e.getId(), paramMap);
            } catch (Exception exception) {
                log.warn("triggerTaskInstance invoke fail, ea:{} triggerTaskInstanceId:{}", ea, e.getId(), exception);
            }
        });
    }

    /**
     * 定时任务执行sop任务
     *
     * @param triggerTaskInstanceId
     * @return
     */
    public boolean finishTriggerTask(String triggerTaskInstanceId){
        TriggerTaskInstanceEntity triggerTaskInstance = triggerTaskInstanceDao.getById(triggerTaskInstanceId);
        if (triggerTaskInstance == null){
            return false;
        }

        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(triggerTaskInstance.getEa()) || appVersionManager.getCurrentAppVersion(triggerTaskInstance.getEa()) == null){
            log.info("TriggerInstanceManager.finishTriggerTask failed enterprise stop or license expire ea:{}", triggerTaskInstance.getEa());
            return false;
        }

        if (!Lists.newArrayList(ExecuteStatusEnum.TODO.getExecuteStatus(),ExecuteStatusEnum.LISTENING.getExecuteStatus()).contains(triggerTaskInstance.getExecuteStatus())){
            return false;
        }
        // 如果触发器已停止, 在这将延迟任务更新为取消
        try {
            TriggerInstanceEntity triggerInstanceEntity = triggerInstanceDao.getById(triggerTaskInstance.getTriggerInstanceId());
            SceneTriggerEntity sceneTrigger = sceneTriggerDao.getByTriggerIdAndSceneInfo(triggerInstanceEntity.getEa(),
                    triggerInstanceEntity.getSceneType(), triggerInstanceEntity.getSceneId(), triggerInstanceEntity.getSceneTargetId(), triggerInstanceEntity.getTriggerId());
            TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getById(triggerInstanceEntity.getEa(), triggerInstanceEntity.getTriggerSnapshotId());
            if (!SceneTriggerLifeStatus.ENABLED.getLifeStatus().equals(sceneTrigger.getLifeStatus())
                    || !TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(triggerSnapshot.getSnapshotStatus())) {
                triggerTaskInstanceDao.cancelDelayTask(triggerInstanceEntity.getEa(), triggerTaskInstanceId);
                return false;
            }
            if(EffectiveTimeType.RANGE.getType().equals(sceneTrigger.getEffectiveTimeType()) && (sceneTrigger.getStartEffectiveTime() > System.currentTimeMillis() || sceneTrigger.getEndEffectiveTime() < System.currentTimeMillis())){
                triggerTaskInstanceDao.cancelDelayTask(triggerInstanceEntity.getEa(), triggerTaskInstanceId);
                return false;
            }
        } catch (Exception e) {}

        return this.finishTriggerTask(triggerTaskInstance.getEa(), triggerTaskInstance.getId());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class ExecuteResult implements Serializable {
        private boolean success = true;
        private String result;

        public static ExecuteResult successResult() {
            return new ExecuteResult();
        }

        public static ExecuteResult successResult(String result) {
            return new ExecuteResult(true, result);
        }

        public static ExecuteResult failResult(String result) {
            return new ExecuteResult(false, result);
        }
    }

    /**
     * 不包含监听节点使用
     *
     * @param ea
     * @param triggerTaskInstanceId
     * @return
     */
    public boolean finishTriggerTask(String ea, String triggerTaskInstanceId) {
        return this.finishTriggerTask(ea, triggerTaskInstanceId, null);
    }

    /**
     * 包含监听节点使用, paramMap应是触发时传入
     *
     * @param ea
     * @param triggerTaskInstanceId
     * @param paramMap:
     *                targetObjectType 触发物料类型
     *                targetObjectId: 触发物料id
     *                spreadFsUid: 推广人
     * @return
     */
    public boolean finishTriggerTask(String ea, String triggerTaskInstanceId, Map<String, Object> paramMap) {
        TriggerTaskInstanceEntity triggerTaskInstance = triggerTaskInstanceDao.getById(triggerTaskInstanceId);
        // 如果触发器已停止, 在这将延迟任务更新为取消
        try {
            TriggerInstanceEntity triggerInstanceEntity = triggerInstanceDao.getById(triggerTaskInstance.getTriggerInstanceId());
            SceneTriggerEntity sceneTrigger = sceneTriggerDao.getByTriggerIdAndSceneInfo(triggerInstanceEntity.getEa(),
                    triggerInstanceEntity.getSceneType(), triggerInstanceEntity.getSceneId(), triggerInstanceEntity.getSceneTargetId(), triggerInstanceEntity.getTriggerId());
            TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getById(triggerInstanceEntity.getEa(), triggerInstanceEntity.getTriggerSnapshotId());
            if (!SceneTriggerLifeStatus.ENABLED.getLifeStatus().equals(sceneTrigger.getLifeStatus())
                    || !TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(triggerSnapshot.getSnapshotStatus())) {
                return false;
            }
            if(EffectiveTimeType.RANGE.getType().equals(sceneTrigger.getEffectiveTimeType()) && (sceneTrigger.getStartEffectiveTime() > System.currentTimeMillis() || sceneTrigger.getEndEffectiveTime() < System.currentTimeMillis())){
                return false;
            }
        } catch (Exception e) {}
        // 更新为执行中
        int updated = 0;
        updated = triggerTaskInstanceDao.updateExecuteStatus(ea, triggerTaskInstanceId, ExecuteStatusEnum.EXECUTING.getExecuteStatus(), ExecuteStatusEnum.TODO.getExecuteStatus());
        if (updated == 0) {
            updated = triggerTaskInstanceDao.updateExecuteStatus(ea, triggerTaskInstanceId, ExecuteStatusEnum.LISTEN_EXECUTING.getExecuteStatus(), ExecuteStatusEnum.LISTENING.getExecuteStatus());
        }
        if (updated == 0) {
            return false;
        }
        if (paramMap == null && StringUtils.isNotEmpty(triggerTaskInstance.getParamMap())) {
            paramMap = JSONObject.parseObject(triggerTaskInstance.getParamMap());
        }
        triggerTaskInstance = triggerTaskInstanceDao.getById(triggerTaskInstanceId);
        TriggerInstanceEntity triggerInstance = triggerInstanceDao.getById(triggerTaskInstance.getTriggerInstanceId());
        TriggerTaskSnapshotEntity triggerTaskSnapshot = triggerTaskSnapshotDao.getById(triggerTaskInstance.getTriggerTaskSnapshotId());
        ExecuteResult executeResult = ExecuteResult.successResult();
        TriggerTaskSnapshotEntity nextNode = null;
        try {
            switch (TriggerTaskTypeEnum.getByTriggerTaskType(triggerTaskSnapshot.getTaskType())) {
                case ADD_TAG: {
                    executeResult = addTag(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_SMS_MSG: {
                    executeResult = sendSmsMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_WX_GRAPHIC_MESSAGE:
                case SEND_WX_TEXT_MSG:
                case SEND_WX_IMAGE_MSG:
                case SEND_WX_NEWS_MSG: {
                    executeResult = sendWxMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_WX_TEMPLATE_MSG: {
                    executeResult = sendWxTemplateMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_EMAIL_MSG: {
                    executeResult = sendEmailMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case ADD_BOARD: {
                    executeResult = addBoard(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case INVOKE_WEB_HOOK: {
                    executeResult = invokeWebHook(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_UNION_MSG: {
                    executeResult = sendUnionMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_WORK_WX_MSG: {
                    executeResult = sendWorkWxMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case SEND_WORK_WX_SOP: {
                    executeResult = sendWorkWxSop(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance);
                    break;
                }
                case BRANCH_JUDGMENT:
                    nextNode = branchJudgment(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance, paramMap);
                    FlexibleJson properties = triggerTaskSnapshot.getProperty();
                    JSONObject property = JSONObject.parseObject(JSONObject.toJSONString(properties));
                    Boolean enableOther = property.getBoolean("enableOther");
                    long nowTime = new Date().getTime();
                    Long waittingEndTime = property.getLong("waitingTime") * 60 * 1000 + nowTime;
                    // 没有匹配到下一节点, 先更改为监听中, 跳过
                    if (nextNode == null && BooleanUtils.isTrue(enableOther) && ExecuteStatusEnum.EXECUTING.getExecuteStatus().equals(triggerTaskInstance.getExecuteStatus())) {
                        // 状态 executing -> listening 并设置等待结束时间 若开启其他节点有等待时间, 等待结束后直接执行[其他]分支
                        log.warn("branchJudgment fail, into listening. marketingUserId:{} triggerTaskInstanceId:{}", triggerInstance.getMarketingUserId(), triggerTaskInstance.getId());
                        triggerTaskInstanceDao.updateExecuteTime(ea, triggerTaskInstance.getId(), waittingEndTime);
                        triggerTaskInstanceDao.updateExecuteResult(ea, triggerTaskInstance.getId(), ExecuteStatusEnum.LISTENING.getExecuteStatus(), null);
                        return false;
                    }
                    // 没有匹配到下一节点, 且还在监听中, 跳过, 继续监听
                    if (nextNode == null && BooleanUtils.isTrue(enableOther) && triggerTaskInstance.getExecuteTime() > nowTime) {
                        log.warn("branchJudgment fail, continue. marketingUserId:{} triggerTaskInstanceId:{}", triggerInstance.getMarketingUserId(), triggerTaskInstance.getId());
                        triggerTaskInstanceDao.updateExecuteResult(ea, triggerTaskInstance.getId(), ExecuteStatusEnum.LISTENING.getExecuteStatus(), null);
                        return false;
                    }
                    // 没有匹配到下一节点, 且结束监听, 执行其他
                    if (nextNode == null && BooleanUtils.isTrue(enableOther) && triggerTaskInstance.getExecuteTime() < nowTime) {
                        log.warn("branchJudgment fail, invoke other, marketingUserId:{} triggerTaskInstanceId:{}", triggerInstance.getMarketingUserId(), triggerTaskInstance.getId());
                        Integer nextSerialNumber = property.getInteger("otherSerialNumber");
                        nextNode = triggerTaskSnapshotDao.getNextTriggerTaskSnapshot(ea, triggerTaskSnapshot.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId(), nextSerialNumber);
                        break;
                    }
                    // 没有匹配到下一节点, 以上情况都不是, 返回错误
                    if (nextNode == null) {
                        executeResult = ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_471));
                        break;
                    }

                    executeResult = ExecuteResult.successResult();
            }
        } catch (Exception e) {
            log.warn("finishTriggerTask e:", e);
            executeResult = ExecuteResult.failResult(e.getMessage());
        }
        if (executeResult.isSuccess()) {
            triggerTaskInstanceDao.updateExecuteResult(ea, triggerTaskInstance.getId(), ExecuteStatusEnum.SUCCESS.getExecuteStatus(), executeResult.getResult());
            triggerInstanceDao.incrementSuccessTaskCount(ea, triggerInstance.getId());
        } else {
            triggerTaskInstanceDao.updateExecuteResult(ea, triggerTaskInstance.getId(), ExecuteStatusEnum.FAILED.getExecuteStatus(), executeResult.getResult());
            triggerInstanceDao.incrementFailTaskCount(ea, triggerInstance.getId());
        }
        if (paramMap != null && paramMap.get("isQywxTiming") != null && BooleanUtils.isTrue((Boolean) paramMap.get("isQywxTiming"))) {
            return executeResult.isSuccess();
        }
        // 非判断节点直接查询下一节点(需兼容无preSerialNumber的旧数据)
        if (TriggerTaskTypeEnum.getByTriggerTaskType(triggerTaskSnapshot.getTaskType()) != BRANCH_JUDGMENT) {
            nextNode = triggerTaskSnapshotDao.findNextNode(ea, triggerTaskSnapshot.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId(), triggerTaskSnapshot.getSerialNumber());
        }
        if (nextNode != null) {
            log.warn("continue nextNode, marketingUserId:{} triggerTaskInstanceId:{}", triggerInstance.getMarketingUserId(), triggerTaskInstance.getId());
            Runnable nextTask = buildAndInvokeNextNodeTask(ea, triggerInstance, nextNode, paramMap);
            ThreadPoolUtils.execute(nextTask, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
        return executeResult.isSuccess();
    }

    /**
     * 构建下个节点任务
     *
     * @param ea
     * @param paramMap
     * @param triggerInstance
     * @param nextNode
     * @return
     */
    public Runnable buildAndInvokeNextNodeTask(String ea, TriggerInstanceEntity triggerInstance, TriggerTaskSnapshotEntity nextNode, Map<String, Object> paramMap) {
        return () -> invokeNextNodeTask(ea, triggerInstance, nextNode, paramMap);
    }

    public void invokeNextNodeTask(String ea, TriggerInstanceEntity triggerInstance, TriggerTaskSnapshotEntity nextNode, Map<String, Object> paramMap) {
        TriggerSnapshotEntity triggerSnapshotEntity = triggerSnapshotDao.getById(ea, triggerInstance.getTriggerSnapshotId());
        TriggerTaskInstanceEntity triggerTaskInstanceEntity = buildTriggerTaskInstance(ea, paramMap, triggerSnapshotEntity, triggerInstance.getId(), nextNode);
        triggerTaskInstanceDao.batchInsert(Collections.singleton(triggerTaskInstanceEntity));
        if (ExecuteTypeEnum.IMMEDIATELY.getExecuteType().equals(triggerTaskInstanceEntity.getExecuteType())) {
            log.warn("invoke next, marketingUserId:{} triggerTaskInstanceId:{}", triggerInstance.getMarketingUserId(), triggerTaskInstanceEntity.getId());
            finishTriggerTask(ea, triggerTaskInstanceEntity.getId());
        }
    }

    /**
     * 分支判断:
     * 主要根据paramMap做匹配
     * 1. 匹配到下一节点, 直接返回
     * 2. 未匹配到下一节点, 返回null
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public TriggerTaskSnapshotEntity branchJudgment(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance, Map<String, Object> paramMap) {
        TriggerTaskSnapshotEntity nextNode = null;
        try {
            JSONObject property = JSONObject.parseObject(JSONObject.toJSONString(triggerTaskSnapshot.getProperty()));
            Integer nextSerialNumber = null;
            JSONArray branchList = property.getJSONArray("branchList");
            for (int i = 0; i < branchList.size(); i++) {
                JSONObject branch = branchList.getJSONObject(i);
                String branchType = property.getString("branchType");
                boolean hit = branchConditionsMatch(triggerTaskInstance, triggerInstance, branchType, branch, paramMap);;
                // 直接取第一个符合条件的分支
                if (hit) {
                    nextSerialNumber = branch.getInteger("nextSerialNumber");
                    break;
                }
            }
            if (nextSerialNumber != null) {
                nextNode = triggerTaskSnapshotDao.getNextTriggerTaskSnapshot(ea, triggerTaskSnapshot.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId(), nextSerialNumber);
            }
        } catch (Exception e) {
            log.warn("branchJudgment error triggerTaskInstanceId:{} paramMap:{}", triggerTaskInstance.getId(), paramMap, e);
        }
        log.warn("branchJudgment ea:{}, nextNode:{}", ea, nextNode);
        return nextNode;
    }

    /**
     * 分支条件匹配
     *
     * @param triggerTaskInstance
     * @param triggerInstance
     * @param branchType          分支类型
     * @param branch              分支设置
     * @param paramMap            实例触发参数
     * @return hit true:命中  false:不符合
     */
    public boolean branchConditionsMatch(TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance, String branchType, JSONObject branch, Map<String, Object> paramMap) {
        String ea = triggerTaskInstance.getEa();
        String marketingUserId = triggerInstance.getMarketingUserId();
        boolean hit = false;
        JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(paramMap));
        switch (branchType) {
            // 基于表单值
            case "BASE_SUBMIT_FORM":
                if (params == null) {
                    return false;
                }
                String enrollId = params.getString("enrollId");
                CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(enrollId);
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId());
                Map<String, String> picMap = customizeFormDataManager.conversionEnrollDataPic(ea, Collections.singleton(customizeFormDataUserEntity));
                customizeFormDataManager.buildAreaInfoByEnrollData(Collections.singletonList(customizeFormDataUserEntity));
                Map<String, Object> submitMap = customizeFormDataManager.generateEnrollData(customizeFormDataUserEntity, customizeFormDataEntity.getFormBodySetting(), picMap, true);
                // 遍历条件, [且]关系连接, 但凡一个不符合直接下一个分支
                JSONArray conditions = branch.getJSONArray("conditions");
                for (int j = 0; j < conditions.size(); j++) {
                    JSONObject condition = conditions.getJSONObject(j);
                    if ("EQ".equals(condition.get("operator"))) {
                        if (submitMap.get(condition.getString("key")) instanceof List) {
                            // 数组
                            hit = ListUtils.isEqualList((List<String>) condition.get("value"), (List<String>) submitMap.get(condition.getString("key")));
                        } else {
                            // 字符串或数字
                            hit = Objects.equals(condition.get("value"), submitMap.get(condition.getString("key")));
                        }
                    } else if ("CONTAINS".equals(condition.get("operator"))) {
                        if (submitMap.get(condition.getString("key")) instanceof List) {
                            // 数组
                            hit = ((List<?>) submitMap.get(condition.getString("key"))).containsAll((List<?>) condition.get("value"));
                        } else if (submitMap.get(condition.getString("key")) instanceof String) {
                            // 字符串
                            hit = StringUtils.contains((String) submitMap.get(condition.getString("key")), (String) condition.get("value"));
                        }
                    }
                    if (!hit) {
                        log.warn("branchConditionsMatch ea:{}, triggerTaskInstanceId:{}, triggerInstanceId:{}, branch:{}, submitMap:{}", ea, triggerTaskInstance.getId(), triggerInstance.getId(), branch, submitMap);
                        break;
                    }
                }
                break;
            // 基于对象值
            case "BASE_OBJECT_VALUE":
                String objectApiName = branch.getString("objectApiName");
                Integer channelType = null;
                if (ChannelEnum.getByApiName(objectApiName) == null) {
                    channelType = ChannelEnum.CUSTOMIZE_OBJECT.getType();
                } else {
                    channelType = ChannelEnum.getByApiName(objectApiName).getType();
                }
                List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, channelType, Collections.singletonList(triggerInstance.getMarketingUserId()));
                if (CollectionUtils.isEmpty(dataIds)) {
                    break;
                }
                RuleGroupList ruleGroups = GsonUtil.fromJson(branch.getJSONArray("conditions").toJSONString(), new TypeToken<RuleGroupList>(){}.getType());
                List<PaasQueryFilterArg> paasQueryFilterArgs = ruleGroups.stream().map(val -> BeanUtil.copyByGson(val, PaasQueryFilterArg.class)).collect(Collectors.toList());
                for (PaasQueryFilterArg paasQueryFilterArg : paasQueryFilterArgs) {
                    paasQueryFilterArg.getQuery().addFilter("_id", OperatorConstants.IN, dataIds);
                    hit = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg) > 0;
                    if (hit) {
                        break;
                    }
                }
                break;
            // 基于标签
            case "BASE_TAG":
                String tagOperator = branch.getString("tagOperator");
                List<TagName> tagNames = branch.getJSONArray("tagNames").toJavaList(TagName.class);
                Boolean excludeTags = branch.getBoolean("excludeTags");
                List<TagName> excludeTagNames = branch.getJSONArray("excludeTagNames").toJavaList(TagName.class);
                boolean tagOperatorValid = "IN".equals(tagOperator) || "LIKE".equals(tagOperator) || "HASANYOF".equals(tagOperator);
                if (tagOperatorValid && tagNames != null && !tagNames.isEmpty() || excludeTags && excludeTagNames != null && !excludeTagNames.isEmpty()) {
                    Map<String, List<TagName>> marketingUserToTagNameMap = userMarketingAccountManager
                            .listTagNameListByUserMarketingAccountIds(ea, ChannelEnum.getAllChannelApiName(), Lists.newArrayList(marketingUserId));
                    if (tagOperatorValid && tagNames != null && !tagNames.isEmpty()) {
                        // 标签为空
                        if (marketingUserToTagNameMap == null || marketingUserToTagNameMap.get(marketingUserId) == null || marketingUserToTagNameMap.get(marketingUserId).isEmpty()) {
                            return false;
                        }
                        // 包含以下所有标签
                        if (("IN".equals(tagOperator) || "LIKE".equals(tagOperator)) && marketingUserToTagNameMap.get(marketingUserId).containsAll(tagNames)) {
                            hit = true;
                        }
                        // 包含以下任意标签
                        if ("HASANYOF".equals(tagOperator) && marketingUserToTagNameMap.get(marketingUserId).stream().anyMatch(tagName -> tagNames.contains(tagName))) {
                            hit = true;
                        }
                    }
                    // 且不包含以下任意标签
                    if (excludeTags && excludeTagNames != null && !excludeTagNames.isEmpty() && marketingUserToTagNameMap != null && marketingUserToTagNameMap.get(marketingUserId) != null) {
                        hit = !marketingUserToTagNameMap.get(marketingUserId).stream().anyMatch(tagName -> excludeTagNames.contains(tagName));
                    }
                }
                break;
            // 基于动作结果
            case "BASE_ACTION_RESULT":
                if (params.getString("actionType").equals(branch.getString("actionType"))
                        && params.getString("actionStatus").equals(branch.getString("actionStatus"))) {
                    hit = true;
                }
                break;
            // 基于用户行为
            case "BASE_USER_BEHAVIOR":
                if (branch.getInteger("actionType").equals(params.getInteger("actionType"))) {
                    // 任意内容
                    if ("all".equals(branch.getString("materialScope"))) {
                        hit = true;
                        break;
                    }
                    // 内容分组
                    if ("group".equals(branch.getString("materialScope")) && branch.getString("objectGroupId") != null) {
                        ObjectGroupRelationEntity relation = objectGroupRelationDAO.getByObjectId(ea, params.getString("objectId"));
                        if (relation != null && relation.getGroupId().equals(branch.getString("objectGroupId"))) {
                            hit = true;
                            break;
                        }
                    }
                    // 邮件主题包含
                    if ("specify".equals(branch.getString("materialScope")) && branch.getJSONArray("emailSubject") != null) {
                        for (int i = 0; i < branch.getJSONArray("emailSubject").size(); i++) {
                            String keyWord = branch.getJSONArray("emailSubject").getString(i);
                            if (params.getString("emailSubject") != null && params.getString("emailSubject").contains(keyWord)) {
                                hit = true;
                                break;
                            }
                        }
                    }
                    // 指定内容
                    if ("specify".equals(branch.getString("materialScope")) && branch.getJSONArray("objectList") != null) {
                        JSONArray objectList = branch.getJSONArray("objectList");
                        for (int i = 0; i < objectList.size(); i++) {
                            JSONObject object = objectList.getJSONObject(i);
                            if (branch.getInteger("actionType").equals(MarketingUserActionType.CLICK_WX_SERVICE_MENU.getActionType())
                                    && branch.getString("wxAppId").equals(params.getString("wxAppId"))
                                    && object.getInteger("objectType").equals(params.getInteger("objectType")) && object.getString("objectId").equals(params.getString("objectId"))) {
                                hit = true;
                                break;
                            } else if (object.getInteger("objectType").equals(params.getInteger("objectType")) && object.getString("objectId").equals(params.getString("objectId"))) {
                                hit = true;
                                break;
                            }
                        }
                        break;
                    }
                }
                break;
            default:
                throw new IllegalArgumentException("Not support.");
        }
        return hit;
    }

    /**
     * 构建SOP任务实例
     *
     * @param ea
     * @param paramMap
     * @param triggerSnapshot
     * @param triggerInstanceId
     * @param triggerTaskSnapshot
     * @return
     */
    public TriggerTaskInstanceEntity buildTriggerTaskInstance(String ea, Map<String, Object> paramMap, TriggerSnapshotEntity triggerSnapshot, String triggerInstanceId, TriggerTaskSnapshotEntity triggerTaskSnapshot) {
        TriggerInstanceEntity triggerInstance = triggerInstanceDao.getById(triggerInstanceId);
        TriggerTaskInstanceEntity triggerTaskInstanceToAdd = new TriggerTaskInstanceEntity();
        triggerTaskInstanceToAdd.setId(UUIDUtil.getUUID());
        triggerTaskInstanceToAdd.setEa(ea);
        triggerTaskInstanceToAdd.setTriggerId(triggerSnapshot.getTriggerId());
        triggerTaskInstanceToAdd.setTriggerSnapshotId(triggerSnapshot.getId());
        triggerTaskInstanceToAdd.setTriggerInstanceId(triggerInstanceId);
        triggerTaskInstanceToAdd.setTriggerTaskSnapshotId(triggerTaskSnapshot.getId());
        triggerTaskInstanceToAdd.setExecuteType(triggerTaskSnapshot.getExecuteType());
        triggerTaskInstanceToAdd.setExecuteStatus(ExecuteStatusEnum.TODO.getExecuteStatus());
        if (paramMap != null) {
            triggerTaskInstanceToAdd.setParamMap(GsonUtil.toJson(paramMap));
        }
        triggerTaskInstanceToAdd.setCreateTime(new Date());
        triggerTaskInstanceToAdd.setUpdateTime(new Date());
        if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType()) || TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType())) {
            triggerTaskInstanceToAdd.setBatchId(Integer.valueOf(DateUtil.format(triggerInstance.getCreateTime(), "yyyyMMdd")));
        }
        if (ExecuteTypeEnum.DELAYED.getExecuteType().equals(triggerTaskSnapshot.getExecuteType())){
            long delayTime = new DateTime().plusMinutes(triggerTaskSnapshot.getExecuteDelayMinutes()).getMillis();
            triggerTaskInstanceToAdd.setExecuteTime(delayTime);
            if (null != triggerTaskSnapshot.getTaskOffsetDay() && null != triggerTaskSnapshot.getTaskOffsetMinute()) {
                triggerTaskInstanceToAdd.setTaskEnd(DateUtil.getExactTime(new Date(delayTime), triggerTaskSnapshot.getTaskOffsetDay(), triggerTaskSnapshot.getTaskOffsetMinute()));
            }
        }else{
            triggerTaskInstanceToAdd.setExecuteTime(new DateTime().getMillis());
            if (null != triggerTaskSnapshot.getTaskOffsetDay() && null != triggerTaskSnapshot.getTaskOffsetMinute()) {
                triggerTaskInstanceToAdd.setTaskEnd(DateUtil.getExactTime(new Date(), triggerTaskSnapshot.getTaskOffsetDay(), triggerTaskSnapshot.getTaskOffsetMinute()));
            }
        }
        return triggerTaskInstanceToAdd;
    }

    /**
     *  发送企微sop任务
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendWorkWxSop(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getById(ea, triggerTaskSnapshot.getTriggerSnapshotId());
        // 客户群SOP
        if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
            String groupSender = triggerInstance.getMarketingUserId();
            if (StringUtils.isBlank(groupSender)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_781));
            }
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (null == agentConfig) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_785));
            }
            List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), ea);
            QywxCustomerAppInfoEntity authAppInfo = null;
            if (!CollectionUtil.isEmpty(qywxCustomerAppInfoEntities)) {
                authAppInfo = qywxCustomerAppInfoEntities.get(0);
            } else {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_792));
            }
            String url = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-task/pages/detail/sop-task?corpId=" + agentConfig.getCorpid() + "&suitId=" + authAppInfo.getSuitId() + "&triggerTaskInstanceId=" + triggerTaskInstance.getId();
            QywxAgentMessageArg arg = new QywxAgentMessageArg();
            arg.setMsgtype("textcard");
            QywxAgentMessageArg.TextCard text = new QywxAgentMessageArg.TextCard();
            text.setTitle(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_798));
            text.setDescription(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_799));
            text.setUrl(url);
            arg.setAgentid(Integer.valueOf(authAppInfo.getAgentId()));
            arg.setTextCard(text);
            arg.setTouser(groupSender);
            SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = qywxManager.sendAgentMessage(momentManager.getOrCreateAccessToken(ea), arg);
            if (null == spreadQywxMiniappMessageResult || 0 != spreadQywxMiniappMessageResult.getErrcode()) {
                return ExecuteResult.failResult(Objects.requireNonNull(spreadQywxMiniappMessageResult).getErrmsg());
            }
            // 员工执行情况查询使用 更新group_sender和to_user to_size
            CustomerGroupListResult customerGroupListResult = customerGroupManager.queryCustomerListNew(ea, ImmutableList.of(groupSender), 1000);
            if (null != customerGroupListResult && 0 == customerGroupListResult.getErrcode()) {
                //这里暂时只发状态是0的
                List<String> groupIdList = customerGroupListResult.getGroupList().stream().filter(r -> r.getStatus() == 0).map(r -> r.getGroupId()).collect(Collectors.toList());
                triggerTaskInstance.setGroupSender(groupSender);
                triggerTaskInstance.setToUser(GsonUtil.toJson(groupIdList));
                triggerTaskInstance.setToSize(groupIdList.size());
            }
            triggerTaskInstanceDao.updateSubsidiaryData(triggerTaskInstance);
        }

        // 客户SOP
        if (triggerSnapshot.getSendRange() != QywxGroupSendRangeTypeEnum.GROUP.getType()) {
            Integer owner = null;
            List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Collections.singletonList(triggerInstance.getMarketingUserId()));
            if (null != triggerTaskSnapshot.getSendSopSetting() && entities != null && !entities.isEmpty()) {
                String externalUserId = entities.get(0).getWxWorkExternalUserId();
                if (StringUtils.isNotEmpty(externalUserId)) {
                    owner = this.getOwnerByRules(ea, triggerTaskSnapshot.getSendSopSetting(), externalUserId);
                }
            }
            if (null == owner) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_831));
            }
            String ownerUserId = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(ea, owner);

            // 根据激活状态过滤
            if (qywxActivatedAccountManager.shouldFiltering(ea) && !qywxActivatedAccountManager.getActivatedUserIds(ea).contains(ownerUserId)) {
                ownerUserId = null;
            }
            if (StringUtils.isBlank(ownerUserId)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_831));
            }

            //已经发过该负责人 这里只有定时和周期时间可以查到
            List<String> executeStatus = triggerTaskInstanceDao.hasNoticeOwner(ea, triggerTaskInstance.getTriggerId(), triggerTaskInstance.getTriggerSnapshotId(), triggerTaskInstance.getTriggerTaskSnapshotId(), triggerTaskInstance.getBatchId(), triggerTaskInstance.getOwner());
            if (CollectionUtils.isNotEmpty(executeStatus)) {
                if (executeStatus.contains("success")) {
                    return ExecuteResult.successResult("success-repeat");
                } else {
                    return ExecuteResult.failResult("fail-repeat");
                }
            }

            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig == null) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_785));
            }
            List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), ea);
            QywxCustomerAppInfoEntity authAppInfo = null;
            if (CollectionUtil.isEmpty(qywxCustomerAppInfoEntities)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_854));
            } else {
                authAppInfo = qywxCustomerAppInfoEntities.get(0);
            }
            String url = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-task/pages/detail/sop-task?corpId=" + agentConfig.getCorpid() + "&suitId=" + authAppInfo.getSuitId() + "&triggerTaskInstanceId=" + triggerTaskInstance.getId();
            QywxAgentMessageArg arg = new QywxAgentMessageArg();
            arg.setMsgtype("textcard");
            QywxAgentMessageArg.TextCard text = new QywxAgentMessageArg.TextCard();
            text.setTitle(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_798));
            text.setDescription(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_799));
            text.setUrl(url);
            arg.setAgentid(Integer.valueOf(authAppInfo.getAgentId()));
            arg.setTextCard(text);
            arg.setTouser(ownerUserId);
            SpreadQywxMiniappMessageResult spreadQywxMiniappMessageResult = qywxManager.sendAgentMessage(momentManager.getOrCreateAccessToken(ea), arg);
            if (null == spreadQywxMiniappMessageResult || 0 != spreadQywxMiniappMessageResult.getErrcode()) {
                return ExecuteResult.failResult(Objects.requireNonNull(spreadQywxMiniappMessageResult).getErrmsg());
            }
            // 员工执行情况查询使用 更新owner owner_user_id和to_user to_size
            Map<Integer, List<String>> ownerToMarketingUserIdMap = new HashMap<>();
            getMarketingAccountUserIds(ea, triggerSnapshot, ownerToMarketingUserIdMap);
            triggerTaskInstance.setOwner(owner);
            triggerTaskInstance.setOwnerUserId(ownerUserId);
            if (null != ownerToMarketingUserIdMap && ownerToMarketingUserIdMap.containsKey(owner)) {
                triggerTaskInstance.setToUser(GsonUtil.toJson(ownerToMarketingUserIdMap.get(owner)));
                triggerTaskInstance.setToSize(ownerToMarketingUserIdMap.get(owner).size());
            }
            triggerTaskInstanceDao.updateSubsidiaryData(triggerTaskInstance);

        }

        return ExecuteResult.successResult();
    }

    /**
     * 发送企微消息
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendWorkWxMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        Map<String, Object> paramMap = null;
        if (StringUtils.isNotEmpty(triggerTaskInstance.getParamMap())) {
            paramMap = JSONObject.parseObject(triggerTaskInstance.getParamMap());
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(triggerTaskSnapshot.getEa());
        if (agentConfig == null) {
            log.warn("TriggerInstanceManager finishQywxTriggerTask agentConfig is null, triggerTask={}", triggerTaskSnapshot);
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_785));
        }
        List<String> extenalUserIds = Lists.newArrayList();
        TriggerSnapshotEntity triggerSnapshotEntity = triggerSnapshotDao.getCurrentUseSnapshot(ea, triggerInstance.getTriggerId());
        boolean isGroupSend = triggerSnapshotEntity.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType();
        String groupSender = null;
        if (isGroupSend) {
            groupSender = triggerInstance.getMarketingUserId();
            // 根据激活状态过滤
            if (qywxActivatedAccountManager.shouldFiltering(ea) && !qywxActivatedAccountManager.getActivatedUserIds(ea).contains(groupSender)) {
                groupSender = null;
            }
            if (StringUtils.isBlank(groupSender)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_781));
            }
            //群发
            List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
            if (CollectionUtils.isEmpty(userMarketingIds)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_951));
            }
        } else {
            List<String> marketingUserIdList = Lists.newArrayList();
            if (TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(triggerSnapshotEntity.getTriggerType()) || TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())||TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                // 判断状态
                TriggerTaskInstanceEntity successOrFailInstance = null;
                if (TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                    successOrFailInstance = triggerTaskInstanceDao.getSuccessOrFailInstanceByTriggerIdAndSnapshotIdAndRecord(ea, triggerTaskInstance.getTriggerId(), triggerTaskInstance.getTriggerSnapshotId(), triggerTaskInstance.getTriggerTaskSnapshotId(), triggerInstance.getTriggerRecordId());
                } else if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                    successOrFailInstance = triggerTaskInstanceDao.getSuccessOrFailInstanceByTriggerIdAndSnapshotIdAndBatch(ea, triggerTaskInstance.getTriggerId(), triggerTaskInstance.getTriggerSnapshotId(), triggerTaskInstance.getTriggerTaskSnapshotId(), triggerTaskInstance.getBatchId());
                } else {
                    successOrFailInstance = triggerTaskInstanceDao.getSuccessOrFailInstanceByTriggerIdAndSnapshotId(ea, triggerTaskInstance.getTriggerId(), triggerTaskInstance.getTriggerSnapshotId(), triggerTaskInstance.getTriggerTaskSnapshotId());
                }
                if (successOrFailInstance != null) {
                    log.info("已在第一个触发用户批量完成执行");
                    List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
                    if (CollectionUtils.isEmpty(userMarketingIds)) {
                        return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
                    }
                    return ExecuteResult.successResult(successOrFailInstance.getExecuteResult());
                }
                if (TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                    marketingUserIdList = triggerInstanceDao.listMarketingUserIdByTriggerIdAndRecordId(ea, triggerInstance.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId(), triggerInstance.getTriggerRecordId());
                } else if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                    marketingUserIdList = triggerInstanceDao.listMarketingUserIdByTriggerIdAndBatchId(ea, triggerInstance.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId(), triggerTaskInstance.getBatchId());
                } else {
                    marketingUserIdList = triggerInstanceDao.listMarketingUserIdByTriggerId(ea, triggerInstance.getTriggerId(), triggerTaskSnapshot.getTriggerSnapshotId());
                }
            } else {
                marketingUserIdList.add(triggerInstance.getMarketingUserId());
            }
            if (CollectionUtils.isEmpty(marketingUserIdList)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_929));
            }
            // 过滤逻辑：实际批量发送数量
            List<String> userMarketingIds = this.batchMarketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, marketingUserIdList, false);
            if (CollectionUtils.isEmpty(userMarketingIds)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
            }
            // 过滤逻辑：记录当前营销用户发送
            enterpriseSpreadRecordManager.upsertList(Collections.singletonList(triggerInstance.getMarketingUserId()), MarketingActivityActionEnum.getCustomSpreadType(triggerTaskSnapshot.getId()), triggerTaskSnapshot.getEa());
            int ei = eieaConverter.enterpriseAccountToId(ea);
            for (List<String> partMarketingUserIdList : Lists.partition(marketingUserIdList, 1000)) {
                List<UserMarketingWxWorkExternalUserRelationEntity> userMarketingWxWorkExternalUserRelationEntities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, partMarketingUserIdList);
                if (null == userMarketingWxWorkExternalUserRelationEntities) {
                    continue;
                }
                extenalUserIds.addAll(userMarketingWxWorkExternalUserRelationEntities.stream()
                        .map(UserMarketingWxWorkExternalUserRelationEntity::getWxWorkExternalUserId)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(extenalUserIds)) {
                extenalUserIds = wechatWorkExternalUserObjManager.filterDataByScop(ei, extenalUserIds);
            }
            if (CollectionUtils.isEmpty(extenalUserIds)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_949));
            }
        }
        AddNewMsgTemplateArg arg = new AddNewMsgTemplateArg();
        AddNewMsgTemplateArg.Text text = new AddNewMsgTemplateArg.Text();
        String messageContent = triggerTaskSnapshot.getMessageContent();
        if (StringUtils.isNotBlank(messageContent) && paramMap != null) {
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                messageContent = messageContent.replaceAll("\\{" + entry.getKey() + "\\}", String.valueOf(entry.getValue()));
            }
        }
        text.setContent(messageContent);
        arg.setText(text);
        //支持多附件
        String accessToken = momentManager.getOrCreateAccessToken(ea);
        try {
            this.setAttachments(ea, triggerTaskSnapshot, accessToken, arg);
        } catch (Exception e) {
            return ExecuteResult.failResult(e.getMessage());
        }
        String marketingEventId = getMarketingEventId(triggerInstance);
        if (isGroupSend) {
            arg.setChatType("group");
            arg.setSender(groupSender);
            String chatIdList = triggerInstance.getChatIdList();
            if (StringUtils.isNotEmpty(chatIdList)) {
                arg.setChatIdList(GsonUtil.fromJson(chatIdList, ArrayList.class));
            }

            if (CollectionUtils.isNotEmpty(arg.getAttachments())) {
                // 外部内容添加活动参数
                try {
                    QyWxAddressBookEntity qyWxAddressBook = qywxAddressBookManager.queryByEaAndUserId(ea, groupSender);
                    OutLinkMktParamManager.MktParam mktParam = new OutLinkMktParamManager.MktParam(marketingEventId, null, groupSender, qyWxAddressBook.getMobile());
                    outLinkMktParamManager.sopAttachmentAppendMktParam(arg.getAttachments(), mktParam);
                } catch (Exception e) {
                    log.warn("attachmentAppendMktParam fail e:", e);
                }
            }

            AddMsgTemplateResult addMsgTemplateResult = this.addMsgTemplate(accessToken, arg);
            if (null == addMsgTemplateResult || 0 != addMsgTemplateResult.getErrcode()) {
                return ExecuteResult.failResult(null == addMsgTemplateResult ? null : addMsgTemplateResult.getErrmsg());
            } else {
                QywxGroupSendGroupResultEntity qywxGroupSendGroupResultEntity = new QywxGroupSendGroupResultEntity();
                String uuid = UUIDUtil.getUUID();
                qywxGroupSendGroupResultEntity.setId(uuid);
                qywxGroupSendGroupResultEntity.setEa(ea);
                qywxGroupSendGroupResultEntity.setSendid(uuid);
                qywxGroupSendGroupResultEntity.setSender(groupSender);
                qywxGroupSendGroupResultEntity.setMsgid(addMsgTemplateResult.getMsgid());
                qywxGroupSendGroupResultEntity.setErrcode(addMsgTemplateResult.getErrcode());
                qywxGroupSendGroupResultEntity.setErrmsg(addMsgTemplateResult.getErrmsg());
                CustomerGroupListResult result = customerGroupManager.queryCustomerListNew(ea, Lists.newArrayList(groupSender), 1000);
                if (result.isSuccess() && result.getGroupList() != null) {
                    qywxGroupSendGroupResultEntity.setTotalGroupCount(result.getGroupList().size());
                }
                //群筛选条件,获取群数量
                if (CollectionUtils.isNotEmpty(arg.getChatIdList())) {
                    qywxGroupSendGroupResultEntity.setTotalGroupCount(arg.getChatIdList().size());
                }
                qywxGroupSendGroupResultDAO.insert(qywxGroupSendGroupResultEntity);
                return ExecuteResult.successResult(addMsgTemplateResult.getMsgid());
            }
        } else {
            arg.setChatType("single");
            List<AddNewMsgTemplateArg> args = Lists.newArrayList();
            if (triggerTaskSnapshot.getSendSopSetting() != null) {
                if (triggerTaskSnapshot.getSendSopSetting().getAllowSelect() != null) {
                    arg.setAllowSelect(triggerTaskSnapshot.getSendSopSetting().getAllowSelect());
                }
                // 函数指定发送员工
                if (paramMap != null && paramMap.get("qywxEmployeeId") != null) {
                    String qywxEmployeeId = (String) paramMap.get("qywxEmployeeId");
                    // 根据激活状态过滤
                    if (qywxActivatedAccountManager.shouldFiltering(ea) && !qywxActivatedAccountManager.getActivatedUserIds(ea).contains(qywxEmployeeId)) {
                        return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1018));
                    }
                    List<List<String>> partitionExtenalUserIds = Lists.partition(extenalUserIds, 10000);
                    for (List<String> partitionExtenalUserId : partitionExtenalUserIds) {
                        AddNewMsgTemplateArg item = BeanUtil.copy(arg, AddNewMsgTemplateArg.class);
                        item.setSender(qywxEmployeeId);
                        item.setExternalUserid(partitionExtenalUserId);
                        args.add(item);
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(triggerTaskSnapshot.getSendSopSetting().getUserId())
                            || CollectionUtils.isNotEmpty(triggerTaskSnapshot.getSendSopSetting().getDepartmentIds())) {
                        List<Integer> departmentIds = triggerTaskSnapshot.getSendSopSetting().getDepartmentIds()
                                .stream().filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList());
                        // 发送员工范围
                        List<String> senderIds = qywxManager.handleQywxEmployeeUserId(ea, triggerTaskSnapshot.getSendSopSetting().getUserId(), departmentIds, null);
                        // 当前营销用户关联外部联系人的添加员工, 仅触发类型做过滤
                        if (TriggerTypeEnum.TRIGGER_BY_ACTION.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                            List<String> qywxUserIdListByExternalUserIds = groupSendMessageManager.getQywxUserIdListByExternalUserIds(ea, extenalUserIds);
                            senderIds = (List<String>) CollectionUtils.intersection(senderIds, qywxUserIdListByExternalUserIds);
                        }
                        List<String> activatedUserIdList = qywxActivatedAccountManager.getActivatedUserIds(ea);
                        senderIds = senderIds.stream().filter(sender -> {
                            // 根据激活状态过滤
                            if (qywxActivatedAccountManager.shouldFiltering(ea) && !activatedUserIdList.contains(sender)) {
                                return false;
                            }
                            return true;
                        }).collect(Collectors.toList());
                        Map<String, Set<String>> employeeExternalUserIds = groupSendMessageManager.getEmployeeExternalUserIds(ea, senderIds, false, null);
                        for (Map.Entry<String, Set<String>> entry : employeeExternalUserIds.entrySet()) {
                            AddNewMsgTemplateArg item = BeanUtil.copy(arg, AddNewMsgTemplateArg.class);
                            String sender = entry.getKey();
                            item.setSender(sender);
                            List<String> finalExternalUserid = (List<String>) CollectionUtils.intersection(extenalUserIds, entry.getValue());
                            if (CollectionUtils.isEmpty(finalExternalUserid)) {
                                continue;
                            }
                            List<List<String>> partitionExtenalUserIds = Lists.partition(finalExternalUserid, 10000);
                            for (List<String> partitionExtenalUserId : partitionExtenalUserIds) {
                                AddNewMsgTemplateArg pitem = BeanUtil.copy(item, AddNewMsgTemplateArg.class);
                                pitem.setExternalUserid(partitionExtenalUserId);
                                args.add(pitem);
                            }
                        }
                    } else {
                        List<List<String>> partitionExtenalUserIds = Lists.partition(extenalUserIds, 10000);
                        for (List<String> partitionExtenalUserId : partitionExtenalUserIds) {
                            AddNewMsgTemplateArg item = BeanUtil.copy(arg, AddNewMsgTemplateArg.class);
                            item.setExternalUserid(partitionExtenalUserId);
                            args.add(item);
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(args)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1018));
            }
            String sopQywxMsgTaskId = UUIDUtil.getUUID();
            for (AddNewMsgTemplateArg addNewMsgTemplateArg : args) {
                try {
                    if (CollectionUtils.isNotEmpty(arg.getAttachments())) {
                        // 外部内容添加活动参数
                        try {
                            QyWxAddressBookEntity qyWxAddressBook = qywxAddressBookManager.queryByEaAndUserId(ea, addNewMsgTemplateArg.getSender());
                            OutLinkMktParamManager.MktParam mktParam = new OutLinkMktParamManager.MktParam(marketingEventId, null, groupSender, qyWxAddressBook.getMobile());
                            outLinkMktParamManager.sopAttachmentAppendMktParam(addNewMsgTemplateArg.getAttachments(), mktParam);
                        } catch (Exception e) {
                            log.warn("attachmentAppendMktParam fail e:", e);
                        }
                    }
                    AddMsgTemplateResult addMsgTemplateResult = this.addMsgTemplate(accessToken, addNewMsgTemplateArg);
                    if (null != addMsgTemplateResult && 0 == addMsgTemplateResult.getErrcode()) {
                        String msgid = addMsgTemplateResult.getMsgid();
                        triggerTaskInstanceDao.insertSopQywxMsgTask(ea, sopQywxMsgTaskId, msgid);
                        groupSendMessageManager.handlerSopQywxMsgTaskResult(msgid, ea);
                    }
                } catch (Exception e) {
                    log.warn("addMsgTemplate item fail arg:{}", arg, e);
                }
            }
            return ExecuteResult.successResult(sopQywxMsgTaskId);
        }
    }

    public void setAttachments(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, String accessToken, AddNewMsgTemplateArg arg) {
        QywxAttachmentsRelationEntity attachmentsRelation = qywxAttachmentsRelationDAO.getDetailByTargetId(triggerTaskSnapshot.getId(), QywxAttachmentScenesTypeEnum.QYWX_SOP.getType());
        if(Objects.nonNull(attachmentsRelation)){
            List<SendWelcomeMessageNewArg.Attachment> attachmentList = qywxManager.builtNewAttachmentsArg(ea, accessToken, attachmentsRelation,null, null,null);
            if (CollectionUtils.isEmpty(attachmentList)) {
                log.warn("QywxContactManager sendCustomerWelcomeMessage builtNewAttachmentsArg fail,attachmentsRelation={}", attachmentsRelation);
            }
            List<AddNewMsgTemplateArg.Attachments> copy = BeanUtil.copyByGson(attachmentList, AddNewMsgTemplateArg.Attachments.class);
            arg.setAttachments(copy);
        } else if (StringUtils.isNotBlank(triggerTaskSnapshot.getWxMessageContent())) {
            WxNewsMessageContent wxNewsMessageContent = GsonUtil.fromJson(triggerTaskSnapshot.getWxMessageContent(), WxNewsMessageContent.class);
            if (wxNewsMessageContent.getPicPath() == null) {
                wxNewsMessageContent.setPicPath(groupMessageDefaultCoverPath);
            }
            AddNewMsgTemplateArg.Attachments attachment = new AddNewMsgTemplateArg.Attachments();
            attachment.setMsgType("miniprogram");
            AddNewMsgTemplateArg.Miniprogram miniprogram = new AddNewMsgTemplateArg.Miniprogram();
            StringBuilder sb = new StringBuilder();
            String title = null;
            if (StringUtils.isNotBlank(wxNewsMessageContent.getTitle()) && wxNewsMessageContent.getTitle().length() > 12) {
                for (int i = 0; i < 9; i++) {
                    sb.append(wxNewsMessageContent.getTitle().charAt(i));
                }
                sb.append("...");
                title = sb.toString();
            } else {
                title = wxNewsMessageContent.getTitle();
            }

            miniprogram.setTitle(title);
            miniprogram.setAppId(eaWechatAccountBindDao.getWxAppIdByEa(ea, "YXT"));
            boolean defaultCoverPath = Objects.equals(wxNewsMessageContent.getPicPath(), groupMessageDefaultCoverPath);
            String mediaId = defaultCoverPath ? redisManager.getDefaultCoverPath(wxNewsMessageContent.getPicPath(), ea) : redisManager.getQywxMediaId(wxNewsMessageContent.getPicPath());
            if (StringUtils.isBlank(mediaId)) {
                byte[] data = fileV2Manager.downloadFileByUrl(wxNewsMessageContent.getPicPath(), ea);
                UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, wxNewsMessageContent.getPicPath(), "image");
                if (null != mediaResult && StringUtils.isNotBlank(mediaResult.getMediaId())) {
                    mediaId = mediaResult.getMediaId();
                    if (defaultCoverPath) {
                        redisManager.setDefaultCoverPath(wxNewsMessageContent.getPicPath(), ea, mediaId);
                    } else {
                        redisManager.setQywxMediaId(wxNewsMessageContent.getPicPath(), mediaId);
                    }
                } else {
                    throw new RuntimeException(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1081));
                }
            }
            miniprogram.setPicMediaId(mediaId);
            miniprogram.setPage(wxNewsMessageContent.getPage());
            attachment.setMiniprogram(miniprogram);
            List<AddNewMsgTemplateArg.Attachments> attachments = new ArrayList<>();
            attachments.add(attachment);
            arg.setAttachments(attachments);
        }
    }

    /**
     * 发送互动消息
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendUnionMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        List<SendUnionMessageArg> sendUnionMessageArgs = Lists.newArrayList();
        String sendUnionMessageArgStr = triggerTaskSnapshot.getSendUnionMessageArg();
        try {
            if (sendUnionMessageArgStr.startsWith("{")) {
                SendUnionMessageArg sendUnionMessageArg = JSONObject.parseObject(sendUnionMessageArgStr, SendUnionMessageArg.class);
                sendUnionMessageArgs.add(sendUnionMessageArg);
            } else if (sendUnionMessageArgStr.startsWith("[")) {
                sendUnionMessageArgs.addAll(JSONObject.parseArray(sendUnionMessageArgStr, SendUnionMessageArg.class));
            }
        } catch (Exception e) {
            log.warn("arg参数无效:{} triggerInstanceId:{}", sendUnionMessageArgStr, triggerInstance.getId());
        }
        if (CollectionUtils.isEmpty(sendUnionMessageArgs)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1116) + sendUnionMessageArgStr);
        }
        SendUnionMessageExtendArg sendUnionMessageExtendArg = triggerTaskSnapshot.getSendUnionMessageExtendArg() == null ? new SendUnionMessageExtendArg() : triggerTaskSnapshot.getSendUnionMessageExtendArg();
        String marketingUserId = triggerInstance.getMarketingUserId();
        if (marketingUserId == null) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_929));
        }
        TriggerSnapshotEntity triggerSnapshotEntity = triggerSnapshotDao.getCurrentUseSnapshot(ea, triggerInstance.getTriggerId());
        Map<String, Object> paramMap = null;
        if (triggerTaskInstance.getParamMap() != null) {
            try {
                paramMap = JSON.parseObject(triggerTaskInstance.getParamMap(), new TypeReference<Map<String, Object>>() {
                });
            } catch (Exception ignored) {
            }
        }
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, -10000, Collections.singletonList(marketingUserId), InfoStateEnum.BRIEF);
        if (null == userMarketingAccountDataMap || userMarketingAccountDataMap.isEmpty()) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1134));
        }
        if (BooleanUtils.isTrue(sendUnionMessageExtendArg.getUserExecuteOnce())) {
            try {
                // 历史数据兼容
                String targetObjectId = null;
                Integer targetObjectType = null;
                if (triggerSnapshotEntity.getTargetObjects() == null || triggerSnapshotEntity.getTargetObjects().isEmpty()) {
                    targetObjectId = triggerSnapshotEntity.getTargetObjectId();
                    targetObjectType = triggerSnapshotEntity.getTargetObjectType();
                }
                if (StringUtils.isBlank(targetObjectId)) {
                    targetObjectId = triggerInstance.getTargetObjectId();
                    targetObjectType = triggerInstance.getTargetObjectType();
                }
                if (StringUtils.isBlank(targetObjectId)) {
                    targetObjectId = (String) paramMap.get("objectId");
                    targetObjectType = (Integer) paramMap.get("objectType");
                }
                int count = triggerTaskInstanceDao.listByTriggerInfoAndObjectId(ea, triggerTaskInstance.getTriggerId(),
                        triggerTaskInstance.getTriggerSnapshotId(), triggerTaskInstance.getTriggerTaskSnapshotId(),
                        marketingUserId, targetObjectId);
                if (count > 0) {
                    log.info("此SOP下多次访问同一内容仅触发一次通知 {}", triggerTaskInstance.getTriggerId());
                    return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1158));
                }
            } catch (Exception e) {
                log.info("未知错误 {}", triggerTaskInstance.getTriggerId());
            }
        }
        String name = userMarketingAccountDataMap.get(marketingUserId).getName();
        if (StringUtils.isEmpty(name)) {
            if (BooleanUtils.isTrue(sendUnionMessageExtendArg.isMustHasName())) {
                log.info("仅有姓名或昵称的用户产生行为才触发互动通知 {}", triggerTaskInstance.getTriggerId());
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1168));
            }
            name = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1170) + marketingUserId.substring(marketingUserId.length() - 6);
        }
        String actionName = getTriggerActionName(triggerSnapshotEntity);
        String targetName = getTargetName(triggerSnapshotEntity, triggerInstance, marketingUserId, ea, paramMap);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String unionTime = dateFormat.format(triggerTaskInstance.getCreateTime());
        Map<String, String> executeResultMap = new HashMap<>();
        executeResultMap.put(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1177), name);
        executeResultMap.put(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1178), actionName);
        executeResultMap.put(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1179), targetName);
        executeResultMap.put(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1180), unionTime);

        Map<String, String> customParams = new HashMap<>();
        String campaignId = triggerInstance.getCampaignId();
        if (StringUtils.isNotBlank(campaignId)) {
            CampaignMergeDataEntity campaignMergeData = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
            String campaignMembersObjId = campaignMergeData.getCampaignMembersObjId();
            if (campaignMembersObjId != null) {
                // 获取活动成员自定义字段
                Map<String, String> customFields = new HashMap<>();
                List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
                if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                    crmCustomerFields.forEach(e -> {
                        customFields.put(e.getFieldCaption(), e.getFieldName());
                    });
                }
                if (!customFields.isEmpty() && StringUtils.isNotBlank(campaignMembersObjId)) {
                    Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                    if (detail != null) {
                        for (String customField : customFields.keySet()) {
                            Object value = detail.get(customFields.get(customField));
                            if (null != value) {
                                customParams.put(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getLabel() + "." + customField, String.valueOf(value));
                            } else {
                                customParams.put(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getLabel() + "." + customField, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                            }
                        }
                    }
                }
            }
        }

        String marketingEventId = getMarketingEventId(triggerInstance);
        if (StringUtils.isNotBlank(marketingEventId)) {
            Map<String, String> customFields = new HashMap<>();
            List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);
            if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                crmCustomerFields.forEach(e -> {
                    customFields.put(e.getFieldCaption(), e.getFieldName());
                });
            }
            if (!customFields.isEmpty()) {
                Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
                if (detail != null) {
                    for (String customField : customFields.keySet()) {
                        Object value = detail.get(customFields.get(customField));
                        if (null != value) {
                            customParams.put(CrmObjectApiNameEnum.MARKETING_EVENT.getLabel() + "." + customField, String.valueOf(value));
                        } else {
                            customParams.put(CrmObjectApiNameEnum.MARKETING_EVENT.getLabel() + "." + customField, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                        }
                    }
                }
            }
        }

        List<String> userIds = new ArrayList<>();
        List<String> phones = new ArrayList<>();
        for (SendUnionMessageArg sendUnionMessageArg : sendUnionMessageArgs) {
            String crmObjectApiName = sendUnionMessageArg.getLabel();
            String fieldName = sendUnionMessageArg.getFieldName();//对象字段名或“指定人员”
            if ("SPECIFIED".equals(fieldName)) {
                // 指定人员
                String userIdString = sendUnionMessageExtendArg.getUserIdString();
                if (StringUtils.isNotEmpty(userIdString)) {
                    userIds.addAll(Arrays.asList(userIdString.split(",")));
                }
                // 指定手机号
                String phoneNumberString = sendUnionMessageExtendArg.getPhoneNumberString();
                if (StringUtils.isNotEmpty(phoneNumberString)) {
                    phones.addAll(Arrays.asList(phoneNumberString.split(",")));
                }
            } else if ("PROMOTER".equals(fieldName)) {
                // 活动推广人
                Integer spreadFsUid = triggerInstance.getSpreadFsUid();
                if (spreadFsUid == null && paramMap != null) {
                    spreadFsUid = (Integer) paramMap.get("spreadFsUid");
                }
                if (spreadFsUid != null) {
                    userIds.add(String.valueOf(spreadFsUid));
                }
            } else if ("MARKETING_OWNER".equals(fieldName) || "MARKETING_TEAM".equals(fieldName)) {
                // 活动负责人与有编辑权限的相关团队成员
                if (StringUtils.isNotBlank(marketingEventId)) {
                    ObjectData marketingEvent = crmV2Manager.getDetailIgnoreError(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
                    if (marketingEvent != null && "MARKETING_OWNER".equals(fieldName)) {
                        userIds.add(String.valueOf(marketingEvent.getOwner()));
                    } else if (marketingEvent != null && "MARKETING_TEAM".equals(fieldName)) {
                        JSONArray relevantTeamList = JSONArray.parseArray(JSONArray.toJSONString(marketingEvent.get("relevant_team")));
                        for (int i = 0; i < relevantTeamList.size(); i++) {
                            JSONObject relevantTeam = relevantTeamList.getJSONObject(i);
                            if ("2".equals(relevantTeam.getString("teamMemberPermissionType")) && null != relevantTeam.getJSONArray("teamMemberEmployee")) {
                                userIds.addAll(relevantTeam.getJSONArray("teamMemberEmployee").toJavaList(String.class));
                            }
                        }
                    }
                }
            } else if ("ALL_WECHATEXTERNALUSER_ADDER".equals(fieldName)) {
                // 所有添加客户企微好友的员工
                String crmId = crmV2Manager.getCrmIdByMarketingUserId(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), ea, marketingUserId);
                if (StringUtils.isNotBlank(crmId)) {
                    HashMap<String, Object> queryParams = Maps.newHashMap();
                    queryParams.put("external_user_id", crmId);
                    List<ObjectData> wechatFriendsRecordList = crmV2Manager.queryObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), queryParams);
                    if (CollectionUtils.isNotEmpty(wechatFriendsRecordList)) {
                        userIds.addAll(wechatFriendsRecordList.stream().filter(e -> e.get("user_id") != null).map(e -> e.getString("user_id")).collect(Collectors.toList()));
                    }
                }
            } else {
                CrmObjectApiNameEnum crmObjectApiNameEnum = CrmObjectApiNameEnum.fromName(crmObjectApiName);
                if (crmObjectApiNameEnum == null) {
                    log.warn("无此预设对象:{} triggerInstanceId:{}", crmObjectApiName, triggerInstance.getId());
                }
                String crmId = crmV2Manager.getCrmIdByMarketingUserId(crmObjectApiName, ea, marketingUserId);
                if (Strings.isNullOrEmpty(crmId)) {
                    log.warn("用户无该对象:{} triggerInstanceId:{}", crmObjectApiName, triggerInstance.getId());
                }
                ObjectData detail = crmV2Manager.getOneByList(ea, -10000, crmObjectApiName, crmId);
                if (detail == null) {
                    log.warn("无该对象:{} triggerInstanceId:{} crmId:{}", crmObjectApiName, triggerInstance.getId(), crmId);
                }
                if (detail != null && detail.get(sendUnionMessageArg.getFieldName()) != null) {
                    userIds.addAll((List<String>) detail.get(sendUnionMessageArg.getFieldName()));
                }
            }
        }
        log.warn("根据参数查出员工:{} triggerTask:{}", userIds, triggerTaskSnapshot);
        Set<Integer> fsUserIds = userIds.stream().filter(id -> !"-10000".equals(id)).map(Integer::valueOf).collect(Collectors.toSet());
        // 条件过滤
        if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getUserIds()) || CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getDepartmentIds())) {
            List<Integer> filterUserIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getUserIds())) {
                filterUserIds.addAll(sendUnionMessageExtendArg.getUserIds());
            }
            if (CollectionUtils.isNotEmpty(sendUnionMessageExtendArg.getDepartmentIds())) {
                List<Integer> employeeIdsByCircleIds = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, sendUnionMessageExtendArg.getDepartmentIds());
                if (CollectionUtils.isNotEmpty(employeeIdsByCircleIds)) {
                    filterUserIds.addAll(employeeIdsByCircleIds);
                }
            }
            if (triggerTaskSnapshot.getUpdateTime().getTime() > DateUtil.parse("2024-01-19 23:00:00", "yyyy-MM-dd HH:mm:ss").getTime()) {
                fsUserIds = fsUserIds.stream().filter(id -> !filterUserIds.contains(id)).collect(Collectors.toSet());
            } else {
                fsUserIds = Sets.newHashSet(CollectionUtils.intersection(fsUserIds, filterUserIds));
            }
        }
        log.warn("选择器过滤后员工:{} triggerTask:{}", fsUserIds, triggerTaskSnapshot);
        Map<String, String> noticeContent = sendUnionMessageExtendArg.getNoticeContent();
        if (NoticeTypeEnum.MSG.getNoticeType().equals(triggerTaskSnapshot.getNoticeType())) {
            // 短信消息
            /*********************************** MSG START **********************************/
            // 构建参数
            GroupSenderArg groupSenderArg = new GroupSenderArg();
            groupSenderArg.setChannelType(ChannelTypeEnum.MARKETING_FLOW.getType());
            groupSenderArg.setEventType(SaveOrSendTypeEnum.SEND.getType());
            groupSenderArg.setType(MwSendTaskTypeEnum.IMMEDIATELY_SEND.getType());
            groupSenderArg.setGroupType(SmsGroupTypeEnum.PHONE_LIST.getType());
            groupSenderArg.setShowTemplate(false);
            groupSenderArg.setReal(true);
            groupSenderArg.setEa(ea);
            String triggerId = triggerTaskSnapshot.getTriggerId();
            MarketingTriggerEntity marketingTriggerEntity = marketingTriggerDao.getById(triggerId, ea);
            if (marketingTriggerEntity != null) {
                Integer creator = marketingTriggerEntity.getCreator();
                groupSenderArg.setTaskCreatorId(Objects.equals(creator, -10000) ? -5000 : creator);
            }
            groupSenderArg.setUserId(-5000);
            groupSenderArg.setUnionMsg(true);
            String content = "客户名称：{客户名称} 互动行为：{互动行为} 互动内容：{互动内容} 互动时间：{互动时间}";
            String template = content;
            Map<String, String> templateParams = new HashMap<>();
            if ("custom".equals(sendUnionMessageExtendArg.getNoticeTemplate())) {
                if (noticeContent != null) {
                    content = noticeContent.get("content");
                    template = content;
                    for (Map.Entry<String, String> entry : customParams.entrySet()) {
                        if (content.contains("{" + entry.getKey() + "}")) {
                            templateParams.put(entry.getKey(), entry.getValue());
                        }
                        content = content.replace("{" + entry.getKey() + "}", entry.getValue());
                    }
                }
                if (StringUtils.isBlank(content)) {
                    return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1372));
                }
            }
            Matcher matcher = SmsSettingManager.pattern.matcher(template);
            template = matcher.replaceAll(" ");
            MwSmsTemplateEntity presetTemplatesByContent = mwSmsTemplateDao.getPresetTemplatesByContent(template);
            if (null != presetTemplatesByContent) {
                groupSenderArg.setTemplateId(presetTemplatesByContent.getId());
            } else {
                groupSenderArg.setTemplateContent(template);
            }

            // 根据userId -> phone
            phones.addAll(fsUserIds.stream().map(userId -> {
                AccountIsApplyForKISArg arg = new AccountIsApplyForKISArg();
                arg.setEa(ea);
                arg.setFsUserId(userId);
                Result<AccountIsApplyForKISResult> applyForKIS = accountService.isApplyForKIS(arg);
                return applyForKIS.getData() == null ? null : applyForKIS.getData().getPhone();
            }).filter(Objects::nonNull).collect(Collectors.toList()));

            if (phones.isEmpty()) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1391));
            }
            executeResultMap.putAll(templateParams);
            List<PhoneContentResult> phoneContentResults = phones.stream().map(phone -> new PhoneContentResult(phone, executeResultMap)).collect(Collectors.toList());
            groupSenderArg.setPhones(phoneContentResults);
            groupSenderArg.setBusinessType(ChannelTypeEnum.MARKETING_FLOW.getName());
            groupSenderArg.setNodeType(SmsRecordNodeTypeEnum.END.getNodeType());
            groupSenderArg.setNodeType(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1398));
            Result<GroupSendResult> sendGroupSmsResult = sendService.sendGroupSms(groupSenderArg);
            if (sendGroupSmsResult.isSuccess()) {
                String resultData;
                if ("custom".equals(sendUnionMessageExtendArg.getNoticeTemplate())) {
                    Map<String, String> result = new HashMap<>();
                    result.put("content", content);
                    resultData = GsonUtil.toJson(result);
                } else {
                    resultData = GsonUtil.toJson(executeResultMap);
                }
                return ExecuteResult.successResult(resultData);
            } else {
                return ExecuteResult.failResult(sendGroupSmsResult.getErrCode() + sendGroupSmsResult.getErrMsg());
            }
            /*********************************** MSG END **********************************/
        } else {
            // 应用消息，可兼容旧数据
            /*********************************** APP START **********************************/
            if (fsUserIds.isEmpty()) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1418));
            }
            String cmlUrl = "ava://marketing_app/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?objectId=" + marketingUserId + "&associationId=";
            UserMarketingCrmLeadAccountRelationEntity leadEntity = userMarketingCrmLeadAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
            if (leadEntity != null) {
                String leadId = leadEntity.getCrmLeadId();
                ArrayList<String> leadsIds = new ArrayList<>(1);
                leadsIds.add(leadId);
                Map<String, String> weChatAvatarUrlMap = userMarketingAccountManager.getLeadAndWeChatAvatarUrlMap(ea, -10000, leadsIds);
                cmlUrl += leadId + "&fromLead=true&avatar=" + weChatAvatarUrlMap.get(leadId);
            }
            List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
            String title = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1430);
            String description = null;
            if ("custom".equals(sendUnionMessageExtendArg.getNoticeTemplate())) {
                title = noticeContent.get("title");
                description = noticeContent.get("content");
                if (StringUtils.isEmpty(title) || StringUtils.isEmpty(description)) {
                    return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1436));
                }
                for (Map.Entry<String, String> entry : customParams.entrySet()) {
                    description = description.replace("{" + entry.getKey() + "}", entry.getValue());
                }
            } else {
                content.add(OfficeMessageArg.LabelWarp.newInstance("名称", "qx.ot.mark.name", name));
                content.add(OfficeMessageArg.LabelWarp.newInstance("互动行为", "qx.ot.mark.interactive_behavior", actionName));
                content.add(OfficeMessageArg.LabelWarp.newInstance("互动内容", "qx.ot.mark.interactive_content", targetName));
                content.add(OfficeMessageArg.LabelWarp.newInstance("互动时间", "qx.ot.mark.interactive_time", unionTime));
            }
            String targetId = null;
            Integer targetType = null;
            UserMarketingObjectResult relation = userMarketingAccountRelationManager.doGetRelationFromUserMarketingId(ea, marketingUserId);
            if(relation!=null && relation.getTargetType()!=null){
                targetId = relation.getTargetId();
                targetType = relation.getTargetType();
            }
            String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail" + "&contentType=" + NoticeContentTypeEnum.SEND_UNION_MSG.getType() + "&objectId=" + marketingUserId+ "&targetId="+ targetId
                    + "&targetType="+ targetType;;
            String qywxUrl = "/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?objectId=" + marketingUserId+ "&targetId="+ targetId
                    + "&targetType="+ targetType;;
            OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
            infoTitle.setContent(title);
            infoTitle.setInternationalContent("qx.ot.mark.interactive_title");
            employeeMsgSender.sendUnionMessage(ea, -10000, fsUserIds, infoTitle, description, content, fsUrl, cmlUrl, qywxUrl);
            //判断是否开启了企微h5(营销助手通知)
            boolean openQywxH5Notice = groupSendMessageManager.openQywxH5Notice(ea);
            if (openQywxH5Notice) {
                List<Integer> allUserIds = new ArrayList<>(fsUserIds);
                String url = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-marketing-user/pages/customer-detail/customer-detail?ea=" + ea + "&objectId=" + marketingUserId+ "&targetId="+ targetId
                        + "&targetType="+ targetType;
                groupSendMessageManager.sendQywxH5AgentMessage(ea, allUserIds, content, url);
            }
            /*********************************** APP END **********************************/
            if ("custom".equals(sendUnionMessageExtendArg.getNoticeTemplate())) {
                Map<String, String> result = new HashMap<>();
                result.put("title", title);
                result.put("content", description);
                return ExecuteResult.successResult(GsonUtil.toJson(result));
            } else {
                return ExecuteResult.successResult(GsonUtil.toJson(executeResultMap));
            }
        }
    }

    public @Nullable String getMarketingEventId(TriggerInstanceEntity triggerInstance) {
        String marketingEventId = null;
        if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType()) || MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(triggerInstance.getSceneType())) {
            marketingEventId = triggerInstance.getSceneTargetId();
        }
        if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
            ActivityEntity conference = conferenceDAO.getConferenceById(triggerInstance.getSceneTargetId());
            if (conference != null) {
                marketingEventId = conference.getMarketingEventId();
            }
        }
        if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
            MarketingLiveEntity live = marketingLiveDAO.getById(triggerInstance.getSceneTargetId());
            if (live != null) {
                marketingEventId = live.getMarketingEventId();
            }
        }
        return marketingEventId;
    }

    /**
     * 执行web hook
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult invokeWebHook(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        EnvContext envContext = new EnvContext();
        if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
            MarketingLiveEntity live = marketingLiveDAO.getById(triggerInstance.getSceneId());
            if (live != null) {
                envContext.setLiveId(live.getId());
                envContext.setMarketingEventId(live.getMarketingEventId());
            }
        }
        if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
            ActivityEntity conference = conferenceDAO.getConferenceById(triggerInstance.getSceneId());
            if (conference != null) {
                envContext.setConferenceId(conference.getId());
                envContext.setMarketingEventId(conference.getMarketingEventId());
            }
        }
        if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType())) {
            envContext.setMarketingEventId(triggerInstance.getSceneId());
        }
        UserContext userContext = new UserContext();
        userContext.setMarketingUserId(triggerInstance.getMarketingUserId());
        userContext.setCampaignId(triggerInstance.getCampaignId());
        webHookManager.invokeWebHook(ea, triggerTaskSnapshot.getWebHookId(), envContext, userContext);
        return ExecuteResult.successResult();
    }

    /**
     * 添加工作任务
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult addBoard(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        BoardCardArg boardCardArg = triggerTaskSnapshot.getBoardCardArg();
        String errorMsg = boardCardArg.verifyErrorMsg();
        if (!Strings.isNullOrEmpty(errorMsg)) {
            return ExecuteResult.failResult(errorMsg);
        }
        BoardCardEntity boardCardEntity = new BoardCardEntity();
        boardCardEntity.setBoardId(boardCardArg.getBoardId());
        boardCardEntity.setBoardCardListId(boardCardArg.getBoardCardListId());
        boardCardEntity.setName(boardCardArg.getBoardCardName());
        boardCardEntity.setPrincipals(boardCardArg.getPrincipals());
        boardCardEntity.setStartTime(new Date(boardCardArg.getStartTime()));
        boardCardEntity.setEndTime(new Date(boardCardArg.getEndTime()));
        String boardCardId = UUIDUtil.getUUID();
        boardCardEntity.setId(boardCardId);
        boardCardEntity.setEa(ea);
        boardCardEntity.setStatus(BoardCardStatus.NOT_START.getStatus());
        boardCardEntity.setType(BoardCardTypeEnum.MARKETING_ACTIVITY_DATA.getType());
        boardCardEntity.setCreator(-10000);
        boardCardDao.insertBoardCard(boardCardEntity);
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getCardDisplayKey(boardCardArg.getBoardCardListId()), ImmutableList.of(new NestedId(boardCardId)));
        AddBoardCardArg addBoardCardArg = new AddBoardCardArg();
        BeanUtils.copyProperties(boardCardEntity, addBoardCardArg);
        addBoardCardArg.setStartTime(boardCardArg.getStartTime());
        addBoardCardArg.setEndTime(boardCardArg.getEndTime());
        boardManager.addBoardCardActivityWhenCreateBoardCard(ea, triggerTaskInstance.getTriggerId(), addBoardCardArg, boardCardArg.getBoardId(), boardCardEntity.getId(), true,
                BoardOperatorTypeEnum.TRIGGER.getOperator());
        return ExecuteResult.successResult();
    }

    /**
     * 发送邮件
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendEmailMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
        }
        List<String> emails = new LinkedList<>();
        if (!Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
            Map<String, String> campaignIdToEmailMap = campaignMergeDataManager.getUserEmailByCampaignId(ea, Lists.newArrayList(triggerInstance.getCampaignId()));
            if (!Strings.isNullOrEmpty(campaignIdToEmailMap.get(triggerInstance.getCampaignId()))) {
                emails.add(campaignIdToEmailMap.get(triggerInstance.getCampaignId()));
            }
        }
        if (emails.isEmpty()) {
            Set<String> marketingUserEmails = marketingUserGroupManager.listEmailByMarketingUserIds(ea, ImmutableList.of(triggerInstance.getMarketingUserId())).stream().map(MarketingUserWithEmail::getEmail).filter(Objects::nonNull).collect(Collectors.toSet());
            emails.addAll(marketingUserEmails);
        }
        if (emails.isEmpty()) {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("actionType", "MAIL");
            paramMap.put("actionStatus", "fail");
            log.info("startTaskInstanceByMarketingUserId actionType:MAIL paramMap:{}", paramMap);
            this.startTaskInstanceByMarketingUserId(ea, triggerInstance.getMarketingUserId(), paramMap);
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1570));
        }
        SendEmailArg sendEmailArg = new SendEmailArg();
        sendEmailArg.setEa(ea);
        sendEmailArg.setAccountType(MailApiUserTypeEnum.TRIGGER.getType());
        sendEmailArg.setSenderIds(ImmutableList.of(triggerTaskSnapshot.getEmail().getSender()));
        sendEmailArg.setTitle(triggerTaskSnapshot.getEmail().getTitle());
        if(!Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
            CampaignMergeDataEntity campaignMergeDataById = campaignMergeDataDAO.getCampaignMergeDataById(triggerInstance.getCampaignId());
            sendEmailArg.setContent(mailManager.replaceCustomFields(triggerTaskSnapshot.getEmail().getHtml(), ea, campaignMergeDataById.getCampaignMembersObjId()));
        } else {
            sendEmailArg.setContent(triggerTaskSnapshot.getEmail().getHtml());
        }
        sendEmailArg.setContent(ReplaceUtil.replaceMarketingActivityId(sendEmailArg.getContent(), StringUtils.EMPTY));
        String marketingEventId = StringUtils.EMPTY;
        if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(triggerInstance.getSceneTargetId());
            marketingEventId = activityEntity != null ? activityEntity.getMarketingEventId() : null;
        } else if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(triggerInstance.getSceneTargetId());
            marketingEventId = marketingLiveEntity != null ? marketingLiveEntity.getMarketingEventId() : null;
        } else if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType())
                || MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(triggerInstance.getSceneType())) {
            marketingEventId = triggerInstance.getSceneTargetId();
        }
        marketingEventId = marketingEventId != null ? marketingEventId : StringUtils.EMPTY;
        sendEmailArg.setContent(ReplaceUtil.replaceMarketingEventId(sendEmailArg.getContent(), marketingEventId));
        if (!Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
            if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
                sendEmailArg.setContent(doReplaceConferenceContent(triggerInstance, sendEmailArg.getContent(), emails.get(0)));
            }
            if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
                sendEmailArg.setContent(doReplaceLiveContent(triggerInstance, sendEmailArg.getContent()));
            }
            if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType())) {
                String html = sendEmailArg.getContent();

                ObjectData mktDetail = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), triggerInstance.getSceneTargetId());
                // 活动参数替换
                if (mktDetail != null) {
                    if (html.contains("{市场活动名称}")) {
                        html = html.replace("{市场活动名称}", mktDetail.get("name") == null ? "暂无" : mktDetail.get("name").toString());
                    }
                    if (html.contains("{活动开始时间}")) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        if (mktDetail.get("begin_time") != null) {
                            html = html.replace("{活动开始时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
                        }
                    }
                    if (html.contains("{活动结束时间}")) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        if (mktDetail.get("end_time") != null) {
                            html = html.replace("{活动结束时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
                        }
                    }
                }
                sendEmailArg.setContent(html);
            }
        }

        // 官网sop邮件参数值替换
        SceneTriggerEntity sceneTrigger = sceneTriggerDao.getByTriggerIdAndSceneInfo(ea,
                triggerInstance.getSceneType(), triggerInstance.getSceneId(), triggerInstance.getSceneTargetId(), triggerInstance.getTriggerId());
        if (MarketingSceneType.OFFICIAL_WEBSITE.getType().equals(sceneTrigger.getSceneType())) {
            List<UserMarketingCrmLeadAccountRelationEntity> leadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, Collections.singleton(triggerInstance.getMarketingUserId()));
            ObjectData leadObj = null;
            ObjectData contactObj = null;
            if (CollectionUtils.isNotEmpty(leadAccountRelationEntities)) {
                UserMarketingCrmLeadAccountRelationEntity crmLeadAccountRelationEntity = leadAccountRelationEntities.get(leadAccountRelationEntities.size() - 1);
                leadObj = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), crmLeadAccountRelationEntity.getCrmLeadId());
            }
            List<UserMarketingCrmContactAccountRelationEntity> contactAccountRelationEntities = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, Collections.singleton(triggerInstance.getMarketingUserId()));
            if (CollectionUtils.isNotEmpty(contactAccountRelationEntities)) {
                UserMarketingCrmContactAccountRelationEntity crmContactAccountRelation = contactAccountRelationEntities.get(contactAccountRelationEntities.size() - 1);
                contactObj = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.CONTACT.getName(), crmContactAccountRelation.getCrmContactId());
            }
            sendEmailArg.setContent(extractFields(sendEmailArg.getContent(), ea, leadObj, contactObj));
        }

        List<String> mailList = Lists.newArrayList(emails.get(0));
        sendEmailArg.setMailList(mailList);
        if (CollectionUtils.isNotEmpty(triggerTaskSnapshot.getEmail().getAttachments())) {
            List<MailServiceMarketingActivityVO.MailAttachment> attachmentList = Lists.newArrayList();
            for (Email.Attachment attachment : triggerTaskSnapshot.getEmail().getAttachments()) {
                MailServiceMarketingActivityVO.MailAttachment sendAttachment = new MailServiceMarketingActivityVO.MailAttachment();
                sendAttachment.setAttachmentPath(attachment.getAPath());
                sendAttachment.setAttachmentName(attachment.getName());
                sendAttachment.setSize(attachment.getSize());
                sendAttachment.setExt(attachment.getExt());
                attachmentList.add(sendAttachment);
            }
            sendEmailArg.setAttachments(JSON.toJSONString(attachmentList));
        }
        sendEmailArg.setTriggerTaskInstanceId(triggerTaskInstance.getId());
        sendEmailArg.setTaskId(UUIDUtil.getUUID());
        Result<SendEmailResult> result = mailManager.sendEmailWithOutSaveDetailV2(sendEmailArg);
        if (result.isSuccess()) {
            this.saveEmailSendRecord(ea, marketingEventId, triggerInstance.getTriggerId(), mailList, result.getData());
        } else {
            return ExecuteResult.failResult(result.getErrMsg());
        }
        return ExecuteResult.successResult(result.getData().getLabelId() + "");
    }

    private String extractFields(String text, String ea, ObjectData leadObj, ObjectData contactObj) {
        // 正则表达式用于匹配 $${对象.字段.默认值} 或 $${对象.字段}
        Pattern pattern = Pattern.compile("\\$\\$\\{(.*?)\\}");
        Matcher matcher = pattern.matcher(text);
        try {
            while (matcher.find()) {
                String matchedText = matcher.group(1);  // 获取$${}内的全部文本
                String target = "$${" + matchedText + "}";
                String[] parts = matchedText.split("\\.", 3);  // 按照第一个出现的两个点分割成三部分

                if (parts.length == 3) {
                    // 有默认值的情况：$${对象.字段.默认值}
                    String object = parts[0];
                    String field = parts[1];
                    String replacement = parts[2];
                    if (leadObj != null && CrmObjectApiNameEnum.CRM_LEAD.getName().equals(object) && StringUtils.isNotBlank(leadObj.getString(field))) {
                        FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(eieaConverter.enterpriseAccountToId(ea), CrmObjectApiNameEnum.CRM_LEAD.getName(), field);
                        String type = fieldDescribe.getType();
                        replacement = formatValue(type, leadObj, field);
                    }
                    if (contactObj != null && CrmObjectApiNameEnum.CONTACT.getName().equals(object) && StringUtils.isNotBlank(contactObj.getString(field))) {
                        FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(eieaConverter.enterpriseAccountToId(ea), CrmObjectApiNameEnum.CONTACT.getName(), field);
                        String type = fieldDescribe.getType();
                        replacement = formatValue(type, contactObj, field);
                    }
                    text = text.replace(target, replacement);
                } else if (parts.length == 2) {
                    // 无默认值的情况：$${对象.字段}
                    String object = parts[0];
                    String field = parts[1];
                    String replacement = "";
                    if (leadObj != null && CrmObjectApiNameEnum.CRM_LEAD.getName().equals(object)) {
                        if (StringUtils.isNotBlank(leadObj.getString(field))) {
                            FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(eieaConverter.enterpriseAccountToId(ea), CrmObjectApiNameEnum.CRM_LEAD.getName(), field);
                            String type = fieldDescribe.getType();
                            replacement = formatValue(type, leadObj, field);
                        }
                    }
                    if (contactObj != null && CrmObjectApiNameEnum.CONTACT.getName().equals(object)) {
                        if (StringUtils.isNotBlank(contactObj.getString(field))) {
                            FieldDescribe fieldDescribe = crmV2Manager.getFieldDescribe(eieaConverter.enterpriseAccountToId(ea), CrmObjectApiNameEnum.CONTACT.getName(), field);
                            String type = fieldDescribe.getType();
                            replacement = formatValue(type, contactObj, field);
                        }
                    }
                    text = text.replace(target, replacement);
                }
            }
        } catch (Exception e) {
            log.warn("sendEmailMsg extractFields error, text:{}", text, e);
        }
        return text;
    }

    private String formatValue(String type, ObjectData objectData, String fieldName){
        if (objectData == null) {
            return "";
        }
        // 值为空，直接返回
        if (objectData.get(fieldName) == null) {
            return "";
        }
        String value = "";
        if (Objects.equals(type, "date")) {
            // 日期
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.longTimeConvertString(longValue);
            }
        } else if (Objects.equals(type, "date_time")) {
            // 日期和时间
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.format(longValue);
            }
        } else if (Objects.equals(type, "time")){
            // 时间
            if (objectData.getLong(fieldName) != null) {
                Long longValue = objectData.getLong(fieldName);
                value = DateUtil.format5(longValue);
            }
        } else if (Objects.equals(type, "percentile")) {
            // 百分数
            Double dataDouble = objectData.getDouble(fieldName);
            if (dataDouble != null) {
                value = dataDouble.doubleValue() + "%";
            }
        } else {
            value = Objects.toString(objectData.get(fieldName), "");
        }
        return value;
    }

    /**
     * 发送公众号模板消息
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendWxTemplateMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
        }
        List<UserMarketingWxServiceAccountRelationEntity> wxUserRelations = userMarketingWxServiceAccountRelationDao.getByUserMarketingId(triggerInstance.getMarketingUserId());
        List<String> wxOpenIds = wxUserRelations.stream().filter(Objects::nonNull).filter(r -> triggerTaskSnapshot.getWxAppId().equals(r.getWxAppId())).map(UserMarketingWxServiceAccountRelationEntity::getWxOpenId).collect(Collectors.toList());
        if (wxOpenIds.isEmpty()) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_465));
        }
        SendWxTemplateMsgArg wxTemplateMsg = triggerTaskSnapshot.getWxTemplateMsg();
        String redirectUrl = wxTemplateMsg.getRedirectUrl();
        if (!Strings.isNullOrEmpty(redirectUrl)) {
            redirectUrl = redirectUrl.replace("!!wxAppId!!", triggerTaskSnapshot.getWxAppId());
        }
        TemplateMessageVo.Data data = new TemplateMessageVo.Data();
        data.setFirst(new TemplateMessageVo.First(wxTemplateMsg.getMsgBody().getTitle(), wxTemplateMsg.getMsgBody().getTitleColor()));

        List<TemplateMessageVo.KeyNote> keyNotes = Lists.newArrayList();
        for (SendWxTemplateMsgArg.MsgItem msgItem : wxTemplateMsg.getMsgBody().getDataList()) {
            if (StringUtils.equals(msgItem.getKey(), "first")) {
                if (StringUtils.isNotEmpty(msgItem.getValue())) {
                    data.setFirst(new TemplateMessageVo.First(msgItem.getValue(), msgItem.getColor()));
                }
            } else if (StringUtils.equals(msgItem.getKey(), "remark")) {
                if (StringUtils.isNotEmpty(msgItem.getValue())) {
                    TemplateMessageVo.Remark remark = new TemplateMessageVo.Remark(msgItem.getValue(), msgItem.getColor());
                    data.setRemark(remark);
                }
            } else {
                keyNotes.add(new TemplateMessageVo.KeyNote(msgItem.getKey(), msgItem.getValue(), msgItem.getColor()));
            }
        }
        data.setKeyNotes(keyNotes);
        if (!Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
            if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
                data.getFirst().setValue(doReplaceConferenceContent(triggerInstance, data.getFirst().getValue(), null));
                for (TemplateMessageVo.KeyNote keyNote : data.getKeyNotes()) {
                    keyNote.setValue(doReplaceConferenceContent(triggerInstance, keyNote.getValue(), null));
                }
                if (data.getRemark() != null) {
                    data.getRemark().setValue(doReplaceConferenceContent(triggerInstance, data.getRemark().getValue(), null));
                }
                if (RedirectTypeEnum.CONFERENCE_MAIN_PAGE.getType().equals(wxTemplateMsg.getRedirectType())) {
                    SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, triggerInstance.getSceneType(), triggerInstance.getSceneTargetId(), triggerInstance.getTriggerId());
                    ActivityEntity conference = conferenceDAO.getConferenceById(sceneTrigger.getSceneTargetId());
                    redirectUrl = host + "/proj/page/marketing-page?id=" + conference.getActivityDetailSiteId() + "&marketingEventId=" + conference.getMarketingEventId()
                            + "&spreadChannel=wechat&type=1&targetObjectType=13&targetObjectId=" + sceneTrigger.getSceneTargetId() + "&ea=" + ea;
                } else {
                    redirectUrl = doReplaceConferenceContent(triggerInstance, redirectUrl, null);
                }
            }
            if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
                data.getFirst().setValue(doReplaceLiveContent(triggerInstance, data.getFirst().getValue()));
                for (TemplateMessageVo.KeyNote keyNote : data.getKeyNotes()) {
                    keyNote.setValue(doReplaceLiveContent(triggerInstance, keyNote.getValue()));
                }
                if (data.getRemark() != null) {
                    data.getRemark().setValue(doReplaceLiveContent(triggerInstance, data.getRemark().getValue()));
                }
                if (RedirectTypeEnum.LIVE_VIEW.getType().equals(wxTemplateMsg.getRedirectType())) {
                    redirectUrl = doReplaceLiveContent(triggerInstance, "{直播链接}");
                }
                if (RedirectTypeEnum.LIVE_SIGN.getType().equals(wxTemplateMsg.getRedirectType())) {
                    SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, triggerInstance.getSceneType(), triggerInstance.getSceneTargetId(), triggerInstance.getTriggerId());
                    MarketingLiveEntity live = marketingLiveDAO.getById(sceneTrigger.getSceneTargetId());
                    List<String> objectIds = contentMarketingEventMaterialRelationDAO.getApplyObjectIdsByMarketingEventId(ea, live.getMarketingEventId());
                    String objectId = live.getFormHexagonId();
                    if (!objectIds.isEmpty() && !objectIds.contains(objectId)) {
                        objectId = objectIds.get(0);
                    }
                    redirectUrl = host + "/proj/page/marketing-page?id=" + objectId + "&marketingEventId=" + live.getMarketingEventId() + "&spreadChannel=wechat&type=1&ea=" + ea;
                }
            }
            if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType())) {
                ObjectData mktDetail = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), triggerInstance.getSceneTargetId());
                // 活动参数替换
                if (mktDetail != null) {
                    for (TemplateMessageVo.KeyNote keyNote : data.getKeyNotes()) {
                        if (keyNote.getValue().contains("{市场活动名称}")) {
                            keyNote.setValue(keyNote.getValue().replace("{市场活动名称}", mktDetail.get("name") == null ? "暂无" : mktDetail.get("name").toString()));
                        }
                        if (keyNote.getValue().contains("{活动开始时间}")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            if (mktDetail.get("begin_time") != null) {
                                keyNote.setValue(keyNote.getValue().replace("{活动开始时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue()))));
                            }
                        }
                        if (keyNote.getValue().contains("{活动结束时间}")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            if (mktDetail.get("end_time") != null) {
                                keyNote.setValue(keyNote.getValue().replace("{活动结束时间}", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue()))));
                            }
                        }
                    }
                }
            }
            // 替换活动成员参数
            CampaignMergeDataEntity campaignMergeDataById = campaignMergeDataDAO.getCampaignMergeDataById(triggerInstance.getCampaignId());
            if (campaignMergeDataById != null) {
                Map<String, String> customFields = new HashMap<>();
                List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
                if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                    crmCustomerFields.forEach(e -> {
                        if (2 == e.getFieldProperty()) {
                            customFields.put(e.getFieldCaption(), e.getFieldName());
                        }
                    });
                }
                if (!customFields.isEmpty()) {
                    String campaignMembersObjId = campaignMergeDataById.getCampaignMembersObjId();
                    Map<String, String> detail = null;
                    if (StringUtils.isNotBlank(campaignMembersObjId)) {
                        detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                    }
                    for (Map.Entry<String, String> customField : customFields.entrySet()) {
                        if (detail == null) {
                            data.getFirst().setValue(data.getFirst().getValue().replace("{" + customField.getKey() + "}", "暂无"));
                            for (TemplateMessageVo.KeyNote keyNote : data.getKeyNotes()) {
                                keyNote.setValue(keyNote.getValue().replace("{" + customField.getKey() + "}", "暂无"));
                            }
                            if (data.getRemark() != null) {
                                data.getRemark().setValue(data.getRemark().getValue().replace("{" + customField.getKey() + "}", "暂无"));
                            }
                        } else {
                            Object value = detail.get(customFields.get(customField.getKey()));
                            data.getFirst().setValue(data.getFirst().getValue().replace("{" + customField.getKey() + "}", value == null ? "暂无" : value.toString()));
                            for (TemplateMessageVo.KeyNote keyNote : data.getKeyNotes()) {
                                keyNote.setValue(keyNote.getValue().replace("{" + customField.getKey() + "}", value == null ? "暂无" : value.toString()));
                            }
                            if (data.getRemark() != null) {
                                data.getRemark().setValue(data.getRemark().getValue().replace("{" + customField.getKey() + "}", value == null ? "暂无" : value.toString()));
                            }
                        }
                    }
                }
            }
        }
        if (MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(triggerInstance.getSceneType())) {
            String marketingEventId = triggerInstance.getSceneTargetId();
            redirectUrl = ReplaceUtil.replaceOrAppendMarketingEventId(redirectUrl, marketingEventId);
        }
        for (String wxOpenId : wxOpenIds) {
            TemplateMessageVo.Data dataToSend = BeanUtil.copyByGson(data, TemplateMessageVo.Data.class);
            dataToSend.getFirst().setValue(doReplaceWxNickName(ea, triggerTaskSnapshot.getWxAppId(), wxOpenId, dataToSend.getFirst().getValue()));
            for (TemplateMessageVo.KeyNote keyNote : dataToSend.getKeyNotes()) {
                keyNote.setValue(doReplaceWxNickName(ea, triggerTaskSnapshot.getWxAppId(), wxOpenId, keyNote.getValue()));
            }
            if (dataToSend.getRemark() != null) {
                dataToSend.getRemark().setValue(doReplaceWxNickName(ea, triggerTaskSnapshot.getWxAppId(), wxOpenId, dataToSend.getRemark().getValue()));
            }
            BatchTemplateMessageVo batchTemplateMessageVo = new BatchTemplateMessageVo(Lists.newArrayList(wxOpenId), triggerTaskSnapshot.getWxAppId(), wxTemplateMsg.getWxTemplateMsgId(), dataToSend);
            batchTemplateMessageVo.setUrl(redirectUrl);
            if (RedirectTypeEnum.MINI_APP.getType().equals(wxTemplateMsg.getRedirectType())) {
                TemplateMessageVo.MiniProgram miniProgram = new TemplateMessageVo.MiniProgram();
                miniProgram.setMiniAppId(wxTemplateMsg.getRedirectMiniAppId());
                miniProgram.setPagePath(wxTemplateMsg.getRedirectMiniAppPath());
                batchTemplateMessageVo.setMiniProgram(miniProgram);
            }
            ModelResult<Void> mr = wechatMessageService.sendTemplateMessageByOpenIdsAsync(batchTemplateMessageVo);
            if (!mr.isSuccess()) {
                return ExecuteResult.failResult(mr.getErrorMessage());
            }
        }
        return ExecuteResult.successResult();
    }

    /**
     * 发送公众号消息
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendWxMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
        }
        com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg msgArg = new com.facishare.wechat.dubborestouterapi.arg.SendCustomerServiceMsgArg();
        msgArg.setEa(ea);
        msgArg.setFsUserId(-10000);
        Integer msgType = null;
        if (SEND_WX_TEXT_MSG.getTriggerTaskType().equals(triggerTaskSnapshot.getTaskType())) {
            msgType = WxCustomerServiceMsgType.TEXT.getMsgType();
            msgArg.setContent(triggerTaskSnapshot.getWxMessageContent());
        }
        if (SEND_WX_IMAGE_MSG.getTriggerTaskType().equals(triggerTaskSnapshot.getTaskType())) {
            msgType = WxCustomerServiceMsgType.IMAGE.getMsgType();
            msgArg.setTNOrNPath(triggerTaskSnapshot.getWxMessageContent());
        }
        if (SEND_WX_GRAPHIC_MESSAGE.getTriggerTaskType().equals(triggerTaskSnapshot.getTaskType())) {
            msgType = WxCustomerServiceMsgType.MPNEWS.getMsgType();
            msgArg.setMediaId(triggerTaskSnapshot.getWxMessageContent());
        }
        if (SEND_WX_NEWS_MSG.getTriggerTaskType().equals(triggerTaskSnapshot.getTaskType())) {
            msgType = WxCustomerServiceMsgType.NEWS.getMsgType();
            WxNewsMessageContent wxNewsMessageContent = GsonUtil.fromJson(triggerTaskSnapshot.getWxMessageContent(), WxNewsMessageContent.class);
            if (wxNewsMessageContent.materialValid()) {
                Result<MaterialWxPresentMsg> result = materialService
                        .getMaterialWxPresentMsg(ea, NoticeContentTypeEnum.fromType(wxNewsMessageContent.getMaterialType()).toObjectType(), wxNewsMessageContent.getMaterialId());
                if (!result.isSuccess() || result.getData() == null) {
                    return ExecuteResult.failResult(result.getErrMsg());
                }
                msgArg.setSharePicUrl(result.getData().getSharePicUrl());
                msgArg.setContent(result.getData().getTitle());
                msgArg.setDescription(result.getData().getDescription());
                msgArg.setUrl(wxNewsMessageContent.getUrl());
            } else if (WxNewsMessageContent.isValid(wxNewsMessageContent)) {
                if (wxNewsMessageContent.getAPath() == null) {
                    wxNewsMessageContent.setAPath(groupMessageDefaultCoverPath);
                }
                String sharePicUrl = fileManager.getPictureShareUrl(ea, wxNewsMessageContent.getAPath(), false);
                if (Strings.isNullOrEmpty(sharePicUrl)) {
                    return ExecuteResult.failResult("SharePicUrl is empty");
                }
                msgArg.setSharePicUrl(sharePicUrl);
                msgArg.setContent(wxNewsMessageContent.getTitle());
                msgArg.setUrl(wxNewsMessageContent.getUrl());
                msgArg.setDescription(wxNewsMessageContent.getDescription());
            } else {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1877));
            }
        }
        Preconditions.checkState(msgType != null, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1880));
        msgArg.setType(msgType);
        List<UserMarketingWxServiceAccountRelationEntity> wxUserRelations = userMarketingWxServiceAccountRelationDao.getByUserMarketingId(triggerInstance.getMarketingUserId());
        if (CollectionUtils.isEmpty(wxUserRelations)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_465));
        }

        ModelResult<String> appIdResult = outerServiceWechatService.transWxAppIdAndFsEaToAppId(triggerTaskSnapshot.getWxAppId(), ea);
        if (!appIdResult.isSuccess() || Strings.isNullOrEmpty(appIdResult.getResult())) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1889) + appIdResult.getErrorMessage());
        }
        wxUserRelations.stream().filter(entity -> triggerTaskSnapshot.getWxAppId().equals(entity.getWxAppId())).forEach(entity -> {
            msgArg.setAppId(appIdResult.getResult());
            msgArg.setWxOpenId(entity.getWxOpenId());
            msgArg.setMsgId(UUIDUtil.getUUID());
            msgArg.setUrl(ReplaceUtil.replaceWxAppId(msgArg.getUrl(), triggerTaskSnapshot.getWxAppId()));
            msgArg.setUrl(ReplaceUtil.replaceMarketingActivityId(msgArg.getUrl(), ""));
            if (MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(triggerInstance.getSceneType())) {
                String marketingEventId = triggerInstance.getSceneTargetId();
                msgArg.setUrl(ReplaceUtil.replaceOrAppendMarketingEventId(msgArg.getUrl(), marketingEventId));
            }
            wechatMessageRestService.sendCustomerServiceMessage(msgArg);
        });
        return ExecuteResult.successResult();
    }

    /**
     * 发送短信
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult sendSmsMsg(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        List<String> userMarketingIds = this.marketingUserIdSopFilterAndUpsert(triggerTaskSnapshot, triggerInstance, true);
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950));
        }
        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setChannelType(ChannelTypeEnum.MARKETING_FLOW.getType());
        groupSenderArg.setEventType(SaveOrSendTypeEnum.SEND.getType());
        groupSenderArg.setType(MwSendTaskTypeEnum.IMMEDIATELY_SEND.getType());
        groupSenderArg.setEa(ea);
        String triggerId = triggerTaskSnapshot.getTriggerId();
        MarketingTriggerEntity marketingTriggerEntity = marketingTriggerDao.getById(triggerId, ea);
        if (marketingTriggerEntity != null) {
            Integer creator = marketingTriggerEntity.getCreator();
            groupSenderArg.setTaskCreatorId(Objects.equals(creator, -10000) ? -5000 : creator);
        }
        groupSenderArg.setUserId(-5000);
        groupSenderArg.setTemplateId(triggerTaskSnapshot.getSmsTemplateId());
        groupSenderArg.setSmsVarArgs(GsonUtil.fromJson(triggerTaskSnapshot.getSmsVars(), new TypeToken<List<SmsVarArg>>(){}.getType()));
        String marketingEventId = null;
        if (MarketingSceneType.CONFERENCE.getType().equals(triggerInstance.getSceneType())) {
            if (Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1926));
            }
            groupSenderArg.setGroupType(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType());
            groupSenderArg.setCampaignIds(Lists.newArrayList(triggerInstance.getCampaignId()));
            ActivityEntity conference = conferenceDAO.getConferenceById(triggerInstance.getSceneTargetId());
            marketingEventId = conference.getMarketingEventId();
        } else if (MarketingSceneType.LIVE.getType().equals(triggerInstance.getSceneType())) {
            if (Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1926));
            }
            groupSenderArg.setGroupType(SmsGroupTypeEnum.LIVE_ENROLL.getType());
            groupSenderArg.setCampaignIds(Lists.newArrayList(triggerInstance.getCampaignId()));
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(triggerInstance.getSceneTargetId());
            marketingEventId = marketingLiveEntity.getMarketingEventId();
        } else {
            String phone = null;
            if (!Strings.isNullOrEmpty(triggerInstance.getCampaignId())) {
                CampaignMergeDataEntity campaignMergeData = campaignMergeDataDAO.getCampaignMergeDataById(triggerInstance.getCampaignId());
                if (campaignMergeData != null) {
                    phone = campaignMergeData.getPhone();
                }
            }
            if (Strings.isNullOrEmpty(phone) && !Strings.isNullOrEmpty(triggerInstance.getMarketingUserId())) {
                UserMarketingAccountEntity userMarketingAccount = userMarketingAccountDAO.getById(triggerInstance.getMarketingUserId());
                if (userMarketingAccount != null) {
                    phone = userMarketingAccount.getPhone();
                }
            }
            if (Strings.isNullOrEmpty(phone)) {
                return ExecuteResult.failResult(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1955));
            }
            PhoneContentResult phoneContentResult = new PhoneContentResult();
            phoneContentResult.setPhone(phone);

            Map<String, String> paramMap = new HashMap<>();
            if (MarketingSceneType.MARKETING_EVENT.getType().equals(triggerInstance.getSceneType()) || MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(triggerInstance.getSceneType())) {
                marketingEventId = triggerInstance.getSceneTargetId();
                ObjectData mktDetail = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), triggerInstance.getSceneTargetId());
                // 活动参数替换
                if (mktDetail != null) {
                    paramMap.put("activity.name", mktDetail.get("name") == null ? I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996) : mktDetail.get("name").toString());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    if (mktDetail.get("begin_time") != null) {
                        paramMap.put("activity.startTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("begin_time")).longValue())));
                    }
                    if (mktDetail.get("end_time") != null) {
                        paramMap.put("activity.endTime", sdf.format(new Date(BigDecimal.valueOf((Double) mktDetail.get("end_time")).longValue())));
                    }
                    // 添加市场活动自定义字段
                    paramMap.putAll(mwSendManager.buildMktCustomizeFieldMap(ea, marketingEventId));
                }
            }

            CampaignMergeDataEntity campaignMergeData = campaignMergeDataDAO.getCampaignMergeDataById(triggerInstance.getCampaignId());
            if (campaignMergeData != null) {
                String campaignMembersObjId = campaignMergeData.getCampaignMembersObjId();
                Map<String, String> detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                if (detail != null) {
                    detail.forEach((k, v) -> {
                        if (v != null) {
                            paramMap.put(k, v);
                        } else {
                            paramMap.put(k, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                        }
                    });
                }
                // 添加活动成员参数
                paramMap.putAll(campaignMergeData.getParamValueMap());
            }

            phoneContentResult.setParamMap(paramMap);
            groupSenderArg.setPhones(Collections.singletonList(phoneContentResult));
        }
        groupSenderArg.setBusinessType("营销通(SOP)");
        groupSenderArg.setNodeType(SmsRecordNodeTypeEnum.OTHER.getNodeType());
        TriggerSnapshotEntity currentUseSnapshot = triggerSnapshotDao.getCurrentUseSnapshot(ea, triggerInstance.getTriggerId());
        groupSenderArg.setReceiver(currentUseSnapshot != null ? currentUseSnapshot.getName() : null);
        MwSmsTemplateEntity mwSmsTemplateEntity = smsTemplateManager.getSmsTemplate(ea, triggerTaskSnapshot.getSmsTemplateId());
        groupSenderArg.setSendNode(mwSmsTemplateEntity != null ? mwSmsTemplateEntity.getName() : null);
        groupSenderArg.setMarketingEventId(marketingEventId);
        Result<GroupSendResult> sendGroupSmsResult = sendService.sendGroupSms(groupSenderArg);
        if (sendGroupSmsResult.isSuccess()) {
            return ExecuteResult.successResult(sendGroupSmsResult.getData().getSmsSendId());
        } else {
            return ExecuteResult.failResult(sendGroupSmsResult.getErrCode() + sendGroupSmsResult.getErrMsg());
        }
    }

    /**
     * 打标签
     *
     * @param ea
     * @param triggerTaskSnapshot
     * @param triggerTaskInstance
     * @param triggerInstance
     * @return
     */
    public ExecuteResult addTag(String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerTaskInstanceEntity triggerTaskInstance, TriggerInstanceEntity triggerInstance) {
        if (triggerTaskSnapshot.getTagNameList() == null || triggerTaskSnapshot.getTagNameList().isEmpty()) {
            return new ExecuteResult(false, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_2021));
        }
        userMarketingAccountManager.batchAddTagsToUserMarketingAccount(ea, null, ImmutableList.of(triggerInstance.getMarketingUserId()), triggerTaskSnapshot.getTagNameList());
        return ExecuteResult.successResult();
    }

    /*
    public Integer getOwnerByUserMarketingIdAndRules(String ea, SendSopSetting sendSopSetting, String userMarketingId) {
        try {
            List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Collections.singletonList(userMarketingId));
            if (null != sendSopSetting && entities != null && !entities.isEmpty()) {
                String externalUserId = entities.get(0).getWxWorkExternalUserId();

                // 查询企微客户
                com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> pageResult = wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(eieaConverter.enterpriseAccountToId(ea), com.google.common.collect.Lists.newArrayList(externalUserId));
                if (!pageResult.isSuccess() || pageResult.getData() == null || org.apache.commons.collections.CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
                    log.warn("TriggerInstanceManager.getOwnerByRules fail, pageResult is null or pageResult.getData() is null, ea:{} externalUserId：{}", ea, externalUserId);
                }
                ObjectData objectData = pageResult.getData().getDataList().get(0);
                if (objectData != null) {
                    // 查询添加记录
                    PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                    paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                    PaasQueryArg query = new PaasQueryArg(0, 1);
                    query.addFilter("external_user_id", OperatorConstants.IN, Collections.singletonList(objectData.getId()));
                    paasQueryFilterArg.setQuery(query);
                    InnerPage<ObjectData> qwAddUserObjectResult = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
                    if (qwAddUserObjectResult != null && qwAddUserObjectResult.getDataList() != null && !qwAddUserObjectResult.getDataList().isEmpty()) {
                        List<String> qywxUserIds = new ArrayList<>();
                        List<Map<String, Object>> ownersByQwUserIds = new ArrayList<>();
                        for (Map<String, Object> objectMap : qwAddUserObjectResult.getDataList()) {
                            Object qywx_user_id = objectMap.get("qywx_user_id");
                            if (qywx_user_id != null) {
                                qywxUserIds.add(qywx_user_id.toString());
                                Map<String, Object> ownerMap = new HashMap<>();
                                Double addTime = (Double) objectMap.get("add_time");
                                ownerMap.put("addTime", addTime);
                                ownerMap.put("qyUserId", qywx_user_id);
                                ownersByQwUserIds.add(ownerMap);
                            }
                        }
                        if (!qywxUserIds.isEmpty()) {
                            Map<String, Integer> userMap = qywxUserManager.getFsUserIdByQyWxInfo(ea, qywxUserIds, true, true);
                            if (userMap != null) {
                                for (Map<String, Object> ownersByQwUserId : ownersByQwUserIds) {
                                    String qyUserId = String.valueOf(ownersByQwUserId.get("qyUserId"));
                                    Integer userId = userMap.get(qyUserId);
                                    ownersByQwUserId.put("userId", userId);
                                }
                            }
                            List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, qywxUserIds);
                            if (qyWxAddressBookEntities != null && !qyWxAddressBookEntities.isEmpty()) {
                                Map<String, QyWxAddressBookEntity> qyWxAddressBookMap = qyWxAddressBookEntities.stream()
                                        .collect(Collectors.toMap(QyWxAddressBookEntity::getUserId, Function.identity(), (v1, v2) -> v1));
                                for (Map<String, Object> ownersByQwUserId : ownersByQwUserIds) {
                                    String qyUserId = String.valueOf(ownersByQwUserId.get("qyUserId"));
                                    QyWxAddressBookEntity entity = qyWxAddressBookMap.get(qyUserId);
                                    if (entity != null) {
                                        List<String> departments = JSONArray.parseArray(entity.getDepartment(), String.class);
                                        if (departments != null) {
                                            departments = departments.stream().map(e -> String.valueOf(Integer.parseInt(e) + 10000)).collect(Collectors.toList());
                                        }
                                        ownersByQwUserId.put("departments", departments);
                                    }
                                }
                                List<Map<String, Object>> finallyList = ownersByQwUserIds.stream().filter(e -> {
                                    Object uid = e.get("userId");
                                    return uid != null && (Integer) uid < 100000000;
                                }).collect(Collectors.toList());
                                List<String> departmentIds = sendSopSetting.getDepartmentIds();
                                List<String> userId = sendSopSetting.getUserId();
                                if (departmentIds != null && userId != null) {
                                    finallyList = finallyList.stream()
                                            .filter(e -> {
                                                List<String> departments = new ArrayList<>();
                                                Object obj = e.get("departments");
                                                if (obj != null) {
                                                    departments = (List) obj;
                                                }
                                                return !Sets.intersection(new HashSet<>(departmentIds), new HashSet<>(departments)).isEmpty() || userId.contains(String.valueOf(e.get("userId")));
                                            })
                                            .collect(Collectors.toList());
                                }
                                List<String> firstPriority = sendSopSetting.getFirstPriority();
                                if (firstPriority != null) {
                                    firstPriority = firstPriority.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                                    for (Map<String, Object> item : finallyList) {
                                        int priority = -1;
                                        List<String> departments = (List<String>) item.get("departments");
                                        for (String d : departments) {
                                            int index = firstPriority.indexOf(d);
                                            priority = Math.max(index, priority);
                                        }
                                        item.put("priority", priority);
                                    }
                                    finallyList = finallyList.stream()
                                            .sorted(Comparator.comparingInt(o -> (Integer) o.get("priority")))
                                            .collect(Collectors.toList());
                                }
                                String secondPriority = sendSopSetting.getSecondPriority();
                                secondPriority = StringUtils.isEmpty(secondPriority) ? "ASC" : secondPriority;
                                if ("DESC".equals(secondPriority)) {
                                    finallyList = finallyList.stream().sorted(Comparator.comparingDouble(o -> (Double) ((Map) o).get("addTime")).reversed()).collect(Collectors.toList());
                                } else {
                                    finallyList = finallyList.stream().sorted(Comparator.comparingDouble(o -> (Double) o.get("addTime"))).collect(Collectors.toList());
                                }
                                if (!finallyList.isEmpty()) {
                                    Object uid = finallyList.get(0).get("userId");
                                    if (uid != null) {
                                        return (Integer) uid;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("TriggerInstanceManager.getOwnerByRules get owner fail ea:{} userMarketingId：{}", ea, userMarketingId);
        }
        return null;
    }
    */

    public String doReplaceConferenceContent(TriggerInstanceEntity triggerInstance, String contentToReplace, String email) {
        if (Strings.isNullOrEmpty(contentToReplace)){
            return contentToReplace;
        }
        Map<String, String> campaignIdToEnrollIdMap = campaignMergeDataManager.campaignIdToActivityEnrollIdMap(Lists.newArrayList(triggerInstance.getCampaignId()));
        if (!Strings.isNullOrEmpty(campaignIdToEnrollIdMap.get(triggerInstance.getCampaignId()))){
            List<ConferenceEnrollBaseInfoDTO> conferenceEnrollBaseInfoDTOList = activityEnrollDataDAO.queryConferenceEnrollBaseInfoByIds(Lists.newArrayList(campaignIdToEnrollIdMap.get(triggerInstance.getCampaignId())));
            if (!conferenceEnrollBaseInfoDTOList.isEmpty()){
                ConferenceEnrollBaseInfoDTO conferenceEnrollBaseInfoDTO = conferenceEnrollBaseInfoDTOList.get(0);
                conferenceEnrollBaseInfoDTO.setEnrollEmail(email);
                conferenceEnrollBaseInfoDTO.setStartTime(DateUtil.format(conferenceEnrollBaseInfoDTO.getStartTimeStamp()));
                conferenceEnrollBaseInfoDTO.setEndTime(DateUtil.format(conferenceEnrollBaseInfoDTO.getEndTimeStamp()));
                conferenceEnrollBaseInfoDTO.setActivityUrl(host + "/ec/cml-marketing/release/web/cml-marketing.html?conferenceId=" + conferenceEnrollBaseInfoDTO.getActivityId() + "&marketingEventId=" + conferenceEnrollBaseInfoDTO.getMarketingEventId() + "&byshare=1&_hash=/cml/h5/conference_detail");
                return ConferenceParamEnum.replaceContent(triggerInstance.getEa(), contentToReplace, ConferenceParamEnum.getNeedReplaceByContent(contentToReplace), conferenceEnrollBaseInfoDTO, host, triggerInstance.getCampaignId());
            }
        }
        return contentToReplace;
    }


    public void saveEmailSendRecord(String ea, String marketingEventId, String triggerId, List<String> mailList, SendEmailResult result) {
        ThreadPoolUtils.execute(() -> saveEmailSendRecordTask(ea, marketingEventId, triggerId, mailList, result), ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    public void saveEmailSendRecordTask(String ea, String marketingEventId, String triggerId, List<String> mailList, SendEmailResult result) {
        MailSendTaskEntity taskEntity = result.getTaskEntity();
        taskEntity.setMarketingEventId(marketingEventId);
        taskEntity.setSendRange(MailSendRangeEnum.MARKETING_USER_GROUP.getType());
        taskEntity.setScheduleType(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType());
        taskEntity.setFixTime(new Date().getTime());
        taskEntity.setSendStatus(MailSendStatusEnum.SEND_SUCCESS.getStatus());
        taskEntity.setMailType(MailApiUserTypeEnum.TRIGGER.getType());
        taskEntity.setCreateTime(new Date());
        taskEntity.setUpdateTime(new Date());
        taskEntity.setLabelId(result.getLabelId());
        MarketingTriggerEntity marketingTriggerEntity = marketingTriggerDao.getById(triggerId, ea);
        if (marketingTriggerEntity != null) {
            Integer creator = marketingTriggerEntity.getCreator();
            taskEntity.setFsUserId(creator);
        } else {
            taskEntity.setFsUserId(-10000);
        }
        taskEntity.setTotalSendCount(mailList.size());
        mailSendTaskDAO.insert(taskEntity);
        mailManager.addEmailTaskSchedule(ea, taskEntity.getId());
        //保存发送结果
        if (CollectionUtils.isNotEmpty(result.getMailIdList())) {
            List<MailSendTaskResultEntity> entities = Lists.newArrayList();
            for (String mailId : result.getMailIdList()) {
                MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(taskEntity.getEa());
                entity.setTaskId(taskEntity.getId());
                entity.setMailId(mailId);
                String[] mailRex = StringUtils.split(mailId, "$");
                if (mailRex != null && mailRex.length == 2) {
                    entity.setMail(mailRex[1]);
                }
                entity.setType(MailSendTypeEnum.SEND_BY_XSMTPAPI.getType());
                Date now = new Date();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entities.add(entity);
            }
            mailSendTaskResultDAO.batchInsert(entities);
        }

        for (String receiver : mailList) {
            CreateEmailSendRecordDetailObjArg createArg = new CreateEmailSendRecordDetailObjArg();
            createArg.setEa(ea);
            createArg.setTaskId(taskEntity.getId());
            createArg.setReceiver(receiver);
            createArg.setSendTime(new Date());
            createArg.setPreviewUrl(host + "/XV/UI/Home#/app/marketing/index/=/mail-marketing/preview?runtime=desktop&mailId=" + taskEntity.getId());
            createArg.setEa(createArg.getEa());
            createArg.setFsUserId(taskEntity.getFsUserId());
            if (StringUtils.isNotEmpty(marketingEventId)) {
                createArg.setMarketingEventId(marketingEventId);
            }
            createArg.setBusinessType("营销通(SOP)");
            TriggerSnapshotEntity currentUseSnapshot = triggerSnapshotDao.getCurrentUseSnapshot(ea, triggerId);
            createArg.setSendObject(currentUseSnapshot.getName());
            createArg.setSendNode(taskEntity.getSubject());
            emailSendRecordDetailObjManager.tryCreateOrUpdateObj(createArg);
        }
    }

    public AddMsgTemplateResult addMsgTemplate(String accessToken, AddNewMsgTemplateArg arg){
        if (Strings.isNullOrEmpty(accessToken) || arg == null) {
            return null;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_msg_template?access_token=" + accessToken;
        AddMsgTemplateResult result = httpManager.executePostHttp(arg, url, new TypeToken<AddMsgTemplateResult>(){});
        log.warn("addMsgTemplate result:{}", result);
        return result;
    }

    public String doReplaceWxNickName(String ea, String wxAppId, String wxOpenId, String contentToReplace){
        if (Strings.isNullOrEmpty(contentToReplace) || !contentToReplace.contains("{微信用户昵称}")){
            return contentToReplace;
        }
        Optional<String> wxNickName = crmV2Manager.getWxNickNameByCache(ea, wxAppId, wxOpenId);
        return wxNickName.map(s -> contentToReplace.replace("{微信用户昵称}", s)).orElse(contentToReplace);
    }

    public String doReplaceLiveContent(TriggerInstanceEntity triggerInstance, String contentToReplace) {
        if (Strings.isNullOrEmpty(contentToReplace)){
            return contentToReplace;
        }
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(triggerInstance.getSceneTargetId());
        if (marketingLiveEntity != null) {
            return LiveParamEnum.replaceContent(contentToReplace, LiveParamEnum.getNeedReplaceByContent(contentToReplace), marketingLiveEntity);
        }
        return contentToReplace;
    }

    public String getTargetName(TriggerSnapshotEntity triggerSnapshotEntity, TriggerInstanceEntity triggerInstance, String marketingUserId, String ea, Map<String, Object> paramMap) {
        String targetName = null;
        // 历史数据兼容
        String targetObjectId = null;
        Integer targetObjectType = null;
        if (triggerSnapshotEntity.getTargetObjects() == null || triggerSnapshotEntity.getTargetObjects().isEmpty()) {
            targetObjectId = triggerSnapshotEntity.getTargetObjectId();
            targetObjectType = triggerSnapshotEntity.getTargetObjectType();
        }
        if (StringUtils.isBlank(targetObjectId)) {
            targetObjectId = triggerInstance.getTargetObjectId();
            targetObjectType = triggerInstance.getTargetObjectType();
        }
        if (paramMap != null && StringUtils.isBlank(targetObjectId)) {
            targetObjectId = (String) paramMap.get("objectId");
            targetObjectType = (Integer) paramMap.get("objectType");
        }
        if (paramMap != null && (triggerSnapshotEntity.getTriggerActionType().equals(TriggerActionTypeEnum.MAIL_UNSUBSCRIBE.getTriggerActionType()) || triggerSnapshotEntity.getTriggerActionType().equals(TriggerActionTypeEnum.MAIL_REPORT_SPAM.getTriggerActionType()))) {
            if (!Strings.isNullOrEmpty(targetObjectId)) {
                MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(targetObjectId);
                List<String> senderIds = GsonUtil.getGson().fromJson(mailSendTaskEntity.getSenderIds(), new TypeToken<List<String>>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(senderIds)) {
                    MailSendReplyEntity mailSendReplyEntity = mailSendReplyDAO.getById(senderIds.get(0));
                    targetName = mailSendReplyEntity != null ? mailSendReplyEntity.getAddress() : "";
                }
            }
        }
        if (Strings.isNullOrEmpty(targetName)) {
            targetName = objectManager.getObjectName(targetObjectId, targetObjectType);
        }
        if (Strings.isNullOrEmpty(targetName)) {
            targetName = sceneTriggerManager.getEventName(triggerInstance, ea);
        }
        return targetName == null ? "" : targetName;
    }

    public String getTriggerActionName(TriggerSnapshotEntity triggerSnapshotEntity) {
        String actionName = null;
        if (triggerSnapshotEntity.getFrontEndExtensions() != null) {
            actionName = (String) triggerSnapshotEntity.getFrontEndExtensions().get("triggerRuleDescription");
        }
        if (Strings.isNullOrEmpty(actionName)) {
            actionName = Strings.isNullOrEmpty(triggerSnapshotEntity.getTriggerActionType()) ? TriggerTimeTypeEnum.getNameByTriggerTimeType(triggerSnapshotEntity.getTriggerTimeType()) + I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_2271) : TriggerActionTypeEnum.getNameByTriggerActionType(triggerSnapshotEntity.getTriggerActionType());
        }
        return actionName;
    }

    public Integer getOwnerByRules(String ea, SendSopSetting sendSopSetting, String externalUserId) {
        try {
            // 查询企微客户
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> pageResult = wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(eieaConverter.enterpriseAccountToId(ea),
                    Lists.newArrayList(externalUserId));
            if (!pageResult.isSuccess() || pageResult.getData() == null || CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
                log.warn("TriggerInstanceManager.getOwnerByRules fail, pageResult is null or pageResult.getData() is null, ea:{} externalUserId：{}", ea, externalUserId);
            }
            ObjectData objectData = pageResult.getData().getDataList().get(0);
            if (objectData != null) {
                // 查询添加记录
                PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                PaasQueryArg query = new PaasQueryArg(0, 1);
                query.addFilter("external_user_id", OperatorConstants.IN, Collections.singletonList(objectData.getId()));
                paasQueryFilterArg.setQuery(query);
                InnerPage<ObjectData> qwAddUserObjectResult = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
                if (qwAddUserObjectResult != null && qwAddUserObjectResult.getDataList() != null && !qwAddUserObjectResult.getDataList().isEmpty()) {
                    List<String> qywxUserIds = new ArrayList<>();
                    List<Map<String, Object>> ownersByQwUserIds = new ArrayList<>();
                    for (Map<String, Object> objectMap : qwAddUserObjectResult.getDataList()) {
                        Object qywx_user_id = objectMap.get("qywx_user_id");
                        if (qywx_user_id != null) {
                            qywxUserIds.add(qywx_user_id.toString());
                            Map<String, Object> ownerMap = new HashMap<>();
                            Double addTime = (Double) objectMap.get("add_time");
                            ownerMap.put("addTime", addTime);
                            ownerMap.put("qyUserId", qywx_user_id);
                            ownersByQwUserIds.add(ownerMap);
                        }
                    }
                    if (!qywxUserIds.isEmpty()) {
                        Map<String, Integer> userMap = qywxUserManager.getFsUserIdByQyWxInfo(ea, qywxUserIds, true, true);
                        if (userMap != null) {
                            for (Map<String, Object> ownersByQwUserId : ownersByQwUserIds) {
                                String qyUserId = String.valueOf(ownersByQwUserId.get("qyUserId"));
                                Integer userId = userMap.get(qyUserId);
                                ownersByQwUserId.put("userId", userId);
                            }
                        }
                        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, qywxUserIds);
                        if (qyWxAddressBookEntities != null && !qyWxAddressBookEntities.isEmpty()) {
                            Map<String, QyWxAddressBookEntity> qyWxAddressBookMap = qyWxAddressBookEntities.stream()
                                    .collect(Collectors.toMap(QyWxAddressBookEntity::getUserId, Function.identity(), (v1, v2) -> v1));
                            for (Map<String, Object> ownersByQwUserId : ownersByQwUserIds) {
                                String qyUserId = String.valueOf(ownersByQwUserId.get("qyUserId"));
                                QyWxAddressBookEntity entity = qyWxAddressBookMap.get(qyUserId);
                                if (entity != null) {
                                    List<String> departments = JSONArray.parseArray(entity.getDepartment(), String.class);
                                    if (departments != null) {
                                        departments = departments.stream().map(e -> String.valueOf(Integer.parseInt(e) + 10000)).collect(Collectors.toList());
                                    }
                                    ownersByQwUserId.put("departments", departments);
                                }
                            }
                            List<Map<String, Object>> finallyList = ownersByQwUserIds.stream().filter(e -> {
                                Object uid = e.get("userId");
                                return uid != null && (Integer) uid < 100000000;
                            }).collect(Collectors.toList());
                            List<String> departmentIds = sendSopSetting.getDepartmentIds();
                            List<String> userId = sendSopSetting.getUserId();
                            if (departmentIds != null && userId != null) {
                                finallyList = finallyList.stream()
                                        .filter(e -> {
                                            List<String> departments = new ArrayList<>();
                                            Object obj = e.get("departments");
                                            if (obj != null) {
                                                departments = (List) obj;
                                            }
                                            return !Sets.intersection(new HashSet<>(departmentIds), new HashSet<>(departments)).isEmpty() || userId.contains(String.valueOf(e.get("userId")));
                                        })
                                        .collect(Collectors.toList());
                            }
                            List<String> firstPriority = sendSopSetting.getFirstPriority();
                            if (CollectionUtils.isNotEmpty(firstPriority)) {
                                firstPriority = firstPriority.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                                for (Map<String, Object> item : finallyList) {
                                    int priority = -1;
                                    List<String> departments = (List<String>) item.get("departments");
                                    for (String d : departments) {
                                        int index = firstPriority.indexOf(d);
                                        priority = Math.max(index, priority);
                                    }
                                    item.put("priority", priority);
                                }
                                finallyList = finallyList.stream()
                                        .sorted(Comparator.comparingInt(o -> (Integer) o.get("priority")))
                                        .collect(Collectors.toList());
                            }
                            String secondPriority = sendSopSetting.getSecondPriority();
                            secondPriority = StringUtils.isEmpty(secondPriority) ? "ASC" : secondPriority;
                            if ("DESC".equals(secondPriority)) {
                                finallyList = finallyList.stream().sorted(Comparator.comparingDouble(o -> (Double) ((Map) o).get("addTime")).reversed()).collect(Collectors.toList());
                            } else {
                                finallyList = finallyList.stream().sorted(Comparator.comparingDouble(o -> (Double) o.get("addTime"))).collect(Collectors.toList());
                            }
                            if (!finallyList.isEmpty()) {
                                Object uid = finallyList.get(0).get("userId");
                                if (uid != null) {
                                    return (Integer) uid;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("TriggerInstanceManager.getOwnerByRules get owner fail ea:{} externalUserId：{}", ea, externalUserId);
        }
        return null;
    }

    public List<String> getExternalUserIds(String ea, TriggerSnapshotEntity triggerSnapshot) {
        List<String> externalUserIds = Lists.newArrayList();
        try {
            MarketingTriggerEntity marketingTrigger = marketingTriggerDao.getById(triggerSnapshot.getTriggerId(), ea);
            Integer fsUserId = marketingTrigger.getCreator();
            if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.MARKETING_USER_GROUP.getType()) {
                List<String> marketingUserGroupIds = GsonUtil.fromJson(triggerSnapshot.getMarketingUserGroupIds(), new TypeToken<List<String>>() {
                }.getType());
                externalUserIds = marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(triggerSnapshot.getEa(), marketingUserGroupIds);
            } else if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.FILTER.getType()
                    || triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.ALL.getType()) {
                List<PaasQueryArg.Condition> filters = JSONArray.parseArray(triggerSnapshot.getFilters(), PaasQueryArg.Condition.class);
                PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg query = new PaasQueryArg(0, 1);
                if (CollectionUtils.isNotEmpty(filters)) {
                    query.setFilters(filters);
                }
                boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
                if (isOpen) {
                    List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                    query.addFilter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                }
                query.addFilter("record_type", "EQ", Lists.newArrayList("default__c"));
                List<String> externalUserid = Lists.newArrayList();
                paasQueryFilterArg.setQuery(query);
                paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                crmV2Manager.listCrmObjectScanByIdAndHandle(ea, -10000, paasQueryFilterArg, 1000, objectData -> {
                    if ((objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName()) != null && objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName())
                            .equals("normal")) || objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName()) == null) {
                        externalUserid.add(objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) + "");
                    }
                });
                externalUserIds = externalUserid;
            } else {
                List<TagName> tagList = GsonUtil.fromJson(triggerSnapshot.getTagIdList(), new TypeToken<List<TagName>>() {
                }.getType());
                //标签筛选
                if (CollectionUtils.isEmpty(tagList)) {
                    return externalUserIds;
                }
                List<String> externalUserid = new ArrayList<>();
                List<String> apiNameList = Lists.newArrayList();
                apiNameList.add(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                List<String> userMarketingAccountIds = userMarketingAccountManager.listUserMarketingAccountIdsByTagNameList(triggerSnapshot.getEa(), fsUserId,
                        apiNameList, OperatorConstants.IN, tagList, 0, 10000);
                if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
                    for (List<String> tmp : Lists.partition(userMarketingAccountIds, 1000)) {
                        List<UserMarketingWxWorkExternalUserRelationEntity> wxWorkExternalUserRelationEntityList =
                                userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(triggerSnapshot.getEa(), tmp);
                        if (CollectionUtils.isNotEmpty(wxWorkExternalUserRelationEntityList)) {
                            wxWorkExternalUserRelationEntityList.forEach(entity -> {
                                externalUserid.add(entity.getWxWorkExternalUserId());
                            });
                        }
                    }
                }
                externalUserIds = externalUserid;
            }
        } catch (Exception e) {
            log.warn("GroupSendMessageManager getExternalUserIds exception, groupSendTask={}, exception={}", triggerSnapshot, e.fillInStackTrace());
        }

        return externalUserIds;
    }

    public <T> Set<String> getMarketingAccountUserIds(String ea, TriggerSnapshotEntity triggerSnapshot, Map<Integer, List<String>> map) {
        try {
            Map<String, String> externalIdToMarketingUserIdMap;
            MarketingTriggerEntity marketingTrigger = marketingTriggerDao.getById( triggerSnapshot.getTriggerId(),ea);
            Integer fsUserId = marketingTrigger.getCreator();
            if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.MARKETING_USER_GROUP.getType()) {
                List<String> marketingUserGroupIds = GsonUtil.fromJson(triggerSnapshot.getMarketingUserGroupIds(), new TypeToken<List<String>>() {
                }.getType());
                List<String> externalUserid = marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(triggerSnapshot.getEa(), marketingUserGroupIds);
                if (CollectionUtil.isEmpty(externalUserid)) {
                    return new HashSet<>();
                }
                List<UserMarketingWxWorkExternalUserRelationEntity> entities = Lists.newArrayList();
                List<List<String>> partitionList = Lists.partition(externalUserid, 1000);
                for (List<String> partition : partitionList) {
                    entities.addAll(userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(ea, partition));
                }
                if (null != map && !CollectionUtil.isEmpty(entities)) {
                    externalIdToMarketingUserIdMap = new HashMap<>();
                    for (UserMarketingWxWorkExternalUserRelationEntity entity : entities) {
                        externalIdToMarketingUserIdMap.put(entity.getWxWorkExternalUserId(),entity.getUserMarketingId());
                    }
                    getOwnerToMarketingUserMap(map, externalUserid, ea, externalIdToMarketingUserIdMap);
                }
                return entities.stream().map(en -> en.getUserMarketingId()).collect(Collectors.toSet());
            } else if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.FILTER.getType()
                    || triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.ALL.getType()) {
                List<PaasQueryArg.Condition> filters = JSONArray.parseArray(triggerSnapshot.getFilters(), PaasQueryArg.Condition.class);
                PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg query = new PaasQueryArg(0, 1);
                if (CollectionUtils.isNotEmpty(filters)) {
                    query.setFilters(filters);
                }
                boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
                if (isOpen) {
                    List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                    query.addFilter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                }
                List<String> externalUserId = Lists.newArrayList();
                paasQueryFilterArg.setQuery(query);
                paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                crmV2Manager.listCrmObjectScanByIdAndHandle(ea, -10000, paasQueryFilterArg, 1000, objectData -> {
                    if ((objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName()) != null && objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName())
                            .equals("normal")) || objectData.get(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName()) == null) {
                        externalUserId.add(objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) + "");
                    }
                });
                List<UserMarketingWxWorkExternalUserRelationEntity> entities = Lists.newArrayList();
                List<List<String>> partition = Lists.partition(externalUserId, 1000);
                for (List<String> part : partition) {
                    List<UserMarketingWxWorkExternalUserRelationEntity> tmpList = userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(ea, part);
                    if (CollectionUtils.isNotEmpty(tmpList)) {
                        entities.addAll(tmpList);
                    }
                }
                if (null != map && !CollectionUtil.isEmpty(externalUserId)) {
                    externalIdToMarketingUserIdMap = new HashMap<>();
                    for (UserMarketingWxWorkExternalUserRelationEntity entity : entities) {
                        externalIdToMarketingUserIdMap.put(entity.getWxWorkExternalUserId(),entity.getUserMarketingId());
                    }
                    getOwnerToMarketingUserMap(map, externalUserId, ea, externalIdToMarketingUserIdMap);
                }
                return entities.stream().map(en -> en.getUserMarketingId()).collect(Collectors.toSet());

            } else if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
                Set<String> groupUserIds = Sets.newHashSet();
                try {
                    groupUserIds = GsonUtil.fromJson(triggerSnapshot.getGroupMsgSenderIds(), Set.class);
                } catch (Exception e) {
                    log.warn("MarketingSceneServiceImpl getMarketingAccountUserIds error", triggerSnapshot.getGroupMsgSenderIds());
                }
                return groupUserIds;
            } else {
                List<TagName> tagList = GsonUtil.fromJson(triggerSnapshot.getTagIdList(), new TypeToken<List<TagName>>() {
                }.getType());
                //标签筛选
                if (CollectionUtils.isEmpty(tagList)) {
                    return new HashSet<>();
                }
                List<String> externalUserid = new ArrayList<>();
                List<String> apiNameList = Lists.newArrayList();
                apiNameList.add(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                List<String> userMarketingAccountIds = userMarketingAccountManager.listUserMarketingAccountIdsByTagNameList(triggerSnapshot.getEa(), fsUserId,
                        apiNameList, OperatorConstants.IN, tagList, 0, 10000);
                if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
                    for (List<String> tmp : Lists.partition(userMarketingAccountIds, 1000)) {
                        List<UserMarketingWxWorkExternalUserRelationEntity> wxWorkExternalUserRelationEntityList =
                                userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(triggerSnapshot.getEa(), tmp);
                        if (CollectionUtils.isNotEmpty(wxWorkExternalUserRelationEntityList)) {
                            wxWorkExternalUserRelationEntityList.forEach(entity -> {
                                externalUserid.add(entity.getWxWorkExternalUserId());
                            });
                        }
                    }
                }
                List<UserMarketingWxWorkExternalUserRelationEntity> entities = Lists.newArrayList();
                for (List<String> tmp : Lists.partition(userMarketingAccountIds, 1000)) {
                    entities.addAll(userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(ea, tmp));
                }
                if (null != map && !CollectionUtil.isEmpty(externalUserid)) {
                    externalIdToMarketingUserIdMap = new HashMap<>();
                    for (UserMarketingWxWorkExternalUserRelationEntity entity : entities) {
                        externalIdToMarketingUserIdMap.put(entity.getWxWorkExternalUserId(),entity.getUserMarketingId());
                    }
                    getOwnerToMarketingUserMap(map, externalUserid, ea, externalIdToMarketingUserIdMap);
                }
                if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
                    return new HashSet<>(userMarketingAccountIds);
                }
            }
        } catch (Exception e) {
            log.warn("GroupSendMessageManager getExternalUserIds exception, groupSendTask={}, exception={}", triggerSnapshot, e.fillInStackTrace());
        }

        return new HashSet<>();
    }

    //负责人所负责的客户
    public void getOwnerToMarketingUserMap(Map<Integer, List<String>> map, List<String> externalUserid, String ea, Map<String, String> externalIdToMarketingUserId) {
        Map<Integer, List<String>> ownerToExtenalUserMap = userMarketingAccountManager.getOwnerToExtenalUserMap(ea, externalUserid);
        ownerToExtenalUserMap.forEach((woner, externalUserList) -> {
            List<String> marketingUserId = new ArrayList<>();
            for (String externalId : externalUserList) {
                if (externalIdToMarketingUserId.containsKey(externalId)) {
                    marketingUserId.add(externalIdToMarketingUserId.get(externalId));
                }
            }
            map.put(woner, marketingUserId);
        });
    }

    public List<String> batchMarketingUserIdSopFilterAndUpsert(TriggerTaskSnapshotEntity triggerTaskSnapshot, List<String> filterBefor, boolean isUpsert) {
        return this.sopFilterAndUpsert(filterBefor, triggerTaskSnapshot, isUpsert);
    }

    public List<String> marketingUserIdSopFilterAndUpsert(TriggerTaskSnapshotEntity triggerTaskSnapshot, TriggerInstanceEntity triggerInstance, boolean isUpsert) {
        return this.sopFilterAndUpsert(Lists.newArrayList(triggerInstance.getMarketingUserId()), triggerTaskSnapshot, isUpsert);
    }

    public List<String> sopFilterAndUpsert(List<String> filterBefor, TriggerTaskSnapshotEntity triggerTaskSnapshot, boolean isUpsert) {
        FlexibleJson properties = triggerTaskSnapshot.getProperty() == null ? new FlexibleJson() : triggerTaskSnapshot.getProperty();
        JSONObject property = JSONObject.parseObject(JSONObject.toJSONString(properties));
        Integer filterNDaySentUser = property.getInteger("filterNDaySentUser");
        int customSpreadType = MarketingActivityActionEnum.getCustomSpreadType(triggerTaskSnapshot.getId());
        List<String> filterAfter = enterpriseSpreadRecordManager.filterList(filterBefor, customSpreadType, triggerTaskSnapshot.getEa(), filterNDaySentUser);
        if (isUpsert) {
            enterpriseSpreadRecordManager.upsertList(filterAfter, customSpreadType, triggerTaskSnapshot.getEa());
        }
        log.info("sopFilterAndUpsert ea:{} triggerTaskSnapshotId:{} beforSize:{} afterSize:{}", triggerTaskSnapshot.getEa(), triggerTaskSnapshot.getId(), filterBefor.size(), filterAfter.size());
        return filterAfter;
    }

}