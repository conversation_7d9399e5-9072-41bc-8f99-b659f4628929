package com.facishare.marketing.provider.dao.sms.mw;

import com.facishare.marketing.provider.entity.sms.mw.MwAccountEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by ranluch on 2019/9/29.
 */
public interface MwAccountDao {
    @Select("select * from mw_account where id = #{id}")
    MwAccountEntity queryAccount(@Param("id") String id);

    // 使用 queryAccountByTypeAndIndustry 代替
    @Deprecated
    @Select("select * from mw_account where type = #{type} and industry = '0'")
    MwAccountEntity queryAccountByType(@Param("type") Integer type);

    @Select("select * from mw_account where type = #{type} and industry = #{industry}")
    MwAccountEntity queryAccountByTypeAndIndustry(@Param("type") Integer type, @Param("industry") Integer industry);

    @Select("select * from mw_account where status = #{status}")
    List<MwAccountEntity> queryAccountByStatus(@Param("status") Integer status);

    /** 不走审核的账号：普通行业/生产账号 */
    @Select("select * from mw_account where type = '1' and industry = '0'")
    MwAccountEntity getDefaultAccount();
}
