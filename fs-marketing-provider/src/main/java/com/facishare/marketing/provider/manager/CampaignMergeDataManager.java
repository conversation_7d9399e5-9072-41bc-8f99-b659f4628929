package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.CrmFieldResult;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.vo.conference.AddCampaignMembersObjVO.MemberObjDetail;
import com.facishare.marketing.api.vo.conference.ChangeConferenceParticipantsReviewStatusVO;
import com.facishare.marketing.api.vo.conference.DeleteParticipantVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.CampaignMembersConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.campaign.CampaignConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.*;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationUserDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO;
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignDataMailDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationUserEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity;
import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created  By zhoux 2020/10/15
 * @IgnoreI18nFile
 **/
@Component
@Slf4j
public class CampaignMergeDataManager {

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private CustomizeTicketDAO customizeTicketDAO;

    @Autowired
    private WxTicketReceiveDAO wxTicketReceiveDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private ConferenceInvitationUserDAO conferenceInvitationUserDAO;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;
    @Autowired
    private CampaignPayOrderDao campaignPayOrderDao;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private TriggerInstanceManager triggerInstanceManager;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private ConferenceService conferenceService;

    @ReloadableProperty("campaign.status.enable")
    private String campaignStatusEnable;

    public static final String MARKETING_CAMPAIGN_DATA_LOCK_KEY = "MARKETING_CAMPAIGN_DATA_LOCK";

    public String addCampaignMergeDataByUserEnroll(String formDataUserId, Boolean needCreateDataIfNoBindCRM) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(formDataUserId);
        if (customizeFormDataUserEntity == null) {
            return null;
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId());
        if (customizeFormDataEntity == null) {
            return null;
        }
        String marketingEventId = customizeFormDataUserEntity.getMarketingEventId();
        if (StringUtils.isBlank(marketingEventId) && (customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY.getType())
            || customizeFormDataUserEntity.getObjectType().equals(ObjectTypeEnum.ACTIVITY_INVITATION.getType()))) {
            String activityId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getMarketingEventId());
            ActivityEntity activityEntity = activityDAO.getById(activityId);
            marketingEventId = activityEntity != null ? activityEntity.getMarketingEventId() : null;
        }
        if (StringUtils.isBlank(marketingEventId)) {
            return null;
        }
        Map<String, String> bindObjectMap = customizeFormDataManager.getFormEnrollDataBindObject(customizeFormDataUserEntity);
        String campaignMergeDataId = UUIDUtil.getUUID();
        Boolean bindOldData = false;
        Boolean createNewData = false;
        //是否开启不合并相同手机号线索(不开启手机号查重)
        boolean openMergePhone = false;
        //查询手机号合并设置
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(customizeFormDataEntity.getEa());
        if (marketingEventCommonSettingEntity != null && marketingEventCommonSettingEntity.getOpenMergePhone() != null && marketingEventCommonSettingEntity.getOpenMergePhone()) {
            openMergePhone = true;
        }
        if (MapUtils.isNotEmpty(bindObjectMap)) {
            Integer bindObjectType = CampaignMergeDataObjectTypeEnum.getTypeByName(bindObjectMap.keySet().iterator().next());
            // 绑定对象查询总表中是否有该对象数据
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(customizeFormDataEntity.getEa(), marketingEventId, bindObjectType, bindObjectMap.values().iterator().next());
            if (campaignMergeDataEntity == null) {
                if (customizeFormDataUserEntity.getSubmitContent() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
                    // 查询总表中是否具有相同手机号
                    List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO
                        .getCampaignMergeDataByPhone(customizeFormDataEntity.getEa(), marketingEventId, customizeFormDataUserEntity.getSubmitContent().getPhone(), true);
                    if (!openMergePhone && CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                        CampaignMergeDataEntity needBindCampaignData = getCampaignMergeDataByRule(campaignMergeDataEntityList);
                        campaignMergeDataId = needBindCampaignData.getId();
                        bindOldData = true;
                    } else {
                        // 创建表数据创建活动成员数据
                        CampaignMergeDataEntity saveData = new CampaignMergeDataEntity();
                        saveData.setId(campaignMergeDataId);
                        saveData.setEa(customizeFormDataEntity.getEa());
                        saveData.setMarketingEventId(marketingEventId);
                        saveData.setName(customizeFormDataUserEntity.getSubmitContent().getName());
                        saveData.setPhone(customizeFormDataUserEntity.getSubmitContent().getPhone());
                        saveData.setBindCrmObjectType(bindObjectType);
                        saveData.setBindCrmObjectId(bindObjectMap.values().iterator().next());
                        saveData.setCreateTime(new Date());
                        saveData.setUpdateTime(new Date());
                        saveData.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
                        CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
                        if (customizeFormDataEnroll != null) {
                            saveData.setMarketingPromotionSourceId(customizeFormDataEnroll.getMarketingPromotionSourceId());
                        }
                        campaignMergeDataId = saveCampaignDataAndAddCrmObj(saveData, true, CampaignMembersObjMemberStatusEnum.REGISTERED, customizeFormDataUserEntity.getCampaignId(),customizeFormDataUserEntity.getSpreadFsUid(),customizeFormDataEntity,customizeFormDataUserEntity);
                        createNewData = true;
                    }
                } else {
                    // 没有手机且没有对象数据需要创建活动成员（手机从对应对象中获取）
                    ObjectData objectData = null;
                    try {
                        objectData = crmV2Manager.getDetail(customizeFormDataEntity.getEa(), -10000, bindObjectMap.keySet().iterator().next(), bindObjectMap.values().iterator().next());
                    } catch (Exception e) {
                        log.warn("CampaignMergeDataManager.addCampaignMergeDataByUserEnroll error crmV2Manager.getDetail, e:{}", e);
                    }
                    CampaignMergeDataEntity saveData = new CampaignMergeDataEntity();
                    saveData.setId(campaignMergeDataId);
                    saveData.setEa(customizeFormDataEntity.getEa());
                    saveData.setMarketingEventId(marketingEventId);
                    saveData.setName(customizeFormDataUserEntity.getSubmitContent().getName());
                    saveData.setPhone(getPhoneByObject(objectData));
                    saveData.setBindCrmObjectType(bindObjectType);
                    saveData.setBindCrmObjectId(bindObjectMap.values().iterator().next());
                    saveData.setCreateTime(new Date());
                    saveData.setUpdateTime(new Date());
                    saveData.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
                    campaignMergeDataId = saveCampaignDataAndAddCrmObj(saveData, true, CampaignMembersObjMemberStatusEnum.REGISTERED, customizeFormDataUserEntity.getCampaignId(),customizeFormDataUserEntity.getSpreadFsUid(),customizeFormDataEntity,customizeFormDataUserEntity);
                    createNewData = true;
                }
            } else {
                bindOldData = true;
                campaignMergeDataId = campaignMergeDataEntity.getId();
            }
        } else {
            boolean needCreateData = true;
            campaignMergeDataId = UUIDUtil.getUUID();
            // 查询总表中是否有相同手机号且绑定了对象的数据
            if (customizeFormDataUserEntity.getSubmitContent() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
                List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO
                    .getCampaignMergeDataByPhone(customizeFormDataEntity.getEa(), marketingEventId, customizeFormDataUserEntity.getSubmitContent().getPhone(), true);
                if (!openMergePhone && CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                    needCreateData = false;
                    CampaignMergeDataEntity needBindCampaignData = getCampaignMergeDataByRule(campaignMergeDataEntityList);
                    campaignMergeDataId = needBindCampaignData.getId();
                    // 报名数据绑定crm对象
                    bindFormDataUserCrmObj(needBindCampaignData, customizeFormDataUserEntity);
                    bindOldData = true;
                }
            }
            if (needCreateData && needCreateDataIfNoBindCRM) {
                // 创建表数据但不创建活动成员数据
                CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
                campaignMergeDataEntity.setId(campaignMergeDataId);
                campaignMergeDataEntity.setEa(customizeFormDataEntity.getEa());
                campaignMergeDataEntity.setMarketingEventId(marketingEventId);
                campaignMergeDataEntity.setName(customizeFormDataUserEntity.getSubmitContent().getName());
                campaignMergeDataEntity.setPhone(customizeFormDataUserEntity.getSubmitContent().getPhone());
                campaignMergeDataEntity.setCreateTime(new Date());
                campaignMergeDataEntity.setUpdateTime(new Date());
                campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
                CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
                if (customizeFormDataEnroll != null) {
                    campaignMergeDataEntity.setMarketingPromotionSourceId(customizeFormDataEnroll.getMarketingPromotionSourceId());
                }
                campaignMergeDataId = saveCampaignDataAndAddCrmObj(campaignMergeDataEntity, false, null, customizeFormDataUserEntity.getCampaignId(),customizeFormDataUserEntity.getSpreadFsUid(),customizeFormDataEntity,customizeFormDataUserEntity);
                createNewData = true;
            }
        }
        // 若在没有创建表数据且没有绑定新数据时直接返回原总表id
        if (!needCreateDataIfNoBindCRM && !bindOldData && !createNewData) {
            return customizeFormDataUserEntity.getCampaignId();
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId()) && !customizeFormDataUserEntity.getCampaignId().equals(campaignMergeDataId)) {
            if (bindOldData) {
                // 绑定了旧数据
                campaignMergeDataDAO.deleteCampaignMergeData(customizeFormDataUserEntity.getCampaignId());
                customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(customizeFormDataUserEntity.getId(), campaignMergeDataId);
                // 切换之前绑定关联表id
                activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(customizeFormDataUserEntity.getCampaignId());
                customizeTicketDAO.deleteCustomizeTicketFormDataUser(customizeFormDataUserEntity.getCampaignId());
            } else {
                // 未绑定旧数据
                campaignMergeDataDAO.deleteCampaignMergeData(customizeFormDataUserEntity.getCampaignId());
                customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(customizeFormDataUserEntity.getId(), campaignMergeDataId);
                // 切换之前绑定关联表id
                activityEnrollDataDAO.updateActivityEnrollDataFormDataUser(campaignMergeDataId, customizeFormDataUserEntity.getCampaignId());
                customizeTicketDAO.updateCustomizeTicketFormDataUser(campaignMergeDataId, customizeFormDataUserEntity.getCampaignId());
            }
        } else {
            // 绑定报名数据
            customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(customizeFormDataUserEntity.getId(), campaignMergeDataId);
        }
        // 将之前已有相同手机号且解绑数据关联至当前数据（解决解绑数据没有关联问题）
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
        if (!openMergePhone && campaignMergeDataEntity != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
            bindSamePhoneAndWithoutBindData(campaignMergeDataEntity.getPhone(), customizeFormDataEntity.getEa(), marketingEventId, campaignMergeDataEntity);
        }
        // 只有第一次报名才会触发触发器
        if (StringUtils.isBlank(customizeFormDataUserEntity.getCampaignId()) && campaignMergeDataEntity != null) {
            triggerCampaignEnrollAction(campaignMergeDataEntity, customizeFormDataUserEntity);
        }
        return campaignMergeDataId;
    }

    private void triggerCampaignEnrollAction(CampaignMergeDataEntity campaignMergeData, CustomizeFormDataUserEntity customizeFormDataUser){
        try {
            ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(campaignMergeData.getEa(), campaignMergeData.getMarketingEventId());
            Map<String, Object> paramMap = Maps.newHashMap();
            if (customizeFormDataUser != null) {
                paramMap.put("objectType", customizeFormDataUser.getObjectType());
                paramMap.put("objectId", customizeFormDataUser.getObjectId());
                paramMap.put("spreadFsUid", customizeFormDataUser.getSpreadFsUid());
                paramMap.put("enrollId", customizeFormDataUser.getId());
            }
            if (conference != null) {
                paramMap.put("actionType", MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(campaignMergeData.getEa(), campaignMergeData.getId(), MarketingSceneType.CONFERENCE.getType(), conference.getId(), TriggerActionTypeEnum.CONFERENCE_ENROLL.getTriggerActionType(), paramMap);
                if (BooleanUtils.isTrue(conference.getEnrollReview())){
                    triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(campaignMergeData.getEa(), campaignMergeData.getId(), MarketingSceneType.CONFERENCE.getType(), conference.getId(), TriggerActionTypeEnum.CONFERENCE_ENROLL_REVIEW.getTriggerActionType(), paramMap);
                }
            }

            Integer ei = eieaConverter.enterpriseAccountToId(campaignMergeData.getEa());
            MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, campaignMergeData.getMarketingEventId());
            if (marketingLive != null) {
                paramMap.put("actionType", MarketingUserActionType.ENROLL_LIVE.getActionType());
                triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(campaignMergeData.getEa(), campaignMergeData.getId(), MarketingSceneType.LIVE.getType(), marketingLive.getId(), TriggerActionTypeEnum.LIVE_ENROLL.getTriggerActionType(), paramMap);
            }

            if (conference == null && marketingLive == null && customizeFormDataUser != null && customizeFormDataUser.getObjectType() != null) {
                paramMap.put("actionType", MarketingUserActionType.SUBMIT_FORM.getActionType());
                if (ObjectTypeEnum.HEXAGON_PAGE.getType() == customizeFormDataUser.getObjectType() && !Strings.isNullOrEmpty(customizeFormDataUser.getObjectId())) {
                    HexagonPageEntity page = hexagonPageDAO.getById(customizeFormDataUser.getObjectId());
                    if (page != null) {
                        paramMap.put("objectType", ObjectTypeEnum.HEXAGON_SITE.getType());
                        paramMap.put("objectId", page.getHexagonSiteId());
                        triggerInstanceManager.startInstanceByCampaignIdAndSceneAndTargetObject(campaignMergeData.getEa(), campaignMergeData.getId(),
                                MarketingSceneType.MARKETING_EVENT.getType(), campaignMergeData.getMarketingEventId(), TriggerActionTypeEnum.SUBMIT_FORM_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                        triggerInstanceManager.startInstanceByCampaignIdAndSceneAndTargetObject(campaignMergeData.getEa(), campaignMergeData.getId(),
                                MarketingSceneType.TARGET_CROWD_OPERATION.getType(), campaignMergeData.getMarketingEventId(), TriggerActionTypeEnum.SUBMIT_FORM_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                    }
                } else if (!Strings.isNullOrEmpty(customizeFormDataUser.getObjectId())) {
                    triggerInstanceManager.startInstanceByCampaignIdAndSceneAndTargetObject(campaignMergeData.getEa(), campaignMergeData.getId(),
                            MarketingSceneType.MARKETING_EVENT.getType(), campaignMergeData.getMarketingEventId(), TriggerActionTypeEnum.SUBMIT_FORM_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByCampaignIdAndSceneAndTargetObject(campaignMergeData.getEa(), campaignMergeData.getId(),
                            MarketingSceneType.TARGET_CROWD_OPERATION.getType(), campaignMergeData.getMarketingEventId(), TriggerActionTypeEnum.SUBMIT_FORM_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                }
            }
        } catch (Exception e){
            log.warn("CampaignMergeDataManager.triggerCampaignEnrollAction error e:", e);
        }
    }

    public void updateCampaignMergeDataByCampaignObj(String ea, CrmEventDTO.Body body) {
        String objectId = body.getObjectId();
        ObjectData afterTriggerData = body.getAfterTriggerData();
        //处理签到审核状态
        if (afterTriggerData.get(CampaignConstants.SIGN_IN_STATUS_API_NAME) == null && afterTriggerData.get(CampaignConstants.APPROVAL_STATUS_API_NAME) == null) {
            return;
        }

        String signStatus = (String)afterTriggerData.get(CampaignConstants.SIGN_IN_STATUS_API_NAME);
        String approvalStatus = (String)afterTriggerData.get(CampaignConstants.APPROVAL_STATUS_API_NAME);
        if (signStatus != null
                && !signStatus.equals(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName())
                && !signStatus.equals(ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName())){
            return;
        }
        if (approvalStatus != null
                && !approvalStatus.equals(ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getCampaignMembersObjOptions())
                && !approvalStatus.equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions())
                && !approvalStatus.equals(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getCampaignMembersObjOptions())){
            return;
        }

        ActivityEnrollDataEntity  activityEnrollDataEntity = campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(ea, objectId);
        CampaignMergeDataEntity  campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataByObjId(ea, objectId);
        if (activityEnrollDataEntity != null) {
            if (signStatus != null) {
                ActivitySignOrEnrollEnum activitySignOrEnrollEnum = ActivitySignOrEnrollEnum.getByCampaignMergeDataApiName(signStatus);
                //更新签到状态
                activityEnrollDataDAO.updateSignInStatusById(activityEnrollDataEntity.getId(), activitySignOrEnrollEnum.getType());
            }
            if (approvalStatus != null) {
                ConferenceEnrollReviewStatusEnum reviewStatusEnum = ConferenceEnrollReviewStatusEnum.getByCampaignMembersObjOptions(approvalStatus);
                activityEnrollDataDAO.updateApprovalStatusById(activityEnrollDataEntity.getId(), reviewStatusEnum.getStatus());
                ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, campaignMergeDataEntity.getMarketingEventId());
                if (conference != null) {
                    // 审核成功或失败触发sop
                    // 审核通过生成参会码
                    Map<String, Object> paramMap = Maps.newHashMap();
                    CustomizeFormDataUserEntity customizeFormDataUser = customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(campaignMergeDataEntity.getId());
                    if (customizeFormDataUser != null) {
                        paramMap.put("spreadFsUid", customizeFormDataUser.getSpreadFsUid());
                        paramMap.put("enrollId", customizeFormDataUser.getId());
                    }
                    if (reviewStatusEnum == ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS) {
                        customizeTicketManager.createConferenceCustomizeTicket(activityEnrollDataEntity.getObjectType(), activityEnrollDataEntity.getObjectId(), campaignMergeDataEntity.getId(), ea, campaignMergeDataEntity.getMarketingEventId());
                        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(ea, campaignMergeDataEntity.getId(), MarketingSceneType.CONFERENCE.getType(), conference.getId(), TriggerActionTypeEnum.CONFERENCE_ENROLL_REVIEW_PASS.getTriggerActionType(), paramMap);
                    }
                    if (reviewStatusEnum == ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE) {
                        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(ea, campaignMergeDataEntity.getId(), MarketingSceneType.CONFERENCE.getType(), conference.getId(), TriggerActionTypeEnum.CONFERENCE_ENROLL_REVIEW_FAIL.getTriggerActionType(), paramMap);
                    }
                }
            }
        }
    }

    public void deleteCampaignMergeDataByCampaignObj(String ea, String objectDataId){
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataByObjId(ea, objectDataId);
        if (campaignMergeDataEntity == null) {
            return;
        }
        DeleteParticipantVO vo = new DeleteParticipantVO();
        vo.setCampaignId(campaignMergeDataEntity.getId());
        vo.setEa(ea);
        vo.setVerifyBindingObject(false);
        conferenceManager.deleteParticipants(vo);
    }


    public void addCampaignMergeDataByCampaignObj(String ea, Integer tenantId, String objectDataId, ObjectData objectData) {
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(objectDataId);
        arg.setObjectDescribeApiName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        if (objectData == null) {
            objectData = metadataControllerServiceManager.detail(new com.fxiaoke.crmrestapi.common.data.HeaderObj(tenantId, -10000), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), arg);
        }
        if (objectData == null){
            log.info("CrmMqConsumer metadataControllerServiceManager get objectData detail retun null objectDataId:{}", objectDataId);
            return;
        }
        String marketingEventId = objectData.getString(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        String bindApiName = objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName());
        String leadId = objectData.getString(CampaignMembersObjApiNameEnum.LEADS_ID.getApiName());
        String contactId = objectData.getString(CampaignMembersObjApiNameEnum.CONTACT_ID.getApiName());
        String accountId = objectData.getString(CampaignMembersObjApiNameEnum.ACCOUNT_ID.getApiName());
        String membersStatus = objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName());
        String id = objectData.getId();
        if (StringUtils.isBlank(id) || StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(bindApiName) || StringUtils.isBlank(membersStatus)
                || (StringUtils.isBlank(leadId) && StringUtils.isBlank(contactId) && StringUtils.isBlank(accountId))) {
            log.warn("CampaignMergeDataManager.addCampaignMergeDataByCampaignObj error objectData:{}", objectData);
            return;
        }
        String objectId = convertEmptyString(leadId) + convertEmptyString(contactId) + convertEmptyString(accountId);
        ObjectData bindObjDetail = crmV2Manager.getDetail(ea, -10000, bindApiName, objectId);
        if (bindObjDetail == null) {
            return;
        }
        String phone = getPhoneByObject(bindObjDetail);
        String userName = objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName());
        Integer bindType = CampaignMergeDataObjectTypeEnum.getTypeByName(bindApiName);
        if (bindType == null) {
            return;
        }
        Map<String, Object> updateCampaignFieldDataMap = Maps.newHashMap();  //更新活动成员对象
        //处理表单映射活动成员自定义字段,进行更新活动成员(必须放在前面, 如果已经本地已经创建了campaign_merge_data 数据,则会直接返回)
        if (StringUtils.isNotBlank(objectId)) {
            CustomizeFormDataUserEntity customizeFormDataUserEntity = null;
            if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(bindApiName)) {
                customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserByLeadIdOrderByCreateTime(marketingEventId, leadId);
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(bindApiName) || CrmObjectApiNameEnum.CUSTOMER.getName().equals(bindApiName)){
                //处理 关联客户或者联系人
                customizeFormDataUserEntity = customizeFormDataUserDAO.getContactOrCustomerByTypeAndId(bindApiName,objectId,marketingEventId);
            }
            if (customizeFormDataUserEntity != null) {
                String formId = customizeFormDataUserEntity.getFormId();
                //根据表单id ,查询表单映射活动成员配置
                CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
                FieldMappings campaignMemberMap = formDataEntity.getCampaignMemberMap();
                //更新活动成员对象自定义字段
                if (CollectionUtils.isNotEmpty(campaignMemberMap)) {
                    //更新活动成员对象自定义字段
                    Map<String, Object> customizeFormDataToCampaignFieldDataMap = crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(formDataEntity, customizeFormDataUserEntity.getSubmitContent());
                    Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
                    String spreadUserId = (spreadFsUid == null || QywxUserConstants.isVirtualUserId(spreadFsUid)) ? "-10000" : String.valueOf(spreadFsUid);
                    customizeFormDataToCampaignFieldDataMap.put(CampaignConstants.SPREAD_USER_ID_API_NAME,Lists.newArrayList(spreadUserId));
                    updateCampaignFieldDataMap.putAll(customizeFormDataToCampaignFieldDataMap);
                } else {
                    //更新活动成员推广人
                    Map<String, Object> customizeFormDataToCampaignFieldDataMap = Maps.newHashMap();
                    Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
                    String spreadUserId = (spreadFsUid == null || QywxUserConstants.isVirtualUserId(spreadFsUid)) ? "-10000" : String.valueOf(spreadFsUid);
                    customizeFormDataToCampaignFieldDataMap.put(CampaignConstants.SPREAD_USER_ID_API_NAME,Lists.newArrayList(spreadUserId));
                    updateCampaignFieldDataMap.putAll(customizeFormDataToCampaignFieldDataMap);
                }
            }
        }
        String campaignId = UUIDUtil.getUUID();
        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(objectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindType);
        campaignMergeDataEntity.setCampaignMembersObjId(id);
        campaignMergeDataEntity.setName(userName);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
        // 比较添加的线索/客户/联系人 创建时间 与 活动成员创建时间差
        boolean addCampaignMember = objectData.getCreateTime() - bindObjDetail.getCreateTime() > 2 * 1000 * 60;
        if (addCampaignMember) {
            //异步更新活动成员数据存入状态为已关联
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(CampaignMembersConstants.MARKETING_SAVE_STATUS, CampaignMembersSaveStatusEnum.LINK.getType());
            updateCampaignFieldDataMap.putAll(dataMap);
        }
        //更新活动成员数据
        if (MapUtils.isNotEmpty(updateCampaignFieldDataMap)) {
            crmV2Manager.updateCampaign(objectDataId,ea,-10000,updateCampaignFieldDataMap);
        }

        campaignMergeDataEntity.setAddCampaignMember(addCampaignMember);
        campaignId = addCampaignDataByLock(campaignMergeDataEntity, false, 3);
        if (StringUtils.isBlank(campaignId)) {
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null) {
            // 创建关联数据
            conferenceManager.createConferenceEnrollAttachedInfoByCampaignObj(marketingEventId, campaignId, membersStatus, ea);
            customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignId, ea, null);
        }

        //是否开启不合并相同手机号线索(不开启手机号查重)
        boolean openMergePhone = false;
        //查询手机号合并设置
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
        if (marketingEventCommonSettingEntity != null && marketingEventCommonSettingEntity.getOpenMergePhone() != null && marketingEventCommonSettingEntity.getOpenMergePhone()) {
            openMergePhone = true;
        }
        // 将手机号相同且未绑定对象数据绑定至本活动成员
        if (!openMergePhone) {
            bindSamePhoneAndWithoutBindData(phone, ea, marketingEventId, campaignMergeDataEntity);
        }
        triggerCampaignEnrollAction(campaignMergeDataEntity, null);
    }

    public String addCampaignMergeDataByMember(AddCampaignMergeDataByMemberArgContainer addCampaignMergeDataByMemberContainer) {
        if (addCampaignMergeDataByMemberContainer == null) {
            log.warn("CampaignMergeDataManager.addCampaignMergeDataByMember addCampaignMergeDataByMemberContainer is null");
            return null;
        }
        if (StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getMemberAccessibleCampaignId())) {
            if (StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getEa()) || StringUtils
                .isBlank(addCampaignMergeDataByMemberContainer.getMarketingEventId()) || StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getMemberId()) || StringUtils
                .isBlank(addCampaignMergeDataByMemberContainer.getName()) || StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getObjectId())
                || addCampaignMergeDataByMemberContainer.getObjectType() == null) {
                log.warn("CampaignMergeDataManager.addCampaignMergeDataByMember add param error arg:{}", addCampaignMergeDataByMemberContainer);
                return null;
            }
        } else {
            if (StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getMemberAccessibleCampaignId())) {
                log.warn("CampaignMergeDataManager.addCampaignMergeDataByMember update param arg:{}", addCampaignMergeDataByMemberContainer);
                return null;
            }
        }
        AddCampaignMergeDataByMemberResultContainer addCampaignMergeDataByMemberResultContainer = null;
        // 若没有绑定数据则新建
        String memberAccessibleCampaignId = null;
        if (StringUtils.isBlank(addCampaignMergeDataByMemberContainer.getMemberAccessibleCampaignId())) {
            addCampaignMergeDataByMemberResultContainer = handleMemberMergeData(addCampaignMergeDataByMemberContainer);
            // 创建新数据
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(addCampaignMergeDataByMemberResultContainer.getCampaignMergeDataId());
            MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = new MemberAccessibleCampaignEntity();
            memberAccessibleCampaignId = UUIDUtil.getUUID();
            memberAccessibleCampaignEntity.setId(memberAccessibleCampaignId);
            memberAccessibleCampaignEntity.setEa(addCampaignMergeDataByMemberContainer.getEa());
            memberAccessibleCampaignEntity.setUid(addCampaignMergeDataByMemberContainer.getUid());
            memberAccessibleCampaignEntity.setWxAppId(addCampaignMergeDataByMemberContainer.getWxAppId());
            memberAccessibleCampaignEntity.setOpenId(addCampaignMergeDataByMemberContainer.getOpenId());
            memberAccessibleCampaignEntity.setFingerPrint(addCampaignMergeDataByMemberContainer.getFingerPrint());
            memberAccessibleCampaignEntity.setMarketingEventId(addCampaignMergeDataByMemberContainer.getMarketingEventId());
            memberAccessibleCampaignEntity.setMarketingActivityId(addCampaignMergeDataByMemberContainer.getMarketingActivityId());
            memberAccessibleCampaignEntity.setObjectId(addCampaignMergeDataByMemberContainer.getObjectId());
            memberAccessibleCampaignEntity.setObjectType(addCampaignMergeDataByMemberContainer.getObjectType());
            memberAccessibleCampaignEntity.setBindCrmObjectType(campaignMergeDataEntity.getBindCrmObjectType());
            memberAccessibleCampaignEntity.setBindCrmObjectId(campaignMergeDataEntity.getBindCrmObjectId());
            memberAccessibleCampaignEntity.setCampaignId(addCampaignMergeDataByMemberResultContainer.getCampaignMergeDataId());
            memberAccessibleCampaignEntity.setMemberId(addCampaignMergeDataByMemberContainer.getMemberId());
            memberAccessibleCampaignEntity.setSpreadFsUid(addCampaignMergeDataByMemberContainer.getSpreadFsUid());
            memberAccessibleCampaignEntity.setSaveCrmStatus(addCampaignMergeDataByMemberContainer.getSaveCrmStatus());
            memberAccessibleCampaignEntity.setSaveCrmErrorMessage(addCampaignMergeDataByMemberContainer.getSaveCrmErrorMessage());
            memberAccessibleCampaignEntity.setChannelValue(addCampaignMergeDataByMemberContainer.getChannelValue());
            memberAccessibleCampaignDAO.addMemberAccessibleCampaignData(memberAccessibleCampaignEntity);
        } else {
            // 更新会员数据
            if (addCampaignMergeDataByMemberContainer.getSaveCrmStatus() != null) {
                memberAccessibleCampaignDAO.updateMemberAccessibleCampaign(addCampaignMergeDataByMemberContainer.getSaveCrmStatus(), addCampaignMergeDataByMemberContainer.getSaveCrmErrorMessage(),
                    addCampaignMergeDataByMemberContainer.getBindObjectType(), addCampaignMergeDataByMemberContainer.getBindObjectId(),
                    addCampaignMergeDataByMemberContainer.getMemberAccessibleCampaignId());
            }
            MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(addCampaignMergeDataByMemberContainer.getMemberAccessibleCampaignId());
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(memberAccessibleCampaignEntity.getCampaignId());
            memberAccessibleCampaignId = memberAccessibleCampaignEntity.getId();
            addCampaignMergeDataByMemberContainer.setEa(memberAccessibleCampaignEntity.getEa());
            addCampaignMergeDataByMemberContainer.setMarketingEventId(memberAccessibleCampaignEntity.getMarketingEventId());
            addCampaignMergeDataByMemberContainer.setMarketingActivityId(memberAccessibleCampaignEntity.getMarketingActivityId());
            addCampaignMergeDataByMemberContainer.setObjectId(memberAccessibleCampaignEntity.getObjectId());
            addCampaignMergeDataByMemberContainer.setObjectType(memberAccessibleCampaignEntity.getObjectType());
            addCampaignMergeDataByMemberContainer.setCampaignId(memberAccessibleCampaignEntity.getCampaignId());
            addCampaignMergeDataByMemberContainer.setChannelValue(memberAccessibleCampaignEntity.getChannelValue());
            addCampaignMergeDataByMemberContainer.setMemberId(memberAccessibleCampaignEntity.getMemberId());
            addCampaignMergeDataByMemberContainer.setName(campaignMergeDataEntity.getName());
            addCampaignMergeDataByMemberContainer.setPhone(campaignMergeDataEntity.getPhone());
            addCampaignMergeDataByMemberResultContainer = handleMemberMergeData(addCampaignMergeDataByMemberContainer);
        }
        // 若在没有创建表数据且没有绑定新数据时直接返回原总表id
        if (!addCampaignMergeDataByMemberContainer.isNeedCreateDataIfNoBindCRM() && !addCampaignMergeDataByMemberResultContainer.isBindOldData() && !addCampaignMergeDataByMemberResultContainer.isCreateNewData()) {
            return addCampaignMergeDataByMemberContainer.getCampaignId();
        }
        String oldCampaignId = addCampaignMergeDataByMemberContainer.getCampaignId();
        String newCampaignId = addCampaignMergeDataByMemberResultContainer.getCampaignMergeDataId();
        if (StringUtils.isNotBlank(oldCampaignId) && !oldCampaignId.equals(newCampaignId)) {
            if(addCampaignMergeDataByMemberResultContainer.isBindOldData()) {
                // 绑定了旧数据
                campaignMergeDataDAO.deleteCampaignMergeData(oldCampaignId);
                memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(memberAccessibleCampaignId, newCampaignId);
                // 切换之前绑定关联表id
                activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(oldCampaignId);
                customizeTicketDAO.deleteCustomizeTicketFormDataUser(oldCampaignId);
            } else {
                // 未绑定旧数据
                campaignMergeDataDAO.deleteCampaignMergeData(oldCampaignId);
                memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(memberAccessibleCampaignId, newCampaignId);
                // 切换之前绑定关联表id
                activityEnrollDataDAO.updateActivityEnrollDataFormDataUser(newCampaignId, oldCampaignId);
                customizeTicketDAO.updateCustomizeTicketFormDataUser(newCampaignId, oldCampaignId);
            }
        } else {
            // 绑定报名数据
            memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(memberAccessibleCampaignId, newCampaignId);
        }
        if (StringUtils.isBlank(oldCampaignId)) {
            triggerCampaignEnrollAction(campaignMergeDataDAO.getCampaignMergeDataById(newCampaignId), null);
        }
        return newCampaignId;
    }

    public AddCampaignMergeDataByMemberResultContainer handleMemberMergeData(AddCampaignMergeDataByMemberArgContainer addCampaignMergeDataByMemberContainer) {
        String campaignMergeDataId = UUIDUtil.getUUID();
        Boolean bindOldData = false;
        Boolean createNewData = false;
        if (addCampaignMergeDataByMemberContainer.getBindObjectType() != null && StringUtils.isNotBlank(addCampaignMergeDataByMemberContainer.getBindObjectId())) {
            // 绑定对象查询总表中是否有该对象数据
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(addCampaignMergeDataByMemberContainer.getEa(), addCampaignMergeDataByMemberContainer.getMarketingEventId(), addCampaignMergeDataByMemberContainer.getBindObjectType(), addCampaignMergeDataByMemberContainer.getBindObjectId());
            if (campaignMergeDataEntity == null) {
                if (StringUtils.isNotBlank(addCampaignMergeDataByMemberContainer.getPhone())) {
                    // 查询总表中是否具有相同手机号
                    List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO
                        .getCampaignMergeDataByPhone(addCampaignMergeDataByMemberContainer.getEa(), addCampaignMergeDataByMemberContainer.getMarketingEventId(), addCampaignMergeDataByMemberContainer.getPhone(), true);
                    if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                        CampaignMergeDataEntity needBindCampaignData = getCampaignMergeDataByRule(campaignMergeDataEntityList);
                        campaignMergeDataId = needBindCampaignData.getId();
                        bindOldData = true;
                    } else {
                        // 创建表数据创建活动成员数据
                        CampaignMergeDataEntity saveData = new CampaignMergeDataEntity();
                        saveData.setId(campaignMergeDataId);
                        saveData.setEa(addCampaignMergeDataByMemberContainer.getEa());
                        saveData.setMarketingEventId(addCampaignMergeDataByMemberContainer.getMarketingEventId());
                        saveData.setName(addCampaignMergeDataByMemberContainer.getName());
                        saveData.setPhone(addCampaignMergeDataByMemberContainer.getPhone());
                        saveData.setBindCrmObjectType(addCampaignMergeDataByMemberContainer.getBindObjectType());
                        saveData.setBindCrmObjectId(addCampaignMergeDataByMemberContainer.getBindObjectId());
                        saveData.setCreateTime(new Date());
                        saveData.setUpdateTime(new Date());
                        saveData.setSourceType(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType());
                        createNewData = true;
                        saveData.setMarketingPromotionSourceId(addCampaignMergeDataByMemberContainer.getMarketingPromotionSourceId());
                        campaignMergeDataId = saveCampaignDataAndAddCrmObj(saveData, true, CampaignMembersObjMemberStatusEnum.REGISTERED, addCampaignMergeDataByMemberContainer.getCampaignId(),addCampaignMergeDataByMemberContainer.getSpreadFsUid(),null,null);
                    }
                } else {
                    // 没有手机且没有对象数据需要创建活动成员（手机从对应对象中获取）
                    CampaignMergeDataEntity saveData = new CampaignMergeDataEntity();
                    saveData.setId(campaignMergeDataId);
                    saveData.setEa(addCampaignMergeDataByMemberContainer.getEa());
                    saveData.setMarketingEventId(addCampaignMergeDataByMemberContainer.getMarketingEventId());
                    saveData.setName(addCampaignMergeDataByMemberContainer.getName());
                    saveData.setPhone(addCampaignMergeDataByMemberContainer.getPhone());
                    saveData.setBindCrmObjectType(addCampaignMergeDataByMemberContainer.getBindObjectType());
                    saveData.setBindCrmObjectId(addCampaignMergeDataByMemberContainer.getBindObjectId());
                    saveData.setCreateTime(new Date());
                    saveData.setUpdateTime(new Date());
                    saveData.setSourceType(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType());
                    saveData.setMarketingPromotionSourceId(addCampaignMergeDataByMemberContainer.getMarketingPromotionSourceId());
                    campaignMergeDataId = saveCampaignDataAndAddCrmObj(saveData, true, CampaignMembersObjMemberStatusEnum.REGISTERED, addCampaignMergeDataByMemberContainer.getCampaignId(),addCampaignMergeDataByMemberContainer.getSpreadFsUid(),null,null);
                    createNewData = true;
                }
            } else {
                bindOldData = true;
                campaignMergeDataId = campaignMergeDataEntity.getId();
            }
        } else {
            boolean needCreateData = true;
            campaignMergeDataId = UUIDUtil.getUUID();
            // 查询总表中是否有相同手机号且绑定了对象的数据
            if (StringUtils.isNotBlank(addCampaignMergeDataByMemberContainer.getPhone())) {
                List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO
                    .getCampaignMergeDataByPhone(addCampaignMergeDataByMemberContainer.getEa(), addCampaignMergeDataByMemberContainer.getMarketingEventId(), addCampaignMergeDataByMemberContainer.getPhone(), true);
                if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                    needCreateData = false;
                    CampaignMergeDataEntity needBindCampaignData = getCampaignMergeDataByRule(campaignMergeDataEntityList);
                    campaignMergeDataId = needBindCampaignData.getId();
                    bindOldData = true;
                }
            }
            if (needCreateData && addCampaignMergeDataByMemberContainer.isNeedCreateDataIfNoBindCRM()) {
                // 创建表数据但不创建活动成员数据
                CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
                campaignMergeDataEntity.setId(campaignMergeDataId);
                campaignMergeDataEntity.setEa(addCampaignMergeDataByMemberContainer.getEa());
                campaignMergeDataEntity.setMarketingEventId(addCampaignMergeDataByMemberContainer.getMarketingEventId());
                campaignMergeDataEntity.setName(addCampaignMergeDataByMemberContainer.getName());
                campaignMergeDataEntity.setPhone(addCampaignMergeDataByMemberContainer.getPhone());
                campaignMergeDataEntity.setCreateTime(new Date());
                campaignMergeDataEntity.setUpdateTime(new Date());
                campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType());
                campaignMergeDataEntity.setMarketingPromotionSourceId(addCampaignMergeDataByMemberContainer.getMarketingPromotionSourceId());
                campaignMergeDataId = saveCampaignDataAndAddCrmObj(campaignMergeDataEntity, false, null, addCampaignMergeDataByMemberContainer.getCampaignId(),addCampaignMergeDataByMemberContainer.getSpreadFsUid(),null,null);
                createNewData = true;
            }
        }
        AddCampaignMergeDataByMemberResultContainer result = new AddCampaignMergeDataByMemberResultContainer();
        result.setCampaignMergeDataId(campaignMergeDataId);
        result.setBindOldData(bindOldData);
        result.setCreateNewData(createNewData);
        return result;
    }

    public void bindSamePhoneAndWithoutBindData(String phone, String ea, String marketingEventId, CampaignMergeDataEntity needBindCampaign) {
        if (StringUtils.isBlank(phone)) {
            return;
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, marketingEventId, phone, false);
        if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
            campaignMergeDataEntityList = campaignMergeDataEntityList.stream().filter(data -> StringUtils.isBlank(data.getCampaignMembersObjId())).collect(Collectors.toList());
            // 修改绑定表单数据
            batchBindCustomizeFormData(campaignMergeDataEntityList, needBindCampaign, true);
        }
        // 还需要绑定同市场活动下手机相同绑定对象一致没有关联活动成员的数据
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndPhone(marketingEventId, phone);
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            return;
        }
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId())) {
                continue;
            }
            Map<String, String> bindObjectMap = customizeFormDataManager.getFormEnrollDataBindObject(customizeFormDataUserEntity);
            if (MapUtils.isEmpty(bindObjectMap)) {
                continue;
            }
            Integer bindObjectType = CampaignMergeDataObjectTypeEnum.getTypeByName(bindObjectMap.keySet().iterator().next());
            String bindObjectId = bindObjectMap.values().iterator().next();
            if (needBindCampaign.getBindCrmObjectType().equals(bindObjectType) && needBindCampaign.getBindCrmObjectId().equals(bindObjectId)) {
                customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(customizeFormDataUserEntity.getId(), needBindCampaign.getId());
            }
        }
    }

    public void batchBindCustomizeFormData(List<CampaignMergeDataEntity> campaignMergeDataEntityList, CampaignMergeDataEntity needBindCampaign, boolean deleteOldCampaignData) {
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return;
        }
        List<String> needDeleteData = Lists.newArrayList();
        for (CampaignMergeDataEntity data : campaignMergeDataEntityList) {
            if (data.getId().equals(needBindCampaign.getId())) {
                continue;
            }
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(data.getId());
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
                    // 保存crm对象数据待处理的不处理
                    if (customizeFormDataUserEntity.getSaveCrmStatus() != null && customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
                        continue;
                    }
                    customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(customizeFormDataUserEntity.getId(), needBindCampaign.getId());
                    // 修改绑定对象
                    bindFormDataUserCrmObj(needBindCampaign, customizeFormDataUserEntity);
                }
            }
            needDeleteData.add(data.getId());
        }
        if (CollectionUtils.isNotEmpty(needDeleteData) && deleteOldCampaignData) {
            for (String dataId : needDeleteData) {
                campaignMergeDataDAO.deleteCampaignMergeData(dataId);
                // 切换之前绑定关联表id
                activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(dataId);
                customizeTicketDAO.deleteCustomizeTicketFormDataUser(dataId);
            }
        }
    }

    public CampaignMergeDataEntity getCampaignMergeDataByRule(List<CampaignMergeDataEntity> campaignMergeDataEntityList) {
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return new CampaignMergeDataEntity();
        }
        if (campaignMergeDataEntityList.size() == 1) {
            return campaignMergeDataEntityList.get(0);
        }
        // 根据type排序（客户 > 联系人 > 线索）
        campaignMergeDataEntityList = campaignMergeDataEntityList.stream().sorted(Comparator.comparing(CampaignMergeDataEntity::getBindCrmObjectType).reversed()).collect(Collectors.toList());
        return campaignMergeDataEntityList.get(0);
    }

    public String saveCampaignDataAndAddCrmObj(CampaignMergeDataEntity campaignMergeDataEntity, boolean saveCampaignObj, CampaignMembersObjMemberStatusEnum setStatusAfterSaveObj, String oldCampaignMergeDataId, Integer spreadFsUid, CustomizeFormDataEntity customizeFormDataEntity,CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (campaignMergeDataEntity == null) {
            return null;
        }
        if (saveCampaignObj) {
            // 首先查询是否存在关联数据
            String campaignMemberId = getCampaignMergeObjIdByEntity(campaignMergeDataEntity.getEa(), campaignMergeDataEntity);
            if (StringUtils.isBlank(campaignMemberId)) {
                Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(campaignMergeDataEntity.getEa(), campaignMergeDataEntity, setStatusAfterSaveObj, oldCampaignMergeDataId,spreadFsUid);
                //处理表单映射活动成员自定义字段,进行必填字段校验和赋值
                if (customizeFormDataEntity != null && customizeFormDataEntity.getCampaignMemberMap() != null) {
                    Set<String> notNullCrmLeadFieldNames = crmV2Manager.getObjectFieldNameList(campaignMergeDataEntity.getEa(), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
                    Map<String, Object> customizeFormDataToCampaignFieldDataMap = crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(customizeFormDataEntity, customizeFormDataUserEntity.getSubmitContent());
                    //如果customizeFormDataToCampaignFieldDataMap 的key 是在notNullCrmLeadFieldNames中,是必填字段, 则进行赋值,添加到dataMap中
                    if (MapUtils.isNotEmpty(customizeFormDataToCampaignFieldDataMap)) {
                        customizeFormDataToCampaignFieldDataMap.forEach((key, value) -> {
                            if (notNullCrmLeadFieldNames.contains(key)) {
                                dataMap.put(key, value);
                            }
                        });
                    }
                }
                if (MapUtils.isNotEmpty(dataMap)) {
                    campaignMemberId = crmV2Manager
                        .addCampaignMembersObjByLock(campaignMergeDataEntity.getEa(), dataMap, campaignMergeDataEntity.getBindCrmObjectType(), campaignMergeDataEntity.getBindCrmObjectId(),
                            campaignMergeDataEntity.getMarketingEventId());
                }
            }
            if (StringUtils.isNotBlank(campaignMemberId)) {
                campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberId);
            } else {
                // 未绑定活动成员则将bind数据清空
                campaignMergeDataEntity.setBindCrmObjectType(null);
                campaignMergeDataEntity.setBindCrmObjectId(null);
            }
        }
        return addCampaignDataOnlyUnLock(campaignMergeDataEntity);
    }

    public void bindFormDataUserCrmObj(CampaignMergeDataEntity needBindCampaignData, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (needBindCampaignData.getBindCrmObjectType().equals(CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType())) {
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.SUCCESS.getValue(), null, needBindCampaignData.getBindCrmObjectId());
        } else {
            CustomizeFormBindOtherCrmObject customizeFormBindOtherCrmObject = new CustomizeFormBindOtherCrmObject();
            customizeFormBindOtherCrmObject.setApiName(CampaignMergeDataObjectTypeEnum.getByType(needBindCampaignData.getBindCrmObjectType()).getApiName());
            customizeFormBindOtherCrmObject.setObjectId(needBindCampaignData.getBindCrmObjectId());
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmObjectBind(SaveCrmStatusEnum.SUCCESS.getValue(), customizeFormDataUserEntity.getId(), customizeFormBindOtherCrmObject);
        }
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity, CampaignMembersObjMemberStatusEnum setStatusAfterSaveObj, String oldCampaignMergeDataId,Integer spreadFsUid) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        //设置创建人和负责人
        boolean addLeadsObjectAuth = false;
        if (spreadFsUid != null) {
            addLeadsObjectAuth = customizeFormDataManager.checkAddLeadsObjectAuth(ea, spreadFsUid);
            if (!addLeadsObjectAuth) {
                spreadFsUid = clueDefaultSettingService.getClueCreator(campaignMergeDataEntity.getMarketingEventId(), ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(ClueDefaultSettingTypeEnum.OTHER.getName()));
            }
        } else {
            spreadFsUid = clueDefaultSettingService.getClueCreator(campaignMergeDataEntity.getMarketingEventId(), ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(ClueDefaultSettingTypeEnum.OTHER.getName()));
        }
        List<String> ownerIds = Arrays.asList(spreadFsUid.toString());
        dataMap.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(),ownerIds);
        dataMap.put("created_by",ownerIds);
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        // 设置会议状态
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(campaignMergeDataEntity.getMarketingEventId(), ea);
        if (activityEntity != null) {
            // 签到
            dataMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName());
            // 审核
            dataMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME,
                activityEntity.getEnrollReview() ? ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getCampaignMembersObjOptions()
                    : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions());
            // 查询之前是否有签到与审核数据
            if (StringUtils.isNotBlank(oldCampaignMergeDataId)) {
                List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(oldCampaignMergeDataId));
                if (CollectionUtils.isNotEmpty(activityEnrollDataEntityList)) {
                    ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataEntityList.get(0);
                    if (activityEnrollDataEntity != null && activityEnrollDataEntity.getReviewStatus() != null) {
                        ConferenceEnrollReviewStatusEnum conferenceEnrollReviewStatusEnum = ConferenceEnrollReviewStatusEnum.getByType(activityEnrollDataEntity.getReviewStatus());
                        if (conferenceEnrollReviewStatusEnum != null) {
                            dataMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME, conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions());
                        }
                    }
                    if (activityEnrollDataEntity != null && activityEnrollDataEntity.getSignIn() != null) {
                        if (ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(activityEnrollDataEntity.getSignIn())) {
                            dataMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName());
                            if (activityEnrollDataEntity.getSignInTime() != null) {
                                dataMap.put(CampaignConstants.SIGNED_TIME_API_NAME, activityEnrollDataEntity.getSignInTime().getTime());
                            }
                        }
                    }
                }
            }
        }
        if (setStatusAfterSaveObj != null) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), setStatusAfterSaveObj.getValue());
        }
        String marketingPromotionSourceId = campaignMergeDataEntity.getMarketingPromotionSourceId();
        if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
            dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_PROMOTION_SOURCE_ID.getApiName(), marketingPromotionSourceId);
        }
        return dataMap;
    }

    public String getCampaignMergeObjIdByEntity(String ea, CampaignMergeDataEntity campaignMergeDataEntity) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return null;
        }
        return crmV2Manager.getCampaignMembersObjId(ea, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId(),
            campaignMergeDataEntity.getMarketingEventId());
    }

    public String addCampaignDataByLock(CampaignMergeDataEntity campaignMergeDataEntity, boolean returnCheckId, Integer tryTime) {
        if (tryTime <= 0) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap reTryTime limit");
            return null;
        }
        CampaignMergeDataEntity checkData = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataEntity.getId());
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
            checkData = campaignMergeDataDAO.getCampaignMergeDataByObjId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId());
        }
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getBindCrmObjectId()) && campaignMergeDataEntity.getBindCrmObjectType() != null) {
            checkData = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId(), campaignMergeDataEntity.getBindCrmObjectType(),
                    campaignMergeDataEntity.getBindCrmObjectId());
        }
        if (checkData != null) {
            if (returnCheckId) {
                return checkData.getId();
            } else {
                return null;
            }
        }
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getMarketingEventId()) && StringUtils.isNotBlank(campaignMergeDataEntity.getBindCrmObjectId())
            || campaignMergeDataEntity.getBindCrmObjectType() != null) {
            checkData = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId(), campaignMergeDataEntity.getBindCrmObjectType(),
                    campaignMergeDataEntity.getCampaignMembersObjId());
            if (checkData != null) {
                if (returnCheckId) {
                    return checkData.getId();
                } else {
                    return null;
                }
            }
            StringJoiner sj = new StringJoiner("_");
            sj.add(MARKETING_CAMPAIGN_DATA_LOCK_KEY);
            sj.add(campaignMergeDataEntity.getEa());
            sj.add(campaignMergeDataEntity.getMarketingEventId());
            sj.add(campaignMergeDataEntity.getBindCrmObjectType().toString());
            sj.add(campaignMergeDataEntity.getBindCrmObjectId());
            try {
                boolean redisLock = redisManager.lock(sj.toString(), 100);
                if (!redisLock) {
                    return addCampaignDataByLock(campaignMergeDataEntity, returnCheckId, --tryTime);
                }
                this.addCampaignMergeDataToDB(campaignMergeDataEntity);
            } catch (Exception e) {
                log.warn("CampaignMergeDataManager.addCampaignDataByLock addCampaignMergeData error e:{}", e);
                return null;
            } finally {
                redisManager.unLock(sj.toString());
            }
            return campaignMergeDataEntity.getId();
        } else {
            this.addCampaignMergeDataToDB(campaignMergeDataEntity);
            return campaignMergeDataEntity.getId();
        }
    }
    
    public int addCampaignMergeDataToDB(CampaignMergeDataEntity campaignMergeData){
        return campaignMergeDataDAO.addCampaignMergeData(campaignMergeData);
    }
    
    public String addCampaignDataOnlyUnLock(CampaignMergeDataEntity campaignMergeDataEntity) {
        CampaignMergeDataEntity checkData = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataEntity.getId());
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
            checkData = campaignMergeDataDAO.getCampaignMergeDataByObjId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId());
        }
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getBindCrmObjectId()) && campaignMergeDataEntity.getBindCrmObjectType() != null) {
            checkData = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId(), campaignMergeDataEntity.getBindCrmObjectType(),
                    campaignMergeDataEntity.getBindCrmObjectId());
        }
        if (checkData != null) {
            return checkData.getId();
        }
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getMarketingEventId()) && StringUtils.isNotBlank(campaignMergeDataEntity.getBindCrmObjectId())
            || campaignMergeDataEntity.getBindCrmObjectType() != null) {
            checkData = campaignMergeDataDAO
                .getCampaignMergeDataByBindObject(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId(), campaignMergeDataEntity.getBindCrmObjectType(),
                    campaignMergeDataEntity.getCampaignMembersObjId());
            if (checkData != null) {
                return checkData.getId();
            }
            StringJoiner sj = new StringJoiner("_");
            sj.add(MARKETING_CAMPAIGN_DATA_LOCK_KEY);
            sj.add(campaignMergeDataEntity.getEa());
            sj.add(campaignMergeDataEntity.getMarketingEventId());
            sj.add(campaignMergeDataEntity.getBindCrmObjectType().toString());
            sj.add(campaignMergeDataEntity.getBindCrmObjectId());
            try {
                this.addCampaignMergeDataToDB(campaignMergeDataEntity);
            } catch (Exception e) {
                log.warn("CampaignMergeDataManager.addCampaignDataByLock addCampaignMergeData error e:{}", e);
                return null;
            } finally {
                redisManager.unLock(sj.toString());
            }
            return campaignMergeDataEntity.getId();
        } else {
            this.addCampaignMergeDataToDB(campaignMergeDataEntity);
            return campaignMergeDataEntity.getId();
        }
    }

    private String convertEmptyString(String s) {
        if (StringUtils.isBlank(s)) {
            return "";
        }
        return s;
    }

    public List<String> campaignIdToActivityEnrollId(List<String> campaignIds) {
        List<String> activityEnrollIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return activityEnrollIds;
        }
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(campaignIds);
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return activityEnrollIds;
        }
        return activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList());
    }

    public Map<String, String> campaignIdToActivityEnrollIdMap(List<String> campaignIds) {
        Map<String, String> activityEnrollIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return activityEnrollIdMap;
        }
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(campaignIds);
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return activityEnrollIdMap;
        }
        return activityEnrollDataEntityList.stream().collect(Collectors.toMap(ActivityEnrollDataEntity::getFormDataUserId, ActivityEnrollDataEntity::getId, (v1, v2) -> v1));
    }

    public List<String> activityEnrollIdToCampaignId(List<String> activityEnrollIds) {
        List<String> campaignIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(activityEnrollIds)) {
            return campaignIds;
        }
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByIds(activityEnrollIds);
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return campaignIds;
        }
        campaignIds = activityEnrollDataEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getFormDataUserId())).map(ActivityEnrollDataEntity::getFormDataUserId)
            .collect(Collectors.toList());
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return Lists.newArrayList();
        }
        return campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
    }

    public Map<String, CustomizeFormDataUserEntity> getLatestActivityEnrollDataByCampaignId(String ea, String marketingEventId, String conferenceId, List<String> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Maps.newHashMap();
        }

//        List<String> objectIds = activityManager.getActivityLinkObject(conferenceId, ObjectTypeEnum.ACTIVITY.getType(), Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION, ObjectTypeEnum.HEXAGON_SITE,ObjectTypeEnum.CUSTOMIZE_FORM, ObjectTypeEnum.ARTICLE, ObjectTypeEnum.PRODUCT, ObjectTypeEnum.HEXAGON_PAGE ,ObjectTypeEnum.QR_POSTER));
        // 查询从当前活动报名的表单数据
//        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByObjectsAndCampaignId(objectIds, campaignIds);
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserListByMarketingEventId(marketingEventId);

        Map<String, CustomizeFormDataUserEntity> map = customizeFormDataUserEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignId())).collect(Collectors.toMap(CustomizeFormDataUserEntity::getCampaignId, Function.identity(), (k1, k2) -> k2));

        try {
            // 判断有无父活动
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, marketingEventId);
            if (Objects.nonNull(marketingEventData) && StringUtils.isNotBlank(marketingEventData.getParentId())) {
                if (customizeFormDataUserEntityList.size() != campaignIds.size()) {
                    // 说明有些数据要从父活动取
                    List<String> currentCampaignIds = customizeFormDataUserEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignId())).map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList());
                    // 去除掉当前活动报名的活动成员
                    campaignIds.removeAll(currentCampaignIds);

                    Map<String, CustomizeFormDataUserEntity> parentMap = getParentMarketingEventEnrollDataByCampaignId(ea, marketingEventId, campaignIds);
                    map.putAll(parentMap);
                }
            }
        } catch (Exception e) {
            log.warn("getParentMarketingEventEnrollDataByCampaignId e:", e);
        }

        return map;
    }

    /**
     * 从父活动中取报名表单数据
     * @param ea
     * @param marketingEventId
     * @param campaignIds
     * @return
     */
    public Map<String, CustomizeFormDataUserEntity> getParentMarketingEventEnrollDataByCampaignId(String ea, String marketingEventId, List<String> campaignIds){
        String parentMarketingEventId = marketingEventManager.getMarketingEventData(ea, -10000, marketingEventId).getParentId();
        if (parentMarketingEventId == null){
            return Maps.newHashMap();
        }
        // 没有报名的活动成员，无需后续处理
        if (CollectionUtils.isEmpty(campaignIds)){
            return Maps.newHashMap();
        }
        // 查询父活动详情
        MarketingEventData parentMarketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, parentMarketingEventId);
        // 如果不是多会场活动，无需后续处理
        if (!Objects.equals(parentMarketingEventData.getEventForm(), MarketingEventFormEnum.MULTIVENUE_MARKETING.getValue())) {
            return Maps.newHashMap();
        }

        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        if (CollectionUtils.isEmpty(campaignMergeDataEntities)){
            return Maps.newHashMap();
        }
        Map<String, String> phoneCampaignIdMap = campaignMergeDataEntities.stream().filter(entity -> entity.getPhone() != null).collect(Collectors.toMap(CampaignMergeDataEntity::getPhone, CampaignMergeDataEntity::getId, (v1,v2)->v1));
        if (CollectionUtils.isEmpty(phoneCampaignIdMap.keySet())) {
            return Maps.newHashMap();
        }
        List<CustomizeFormDataUserEntity>  customizeFormDataUserEntities = customizeFormDataUserDAO.getCustomizeFormDataUserEntityByContentPhone(parentMarketingEventId, Lists.newArrayList(phoneCampaignIdMap.keySet()));
        if (CollectionUtils.isEmpty(customizeFormDataUserEntities)){
            return Maps.newHashMap();
        }

        Map<String, CustomizeFormDataUserEntity> campaignIdDataMap = new HashMap<>();
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntities){
            if (customizeFormDataUserEntity.getSubmitContent().getPhone() != null){
                String campaignId = phoneCampaignIdMap.get(customizeFormDataUserEntity.getSubmitContent().getPhone());
                if (campaignId != null) {
                    campaignIdDataMap.put(campaignId, customizeFormDataUserEntity);
                }
            }
        }
        return campaignIdDataMap;
    }

    public ActivityEnrollDataEntity getActivityEnrollDataByCampaignId(String campaignId) {
        if (StringUtils.isBlank(campaignId)) {
            return null;
        }
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(campaignId));
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return null;
        }
        return activityEnrollDataEntityList.get(0);
    }

    public String getPhoneByObject(ObjectData objectData) {
        if (objectData == null) {
            return null;
        }
        if (StringUtils.isNotBlank(objectData.getString("mobile"))) {
            return UserMarketingAccountAssociationManager.getFirstPhone(objectData.getString("mobile"));
        }
        if (StringUtils.isNotBlank(objectData.getString("tel"))) {
            return UserMarketingAccountAssociationManager.getFirstPhone(objectData.getString("tel"));
        }
        return null;
    }

    public void addCampaignMembersObjField(String ea) {
        // 邀约状态 审核状态 参会码 分组 签到状态 签到时间
        if (StringUtils.isBlank(ea)) {
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        if (!apiNames.contains(CampaignConstants.INVITATION_STATUS_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "    \"describe_api_name\": \"CampaignMembersObj\",\n"
                    + "    \"type\": \"select_one\",\n"
                    + "    \"define_type\": \"package\",\n"
                    + "    \"api_name\": \"invitation_status\",\n"
                    + "    \"label\": \"邀约状态\",\n"
                    + "    \"help_text\": \"\",\n"
                    + "    \"is_required\": false,\n"
                    + "    \"is_unique\": false,\n"
                    + "    \"is_active\": true,\n"
                    + "    \"is_index\": true,\n"
                    + "    \"resource_bundle_key\": null,\n"
                    + "    \"status\": \"new\",\n"
                    + "    \"default_value\": \"\",\n"
                    + "    \"options\": [{\n"
                    + "        \"label\": \"已邀约\",\n"
                    + "        \"value\": \"invited\"\n"
                    + "    }, {\n"
                    + "        \"label\": \"待邀约\",\n"
                    + "        \"value\": \"inviting\"\n"
                    + "    }],\n"
                    + "    \"maxItems\": -1\n"
                    + "}");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        if (!apiNames.contains(CampaignConstants.APPROVAL_STATUS_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "        \"describe_api_name\": \"CampaignMembersObj\",\n"
                    + "        \"type\": \"select_one\",\n"
                    + "        \"define_type\": \"package\",\n"
                    + "        \"api_name\": \"approval_status\",\n"
                    + "        \"label\": \"审核状态\",\n"
                    + "        \"help_text\": \"\",\n"
                    + "        \"is_required\": false,\n"
                    + "        \"is_unique\": false,\n"
                    + "        \"is_active\": true,\n"
                    + "        \"is_index\": true,\n"
                    + "        \"resource_bundle_key\": null,\n"
                    + "        \"status\": \"new\",\n"
                    + "        \"default_value\": \"\",\n"
                    + "        \"options\": [{\n"
                    + "            \"label\": \"待审核\",\n"
                    + "            \"value\": \"processing\"\n"
                    + "        }, {\n"
                    + "            \"label\": \"审核已通过\",\n"
                    + "            \"value\": \"approved\"\n"
                    + "        }, {\n"
                    + "            \"label\": \"审核未通过\",\n"
                    + "            \"value\": \"rejected\"\n"
                    + "        }],\n"
                    + "        \"maxItems\": -1\n"
                    + "    }");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        if (!apiNames.contains(CampaignConstants.PARTICIPANTS_PASSCODE_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "    \"describe_api_name\": \"CampaignMembersObj\",\n"
                    + "    \"type\": \"text\",\n"
                    + "    \"define_type\": \"package\",\n"
                    + "    \"api_name\": \"participants_passcode\",\n"
                    + "    \"label\": \"参会码\",\n"
                    + "    \"help_text\": \"\",\n"
                    + "    \"is_required\": false,\n"
                    + "    \"is_unique\": false,\n"
                    + "    \"is_active\": true,\n"
                    + "    \"is_index\": true,\n"
                    + "    \"resource_bundle_key\": null,\n"
                    + "    \"status\": \"new\",\n"
                    + "    \"default_value\": \"\",\n"
                    + "    \"default_is_expression\": false,\n"
                    + "    \"default_to_zero\": false,\n"
                    + "    \"max_length\": 100,\n"
                    + "    \"input_mode\": \"\"\n"
                    + "}");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"text\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        if (!apiNames.contains(CampaignConstants.PARTICIPANTS_GROUP_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "    \"describe_api_name\": \"CampaignMembersObj\",\n"
                    + "    \"type\": \"text\",\n"
                    + "    \"define_type\": \"package\",\n"
                    + "    \"api_name\": \"participants_group\",\n"
                    + "    \"label\": \"分组\",\n"
                    + "    \"help_text\": \"\",\n"
                    + "    \"is_required\": false,\n"
                    + "    \"is_unique\": false,\n"
                    + "    \"is_active\": true,\n"
                    + "    \"is_index\": true,\n"
                    + "    \"resource_bundle_key\": null,\n"
                    + "    \"status\": \"new\",\n"
                    + "    \"default_value\": \"\",\n"
                    + "    \"default_is_expression\": false,\n"
                    + "    \"default_to_zero\": false,\n"
                    + "    \"max_length\": 100,\n"
                    + "    \"input_mode\": \"\"\n"
                    + "}");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"text\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        if (!apiNames.contains(CampaignConstants.SIGN_IN_STATUS_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "    \"describe_api_name\": \"CampaignMembersObj\",\n"
                    + "    \"type\": \"select_one\",\n"
                    + "    \"define_type\": \"package\",\n"
                    + "    \"api_name\": \"sign_in_status\",\n"
                    + "    \"label\": \"签到状态\",\n"
                    + "    \"help_text\": \"\",\n"
                    + "    \"is_required\": false,\n"
                    + "    \"is_unique\": false,\n"
                    + "    \"is_active\": true,\n"
                    + "    \"is_index\": true,\n"
                    + "    \"resource_bundle_key\": null,\n"
                    + "    \"status\": \"new\",\n"
                    + "    \"default_value\": \"\",\n"
                    + "    \"options\": [{\n"
                    + "        \"label\": \"未签到\",\n"
                    + "        \"value\": \"unsigned\"\n"
                    + "    }, {\n"
                    + "        \"label\": \"已签到\",\n"
                    + "        \"value\": \"sighed\"\n"
                    + "    }],\n"
                    + "    \"maxItems\": -1\n"
                    + "}");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        if (!apiNames.contains(CampaignConstants.SIGNED_TIME_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                "{\n"
                    + "    \"type\": \"date_time\",\n"
                    + "    \"define_type\": \"package\",\n"
                    + "    \"api_name\": \"signed_time\",\n"
                    + "    \"label\": \"签到时间\",\n"
                    + "    \"help_text\": \"\",\n"
                    + "    \"is_required\": false,\n"
                    + "    \"is_unique\": false,\n"
                    + "    \"is_active\": true,\n"
                    + "    \"is_index\": true,\n"
                    + "    \"resource_bundle_key\": null,\n"
                    + "    \"status\": \"new\",\n"
                    + "    \"default_value\": \"\",\n"
                    + "    \"default_is_expression\": false,\n"
                    + "    \"default_to_zero\": false,\n"
                    + "    \"time_zone\": \"GMT+8\",\n"
                    + "    \"date_format\": \"yyyy-MM-dd HH:mm\"\n"
                    + "}");
            arg.setLayoutList(
                "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"render_type\":\"date_time\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }


    /**
     * 活动成员对象添加直播数据字段
     * @param ea
     */
    public void appendCampaignMembersLiveData(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("OfficialWebsiteManager.appendCampaignMembersLiveData ea error ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 1.获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        // 2.添加 观看状态
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_WATCH_STAUTS)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"select_one\",\"define_type\":\"package\",\"api_name\":\"marketing_watch_stauts\",\"label\":\"观看状态\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"0\",\"options\":[{\"label\":\"已观看\",\"value\":\"1\"},{\"label\":\"未观看\",\"value\":\"0\"}],\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        // 3.添加 观看时长（分钟）
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_WATCH_DURATION)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"number\",\"define_type\":\"package\",\"api_name\":\"marketing_watch_duration\",\"label\":\"观看时长（分钟）\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"max_length\":14,\"length\":14,\"decimal_places\":0,\"round_mode\":4,\"default_to_zero\":true,\"display_style\":\"input\",\"step_value\":1,\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"number\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        // 4.添加 回放状态
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_PLAYBACK_STATUS)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"select_one\",\"define_type\":\"package\",\"api_name\":\"marketing_playback_status\",\"label\":\"回放状态\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"0\",\"options\":[{\"label\":\"已回放\",\"value\":\"1\"},{\"label\":\"未回放\",\"value\":\"0\"}],\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        // 5.添加 回放时长（分钟）
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_PLAYBACK_DURATION)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"number\",\"define_type\":\"package\",\"api_name\":\"marketing_playback_duration\",\"label\":\"回放时长（分钟）\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"max_length\":14,\"length\":14,\"decimal_places\":0,\"round_mode\":4,\"default_to_zero\":true,\"display_style\":\"input\",\"step_value\":1,\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"number\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        // 6.添加 互动状态
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_INTERACTION_STATUS)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"select_one\",\"define_type\":\"package\",\"api_name\":\"marketing_interaction_status\",\"label\":\"互动状态\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"0\",\"options\":[{\"label\":\"未互动\",\"value\":\"0\"},{\"label\":\"已互动\",\"value\":\"1\"}],\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        // 7.添加 互动次数
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_INTERACTION_FREQUENCY)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"number\",\"define_type\":\"package\",\"api_name\":\"marketing_interaction_frequency\",\"label\":\"互动次数\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"max_length\":14,\"length\":14,\"decimal_places\":0,\"round_mode\":4,\"default_to_zero\":true,\"display_style\":\"input\",\"step_value\":1,\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"number\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

    /**
     * 活动成员对象添加支付金额字段
     * @param ea
     */
    public void appendCampaignMembersPayData(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("OfficialWebsiteManager.appendCampaignMembersPayData ea error ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 1.获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        // 2.添加 支付金额
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_TOTAL_AMOUNT)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"number\",\"define_type\":\"package\",\"api_name\":\"" + CampaignMembersConstants.MARKETING_TOTAL_AMOUNT + "\",\"label\":\"支付总金额\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"max_length\":14,\"length\":14,\"decimal_places\":0,\"round_mode\":4,\"default_to_zero\":true,\"display_style\":\"input\",\"step_value\":1,\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"number\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

    /**
     * 活动成员对象添加推广人字段
     * @param ea
     */
    public void appendCampaignMembersSpreadData(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("CampaignMergeDataManager.appendCampaignMembersSpreadData ea error ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 1.获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        // 2.添加 推广人
        if (!apiNames.contains(CampaignConstants.SPREAD_USER_ID_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"describe_api_name\":\"CampaignMembersObj\",\"is_index\":true,\"is_single\":true,\"is_need_convert\": true,\"is_active\":true,\"is_unique\":false,\"label\":\"推广人\",\"type\":\"employee\",\"is_required\":false,\"api_name\":\""+CampaignConstants.SPREAD_USER_ID_API_NAME+"\",\"define_type\":\"package\",\"is_extend\":false,\"status\":\"new\"}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true,\"render_type\":\"employee\",\"is_show\":true,\"is_required\":false,\"is_readonly\":true}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }


    public void updateCampaignMembersObjSpreadData(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("CampaignMergeDataManager.updateCampaignMembersObjSpreadData ea error ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 1.获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        // 2.更新推广人
        if (apiNames.contains(CampaignConstants.SPREAD_USER_ID_API_NAME)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"describe_api_name\":\"CampaignMembersObj\",\"is_index\":true,\"is_single\":true,\"is_need_convert\": true,\"is_active\":true,\"is_unique\":false,\"label\":\"推广人\",\"type\":\"employee\",\"is_required\":false,\"api_name\":\""+CampaignConstants.SPREAD_USER_ID_API_NAME+"\",\"define_type\":\"package\",\"is_extend\":false,\"status\":\"new\"}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"默认布局\",\"is_default\":true,\"render_type\":\"employee\",\"is_show\":true,\"is_required\":false,\"is_readonly\":true}]"
            );
            objectDescribeCrmService.updateCustomFieldDescribe(systemHeader, arg);
        }
    }

    /**
     * 活动成员对象添加数据存入状态
     * @param ea
     */
    public void appendCampaignMemberDataSaveStatus(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("CampaignMergeDataManager.appendCampaignMemberDataSaveStatus ea error ea:{}", ea);
            return;
        }
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        // 1.获取活动成员对象描述
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
        if (CollectionUtils.isEmpty(crmFieldVOS)) {
            return;
        }
        List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
        // 2.添加 数据存入状态
        if (!apiNames.contains(CampaignMembersConstants.MARKETING_SAVE_STATUS)) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
            arg.setFieldDescribe(
                    "{\"type\":\"select_one\",\"define_type\":\"package\",\"api_name\":\"marketing_save_status\",\"label\":\"数据存入状态\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"0\",\"options\":[{\"label\":\"已存入\",\"value\":\"0\"},{\"label\":\"已关联\",\"value\":\"1\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"describe_api_name\":\"CampaignMembersObj\",\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_index_field\":false,\"is_single\":false,\"description\":\"\",\"is_extend\":false}"
            );
            arg.setLayoutList(
                    "[{\"api_name\":\"CampaignMembersObj_layout_generate_by_UDObjectServer__c\",\"label\":\"活动成员默认布局\",\"is_default\":true,\"layout_type\":\"detail\",\"render_type\":\"select_one\",\"is_show\":true,\"is_required\":false,\"is_readonly\":false}]"
            );
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

    /**
     * 修改邀约状态
     * @param inviteStatus
     * @param campaignMergeDataId
     */
    public void updateCampaignMergeDataInviteStatus(Integer inviteStatus, String campaignMergeDataId, boolean updateCrmObj) {
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataId);
        if (campaignMergeDataEntity == null) {
            return;
        }
        // 修改数据库
        campaignMergeDataDAO.updateCampaignMergeDataInviteStatus(inviteStatus, campaignMergeDataId);
        if (updateCrmObj) {
            CampaignMergeDataInviteStatusEnum campaignMergeDataInviteStatusEnum = CampaignMergeDataInviteStatusEnum.getByType(inviteStatus);
            // 修改对象数据
            if (campaignMergeDataInviteStatusEnum != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                Map<String, Object> editMap = Maps.newHashMap();
                editMap.put(CampaignConstants.INVITATION_STATUS_API_NAME, campaignMergeDataInviteStatusEnum.getCrmOptions());
                if (campaignMergeDataInviteStatusEnum == CampaignMergeDataInviteStatusEnum.NOT_INVITED) {
                    // 如果将邀约状态改为待邀约，参与状态 = 待邀约
                    editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue());
                } else {
                    // 处理将邀约状态改为已邀约的情况
                    String ea = campaignMergeDataEntity.getEa();
                    ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
                    if (objectData == null) {
                        return;
                    }
                    // 前提：邀约状态 = 已邀约
                    // 如果已签到，那么参与状态 = 已签到，如果未签到且审核状态=已审核，那么参与状态 = 审核通过，如果未签到且审核状态=未审核并且有表单提交数据，那么参与状态 = 已报名，
                    // 如果未签到且审核状态=审核未通过，并且没有表单提交数据，那么参与状态 = 已邀约
                    String signInStatus = objectData.getString(CampaignConstants.SIGN_IN_STATUS_API_NAME);
                    String approvalStatus = objectData.getString(CampaignConstants.APPROVAL_STATUS_API_NAME);
                    String marketingEventId = objectData.getString("marketing_event_id");
                    // 会议是否开启报名审核
                    ActivityEntity activityEntity = marketingEventId == null ? null : conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
                    boolean enrollReview = activityEntity != null && BooleanUtils.isTrue(activityEntity.getEnrollReview());
                    List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(campaignMergeDataId);
                    CampaignMembersObjMemberStatusEnum campaignMembersObjMemberStatus;
                    if (ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName().equals(signInStatus)) {
                        campaignMembersObjMemberStatus = CampaignMembersObjMemberStatusEnum.PARTICIPATE;
                    } else if (ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions().equals(approvalStatus)) {
                        campaignMembersObjMemberStatus = CampaignMembersObjMemberStatusEnum.REVIEW;
                    } else if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                        campaignMembersObjMemberStatus = CampaignMembersObjMemberStatusEnum.REGISTERED;
                    } else {
                        campaignMembersObjMemberStatus = CampaignMembersObjMemberStatusEnum.NOTICE;
                    }
                    // 如果未开启审核，且审核状态 = 已审核 那么参与状态 = 已邀约
                    if (!enrollReview && campaignMembersObjMemberStatus == CampaignMembersObjMemberStatusEnum.REVIEW) {
                        campaignMembersObjMemberStatus = CampaignMembersObjMemberStatusEnum.NOTICE;
                    }
                    editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), campaignMembersObjMemberStatus.getValue());

                }
                crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);
            }
        }
    }

    /**
     * 修改审核状态
     * @param conferenceEnrollIds
     * @param reviewStatus
     * @param reviewFailedMsg
     */
    public void updateConferenceReviewStatus(List<String> conferenceEnrollIds, Integer reviewStatus, String reviewFailedMsg, Boolean updateCampaignMembersObj) {
        ConferenceEnrollReviewStatusEnum conferenceEnrollReviewStatusEnum = ConferenceEnrollReviewStatusEnum.getByType(reviewStatus);
        if (CollectionUtils.isEmpty(conferenceEnrollIds) || conferenceEnrollReviewStatusEnum == null) {
            return;
        }
        activityEnrollDataDAO.updateReviewStatusByIds(conferenceEnrollIds, reviewStatus, reviewFailedMsg);
        if (updateCampaignMembersObj) {
            ThreadPoolUtils.execute(() -> {
                updateConferenceReviewStatusInThread(conferenceEnrollIds, conferenceEnrollReviewStatusEnum);
            }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        }
    }

    public void updateConferenceReviewStatusInThread(List<String> conferenceEnrollIds, ConferenceEnrollReviewStatusEnum conferenceEnrollReviewStatusEnum){
        List<String> campaignIds = activityEnrollIdToCampaignId(conferenceEnrollIds);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                if (campaignMergeDataEntity != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                    Map<String, Object> editMap = Maps.newHashMap();
                    editMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME, conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions());
                    crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);
                }
            }
        }
    }


    /**
     * 修改分组
     * @param activityEnrollIds
     * @param groupNames
     */
    public void updateCampaignMergeObjUserGroup(List<String> activityEnrollIds, List<String> groupNames) {
        if (CollectionUtils.isEmpty(activityEnrollIds) || CollectionUtils.isEmpty(groupNames)) {
            return;
        }
        String groupKey = null;
        if (CollectionUtils.isNotEmpty(groupNames)) {
            StringJoiner key = new StringJoiner(",");
            for (String groupName : groupNames) {
                key.add(groupName);
            }
            groupKey = key.toString();
        }
        String finalGroupKey = groupKey;
        ThreadPoolUtils.execute(() -> {
            List<String> campaignIds = activityEnrollIdToCampaignId(activityEnrollIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
                for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                    if (campaignMergeDataEntity != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                        Map<String, Object> editMap = Maps.newHashMap();
                        editMap.put(CampaignConstants.PARTICIPANTS_GROUP_API_NAME, finalGroupKey);
                        crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);
                    }
                }
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    /**
     * 修改活动成员参会码
     * @param campaignMergeDataById
     * @param code
     */
    public void updateParticipantsPasscode(String campaignMergeDataById, String code) {
        if (StringUtils.isBlank(campaignMergeDataById)) {
            return;
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeDataById);
        if (campaignMergeDataEntity == null || StringUtils.isBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
            return;
        }
        try {
            Thread.sleep(100);
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.updateParticipantsPasscode error e:{}", e);
        }
        Map<String, Object> editMap = Maps.newHashMap();
        editMap.put(CampaignConstants.PARTICIPANTS_PASSCODE_API_NAME, code);
        crmV2Manager.updateCampaign(campaignMergeDataEntity.getCampaignMembersObjId(), campaignMergeDataEntity.getEa(),-10000,editMap);
    }

    /**
     * 更新签到状态
     */
    public void updateSignInStatus(List<String> activityEnrollDataId, Integer signInStatus, boolean updateCampaignMemberObj) {
        if (CollectionUtils.isEmpty(activityEnrollDataId) || signInStatus == null) {
            return;
        }
        activityEnrollDataDAO.updateSignInTypeByIds(activityEnrollDataId, signInStatus);
        ThreadPoolUtils.execute(() -> {
            asyncUpdateSignInStatus(activityEnrollDataId, signInStatus, updateCampaignMemberObj);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void asyncUpdateSignInStatus(List<String> activityEnrollDataId, Integer signInStatus, boolean updateCampaignMemberObj){
        ActivitySignOrEnrollEnum activitySignOrEnrollEnum = ActivitySignOrEnrollEnum.getByType(signInStatus);
        if (activitySignOrEnrollEnum == null || StringUtils.isBlank(activitySignOrEnrollEnum.getCampaignMergeDataApiName())) {
            return;
        }
        List<String> campaignIds = activityEnrollIdToCampaignId(activityEnrollDataId);
        if (CollectionUtils.isEmpty(campaignIds)) {
            return;
        }

        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
            if (campaignMergeDataEntity != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                if (updateCampaignMemberObj) {
                    Map<String, Object> editMap = Maps.newHashMap();
                    editMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, activitySignOrEnrollEnum.getCampaignMergeDataApiName());
                    editMap.put(CampaignConstants.SIGNED_TIME_API_NAME, new Date().getTime());
                    // 签到的时候，直接将参与状态改为已签到
                    if (ActivitySignOrEnrollEnum.SIGN_IN == activitySignOrEnrollEnum) {
                        editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue());
                    }
                    crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);

                }
                ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId());
                if (conference != null) {
                    if(ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(signInStatus)){
                        Map<String, Object> paramMap = Maps.newHashMap();
                        ActivityEnrollDataEntity  activityEnrollDataEntity = campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId());
                        if (activityEnrollDataEntity != null) {
                            CustomizeFormDataUserEntity customizeFormDataUser = customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(campaignMergeDataEntity.getId());
                            if (customizeFormDataUser != null) {
                                paramMap.put("spreadFsUid", customizeFormDataUser.getSpreadFsUid());
                                paramMap.put("enrollId", customizeFormDataUser.getId());
                                paramMap.put("actionType", MarketingUserActionType.CONFERENCE_CHECK_IN.getActionType());
                            }
                        }
                        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getId(), MarketingSceneType.CONFERENCE.getType(), conference.getId(), TriggerActionTypeEnum.CONFERENCE_CHECK_IN.getTriggerActionType(), paramMap);
                    }
                }
            }
        }

    }

    public void updateCampaignMembersObjByCampaignMergeId(String campaignMergeId, Map<String, Object> editMap) {
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignMergeId);
        if (campaignMergeDataEntity == null || StringUtils.isBlank(campaignMergeDataEntity.getCampaignMembersObjId()) || MapUtils.isEmpty(editMap)) {
            return;
        }
        crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);
    }


    public void addCampaignMembersObjByBindObj(List<MemberObjDetail> memberObjDetails, String ea, String marketingEventId, String campaignMembersStatus, Integer fsUserId) {
        if (CollectionUtils.isEmpty(memberObjDetails) || StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea) || StringUtils.isBlank(campaignMembersStatus)) {
            return;
        }
        for (MemberObjDetail memberObjDetail : memberObjDetails) {
            Map<String, Object> dataMap = Maps.newHashMap();
            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByApiName(memberObjDetail.getApiName());
            if (campaignMergeDataObjectTypeEnum == null) {
                continue;
            }
            ObjectData objectData = null;
            try {
                objectData = crmV2Manager.getDetail(ea, -10000, memberObjDetail.getApiName(), memberObjDetail.getId());
                if (objectData == null) {
                    continue;
                }
            } catch (Exception e) {
                log.warn("CampaignMergeDataManager.addCampaignMembersObjByBindObj error e:{}", e);
                continue;
            }
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), memberObjDetail.getApiName());
            dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), marketingEventId);
            String name = objectData.getName();
            String companyName = objectData.getString("company");
            if (StringUtils.isNotBlank(companyName)) {
                dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
            }
            if (StringUtils.isNotBlank(name)) {
                dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
            }
            dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), memberObjDetail.getId());
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), campaignMembersStatus);
            crmV2Manager.addCampaignMembersObj(ea, dataMap, fsUserId);
        }
    }

    public void createOrUpdateConferenceInvitationUser(String ea, String conferenceId, Integer owner, String campaignMergeDataId, Long startTime, Long endTime, String invitationText) {
        ConferenceInvitationUserEntity conferenceInvitationUserEntity = conferenceInvitationUserDAO.getConferenceInvitationUserUnique(ea, conferenceId, owner, campaignMergeDataId);
        if (conferenceInvitationUserEntity == null) {
            conferenceInvitationUserEntity = new ConferenceInvitationUserEntity();
            conferenceInvitationUserEntity.setId(UUIDUtil.getUUID());
            conferenceInvitationUserEntity.setEa(ea);
            conferenceInvitationUserEntity.setActivityId(conferenceId);
            conferenceInvitationUserEntity.setUserId(owner);
            conferenceInvitationUserEntity.setStartTime(new Date(startTime));
            conferenceInvitationUserEntity.setCampaignMergeDataId(campaignMergeDataId);
            conferenceInvitationUserEntity.setEndTime(new Date(endTime));
            conferenceInvitationUserEntity.setInvitationText(invitationText);
            conferenceInvitationUserDAO.addConferenceInvitationUser(conferenceInvitationUserEntity);
        } else {
            conferenceInvitationUserDAO.updateConferenceInvitationUserStartTime(conferenceInvitationUserEntity.getId(), new Date(startTime), new Date(endTime), invitationText);
        }
    }

    public Map<String, String> getUserEmailByCampaignId(String ea, List<String> campaignIds) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return resultMap;
        }
        // 先从表单中寻找
        List<CampaignDataMailDTO> campaignDataMailDTOList = campaignMergeDataDAO.queryCampaignMailFromSubmitContent(campaignIds);
        if (CollectionUtils.isNotEmpty(campaignDataMailDTOList)) {
            for (CampaignDataMailDTO campaignDataMailDTO : campaignDataMailDTOList) {
                campaignIds.remove(campaignDataMailDTO.getCampaignId());
                resultMap.put(campaignDataMailDTO.getCampaignId(), campaignDataMailDTO.getEmail());
            }
        }
        if (CollectionUtils.isEmpty(campaignIds)) {
            return resultMap;
        }
        // 若为会员则查询会员数据
        List<CampaignDataMailDTO> campaignDataMemberList = campaignMergeDataDAO.queryCampaignFromMemberContent(campaignIds);
        if (CollectionUtils.isNotEmpty(campaignDataMemberList)) {
            PageUtil<CampaignDataMailDTO> campaignDataMailDTOPage = new PageUtil<>(campaignDataMemberList, 50);
            for (int i = 1; i <= campaignDataMailDTOPage.getPageCount(); i++) {
                List<CampaignDataMailDTO> campaignDataMailDTOS = campaignDataMailDTOPage.getPagedList(i);
                Map<String, String> campaignMemberIdMap = campaignDataMailDTOS.stream().collect(Collectors.toMap(CampaignDataMailDTO::getMemberId, CampaignDataMailDTO::getCampaignId, (v1, v2) -> v1));
                List<String> memberId = campaignDataMailDTOS.stream().map(CampaignDataMailDTO::getMemberId).collect(Collectors.toList());
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.addFilter("_id", new ArrayList<>(memberId), FilterOperatorEnum.IN);
                searchQuery.setOffset(0);
                searchQuery.setLimit(100);
                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), searchQuery, Lists.newArrayList("_id", "email"));
                if (result == null || CollectionUtils.isEmpty(result.getDataList())) {
                    continue;
                }
                List<ObjectData> memberData = result.getDataList();
                for (ObjectData objectData : memberData) {
                    String campaignId = campaignMemberIdMap.get(objectData.getId());
                    String email = objectData.getString("email");
                    if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(email)) {
                        continue;
                    }
                    campaignIds.remove(campaignId);
                    resultMap.put(campaignId, email);
                }
            }
        }
        if (CollectionUtils.isEmpty(campaignIds)) {
            return resultMap;
        }
        // 报名表不存在的直接去绑定对象中查找
        Map<String, ObjectData> objectDataMap = getObjectDataByCampaignMergeData(campaignIds);
        if (MapUtils.isEmpty(objectDataMap)) {
            return resultMap;
        }
        for (Map.Entry<String, ObjectData> entry : objectDataMap.entrySet()) {
            ObjectData objectData = entry.getValue();
            String email = objectData.getString("email");
            if (StringUtils.isNotBlank(email)) {
                resultMap.put(entry.getKey(), email);
            }
        }
        return resultMap;
    }
    
    public Map<String, String> getMarketingUserIdByCampaignIds(String ea, Collection<String> campaignIds){
        Map<String, String> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return resultMap;
        }
        List<CampaignMergeDataEntity> campaignMergeData = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        // 通过参与人员的手机查找营销用户
        List<String> phones = campaignMergeData.stream().map(CampaignMergeDataEntity::getPhone).filter(phone -> !Strings.isNullOrEmpty(phone)).collect(Collectors.toList());
        if(!phones.isEmpty()){
            List<UserMarketingAccountEntity> userMarketingAccounts = userMarketingAccountDAO.getUserMarketingAccountByPhones(ei, phones);
            Map<String, String> phoneToMarketingUserIdMap = userMarketingAccounts.stream().collect(Collectors.toMap(UserMarketingAccountEntity::getPhone, UserMarketingAccountEntity::getId, (v1, v2) -> v1));
            campaignMergeData.stream().filter(mergeData -> !Strings.isNullOrEmpty(mergeData.getPhone())).forEach(mergeData -> {
                if (!Strings.isNullOrEmpty(phoneToMarketingUserIdMap.get(mergeData.getPhone()))) {
                    resultMap.putIfAbsent(mergeData.getId(), phoneToMarketingUserIdMap.get(mergeData.getPhone()));
                }
            });
        }
        campaignMergeData = campaignMergeData.stream().filter(mergeData -> !resultMap.containsKey(mergeData.getId())).collect(Collectors.toList());
        if (campaignMergeData.isEmpty()){
            return resultMap;
        }
        
        // 活动成员（线索、客户、联系人）关联
        List<CampaignMergeDataEntity> campaignMergeDataThatHaveBindCrmObject = campaignMergeData.stream().filter(mergeData -> mergeData.getBindCrmObjectType() != null && !Strings.isNullOrEmpty(mergeData.getBindCrmObjectId())).collect(Collectors.toList());
        Map<Integer, List<CampaignMergeDataEntity>> bindObjectTypeToCampaignMergeDataMap = campaignMergeDataThatHaveBindCrmObject.stream().collect(Collectors.groupingBy(CampaignMergeDataEntity::getBindCrmObjectType));
        bindObjectTypeToCampaignMergeDataMap.forEach((objectType, mergeDataList) -> {
            List<ObjectData> objectDataList = mergeDataList.stream().map(mergeData -> {
                ObjectData objectData = new ObjectData(CampaignMergeDataObjectTypeEnum.getByType(objectType).getApiName());
                objectData.put("_id", mergeData.getBindCrmObjectId());
                objectData.put("mobile", mergeData.getPhone());
                objectData.put("tel", mergeData.getPhone());
                objectData.put("phone", mergeData.getPhone());
                return objectData;
            }).collect(Collectors.toList());
            Map<String, String> objectIdToMarketingUserIdMap = userMarketingAccountManager.associateAncGetObjectIdToMarketingUserIdMap(ea, CampaignMergeDataObjectTypeEnum.getByType(objectType).getApiName(), objectDataList);
            for (CampaignMergeDataEntity mergeData : mergeDataList) {
                if (!Strings.isNullOrEmpty(objectIdToMarketingUserIdMap.get(mergeData.getBindCrmObjectId()))) {
                    resultMap.putIfAbsent(mergeData.getId(), objectIdToMarketingUserIdMap.get(mergeData.getBindCrmObjectId()));
                }
            }
        });
        campaignMergeData = campaignMergeData.stream().filter(mergeData -> !resultMap.containsKey(mergeData.getId())).collect(Collectors.toList());
        if (campaignMergeData.isEmpty()){
            return resultMap;
        }
        
            // 从会员数据里查找
        Map<String, String> campaignIdToPhoneMap = campaignMergeData.stream().filter(c -> StringUtils.isNotBlank(c.getPhone())).collect(Collectors.toMap(CampaignMergeDataEntity::getId, CampaignMergeDataEntity::getPhone, (v1, v2) -> v1));
        List<String> campaignIdThatPossibleAssociateMember = campaignMergeData.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
        List<MemberAccessibleCampaignEntity> memberAccessibleCampaigns = memberAccessibleCampaignDAO.getLatestAccessibleMemberByCampaignIds(campaignIdThatPossibleAssociateMember);
        if (!memberAccessibleCampaigns.isEmpty()){
            List<ObjectData> objectDataList = memberAccessibleCampaigns.stream().map(memberAccessibleCampaign -> {
                ObjectData objectData = new ObjectData(CrmObjectApiNameEnum.MEMBER.getName());
                objectData.put("_id", memberAccessibleCampaign.getMemberId());
                objectData.put("mobile", campaignIdToPhoneMap.get(memberAccessibleCampaign.getCampaignId()));
                objectData.put("tel", campaignIdToPhoneMap.get(memberAccessibleCampaign.getCampaignId()));
                objectData.put("phone", campaignIdToPhoneMap.get(memberAccessibleCampaign.getCampaignId()));
                return objectData;
            }).collect(Collectors.toList());
            Map<String, String> objectIdToMarketingUserIdMap = userMarketingAccountManager.associateAncGetObjectIdToMarketingUserIdMap(ea, CrmObjectApiNameEnum.MEMBER.getName(), objectDataList);
            for (MemberAccessibleCampaignEntity memberAccessibleCampaign : memberAccessibleCampaigns) {
                if (!Strings.isNullOrEmpty(objectIdToMarketingUserIdMap.get(memberAccessibleCampaign.getMemberId()))) {
                    resultMap.putIfAbsent(memberAccessibleCampaign.getCampaignId(), objectIdToMarketingUserIdMap.get(memberAccessibleCampaign.getMemberId()));
                }
            }
        }
        campaignMergeData = campaignMergeData.stream().filter(mergeData -> !resultMap.containsKey(mergeData.getId())).collect(Collectors.toList());
        if (campaignMergeData.isEmpty()){
            return resultMap;
        }
        
        // 从表单的其他渠道查找，包括fingerPrintId、uid、wx_app_id + wx_open_id
        List<String> campaignIdThatPossibleAssociateForm = campaignMergeData.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
        List<CustomizeFormDataUserEntity> customizeFormDataUsers = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(campaignIdThatPossibleAssociateForm);
        List<CustomizeFormDataUserEntity> fingerPrintCustomizeFormDataUsers = customizeFormDataUsers.stream().filter(dataUser -> !Strings.isNullOrEmpty(dataUser.getFingerPrint())).collect(Collectors.toList());
        if (!fingerPrintCustomizeFormDataUsers.isEmpty()){
            List<String> browserUserIds = fingerPrintCustomizeFormDataUsers.stream().map(CustomizeFormDataUserEntity::getFingerPrint).collect(Collectors.toList());
            List<UserMarketingBrowserUserRelationEntity> relations = userMarketingBrowserUserRelationDao.listByBrowserUserIds(ea, browserUserIds);
            Map<String, String> browserUserIdToMarketingUserIdMap = relations.stream().collect(Collectors.toMap(UserMarketingBrowserUserRelationEntity::getBrowserUserId, UserMarketingBrowserUserRelationEntity::getUserMarketingId, (v1, v2) -> v1));
            for (CustomizeFormDataUserEntity fingerPrintCustomizeFormDataUser : fingerPrintCustomizeFormDataUsers) {
                if (!Strings.isNullOrEmpty(browserUserIdToMarketingUserIdMap.get(fingerPrintCustomizeFormDataUser.getFingerPrint()))){
                    resultMap.putIfAbsent(fingerPrintCustomizeFormDataUser.getCampaignId(), browserUserIdToMarketingUserIdMap.get(fingerPrintCustomizeFormDataUser.getFingerPrint()));
                }
            }
        }
        List<CustomizeFormDataUserEntity> mankeepCustomizeFormDataUsers = customizeFormDataUsers.stream().filter(dataUser -> !Strings.isNullOrEmpty(dataUser.getUid())).collect(Collectors.toList());
        if (!mankeepCustomizeFormDataUsers.isEmpty()){
            List<String> uids = mankeepCustomizeFormDataUsers.stream().map(CustomizeFormDataUserEntity::getUid).collect(Collectors.toList());
            List<UserMarketingMiniappAccountRelationEntity> relations = userMarketingMiniappAccountRelationDao.listAllByUids(ea, uids);
            Map<String, String> uidToMarketingUserIdMap = relations.stream().collect(Collectors.toMap(UserMarketingMiniappAccountRelationEntity::getUid, UserMarketingMiniappAccountRelationEntity::getUserMarketingId, (v1, v2) -> v1));
            for (CustomizeFormDataUserEntity mankeepCustomizeFormDataUser : mankeepCustomizeFormDataUsers) {
                if (!Strings.isNullOrEmpty(uidToMarketingUserIdMap.get(mankeepCustomizeFormDataUser.getUid()))){
                    resultMap.putIfAbsent(mankeepCustomizeFormDataUser.getCampaignId(), uidToMarketingUserIdMap.get(mankeepCustomizeFormDataUser.getUid()));
                }
            }
        }
        List<CustomizeFormDataUserEntity> wxCustomizeFormDataUsers = customizeFormDataUsers.stream().filter(dataUser -> !Strings.isNullOrEmpty(dataUser.getWxAppId()) && !Strings.isNullOrEmpty(dataUser.getOpenId())).collect(Collectors.toList());
        if(!wxCustomizeFormDataUsers.isEmpty()){
            wxCustomizeFormDataUsers.stream().collect(Collectors.groupingBy(CustomizeFormDataUserEntity::getWxAppId)).forEach((wxAppId, partWxCustomizeFormDataUsers) -> {
                if (!partWxCustomizeFormDataUsers.isEmpty()){
                    List<String> wxOpenIds = partWxCustomizeFormDataUsers.stream().map(CustomizeFormDataUserEntity::getOpenId).collect(Collectors.toList());
                    List<UserMarketingWxServiceAccountRelationEntity> relations = userMarketingWxServiceAccountRelationDao.listByWxOpenIds(ea, wxAppId, wxOpenIds);
                    Map<String, String> wxOpenIdToMarketingUserIdMap = relations.stream().collect(Collectors.toMap(UserMarketingWxServiceAccountRelationEntity::getWxOpenId, UserMarketingWxServiceAccountRelationEntity::getUserMarketingId, (v1, v2) -> v1));
                    for (CustomizeFormDataUserEntity wxCustomizeFormDataUser : partWxCustomizeFormDataUsers) {
                        if (!Strings.isNullOrEmpty(wxOpenIdToMarketingUserIdMap.get(wxCustomizeFormDataUser.getOpenId()))){
                            resultMap.putIfAbsent(wxCustomizeFormDataUser.getCampaignId(), wxOpenIdToMarketingUserIdMap.get(wxCustomizeFormDataUser.getOpenId()));
                        }
                    }
                }
            });
        }
        
        return resultMap;
    }

    public Map<String, ObjectData> getObjectDataByCampaignMergeData(List<String> campaignIds) {
        Map<String, ObjectData> resultMap = Maps.newConcurrentMap();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return resultMap;
        }
        Map<String, List<String>> apiNameIdMap = Maps.newHashMap();
        Map<String, String> campaignMergeDataBindMap = Maps.newConcurrentMap();
        for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
            if (StringUtils.isBlank(campaignMergeDataEntity.getBindCrmObjectId()) || campaignMergeDataEntity.getBindCrmObjectType() == null) {
                continue;
            }
            String apiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(campaignMergeDataEntity.getBindCrmObjectType());
            if (CollectionUtils.isEmpty(apiNameIdMap.get(apiName))) {
                apiNameIdMap.put(apiName, Lists.newArrayList(campaignMergeDataEntity.getBindCrmObjectId()));
            } else {
                apiNameIdMap.get(apiName).add(campaignMergeDataEntity.getBindCrmObjectId());
            }
            campaignMergeDataBindMap.put(apiName + "#" + campaignMergeDataEntity.getBindCrmObjectId(), campaignMergeDataEntity.getId());
        }
        if (MapUtils.isEmpty(apiNameIdMap)) {
            return resultMap;
        }
        String ea = campaignMergeDataEntityList.get(0).getEa();
        // 查询多对象
        CountDownLatch countDownLatch = new CountDownLatch(apiNameIdMap.size());
        for (Map.Entry<String, List<String>> entry : apiNameIdMap.entrySet()) {
            ThreadPoolUtils.execute(() -> {
                Page<ObjectData> objectDataPage;
                try {
                    SearchQuery searchQuery = new SearchQuery();
                    searchQuery.setLimit(1000);
                    searchQuery.setOffset(0);
                    searchQuery.addFilter(CrmV2LeadFieldEnum.ID.getNewFieldName(), entry.getValue(), OperatorConstants.IN);
                    objectDataPage = crmV2Manager.getList(ea, -10000, entry.getKey(), searchQuery);
                    if (objectDataPage == null || CollectionUtils.isEmpty(objectDataPage.getDataList())) {
                        return;
                    }
                    for (ObjectData objectData : objectDataPage.getDataList()) {
                        String campaignId = campaignMergeDataBindMap.get(entry.getKey() + "#" + objectData.getId());
                        if (StringUtils.isBlank(campaignId)) {
                            continue;
                        }
                        resultMap.put(campaignId, objectData);
                    }
                } catch (Exception e) {
                    log.warn("CampaignMergeDataManager.getObjectDataByCampaignMergeData countDownLatch error e:{}", e);
                } finally {
                    objectDataPage = null;
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        }
        try {
            countDownLatch.await(8L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.getObjectDataByCampaignMergeData error e:{}", e);
        }
        campaignMergeDataEntityList.clear();
        apiNameIdMap.clear();
        campaignMergeDataBindMap.clear();
        return resultMap;
    }

    public Map<String, Integer> countPayOrderNumber(String ea, Collection<String> campaignIds){
        if (campaignIds == null || campaignIds.isEmpty()){
            return new HashMap<>(0);
        }
        return campaignPayOrderDao.groupCountPayOrderByCampaignIds(ea, campaignIds).stream().collect(DataCount.getMapCollector());
    }

    public Map<String, WxUserData> queryWxUserInfo(String ea, String wxAppId, List<String> wxOpenId) {
        Map<String, WxUserData> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(wxAppId) || CollectionUtils.isEmpty(wxOpenId)) {
            return resultMap;
        }
        Map<String, ObjectData> wxUserObjInfo = crmV2Manager.getWechatFanByOpenIds(ea, wxAppId, wxOpenId);
        if (MapUtils.isEmpty(wxUserObjInfo)) {
            return resultMap;
        }
        try {
            for (Map.Entry<String, ObjectData> entry : wxUserObjInfo.entrySet()) {
                WxUserData wxUserData = new WxUserData();
                ObjectData objectData = entry.getValue();
                if (objectData == null) {
                    continue;
                }
                String name = objectData.getString(CrmWechatFanFieldEnum.NAME.getFieldName());
                wxUserData.setName(name);
                // 设置图片
                Object imageObject = objectData.get(CrmWechatFanFieldEnum.WX_HEAD_IMAGE.getFieldName());
                if (imageObject != null) {
                    List<Map<String, Object>> imageDataMapList = GsonUtil.fromJson(GsonUtil.toJson(imageObject), new TypeToken<List<Map<String, Object>>>() {
                    }.getType());
                    if (CollectionUtils.isNotEmpty(imageDataMapList)) {
                        // 取第一张图片
                        Map<String, Object> imagePathMap = imageDataMapList.get(0);
                        String imagePath = imagePathMap.get("path") + "." + imagePathMap.get("ext");
                        String url = fileV2Manager.getUrlByPath(imagePath, ea, false);
                        wxUserData.setUserAvatar(url);
                    }
                }
                resultMap.put(objectData.getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()), wxUserData);
            }
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.queryWxUserInfo error e:{}", e);
        }
        return resultMap;
    }

    public Map<String, Integer> getCampaignMergeCountByMarketingIds(String ea, List<String> marketingEventIds) {
        Map<String, Integer> result = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(marketingEventIds)) {
            return result;
        }
        List<CampaignStatisticDTO> campaignStatisticDTOList = campaignMergeDataDAO.getCampaignMergeCountByMarketingIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(campaignStatisticDTOList)) {
            result = campaignStatisticDTOList.stream().collect(Collectors.toMap(CampaignStatisticDTO::getMarketingEventId, CampaignStatisticDTO::getCampaignCount, (v1, v2) -> v1));
        }
        return result;
    }

    public String queryCampaignMergeDataWithTry(String ea, String marketingEventId, Integer bindObjType, String bindObjId, int tryTime, long sleepTime) throws InterruptedException {
        if (tryTime <= 0) {
            return null;
        }
        if (StringUtils.isBlank(ea) || bindObjType == null || StringUtils.isBlank(bindObjId) || StringUtils.isBlank(marketingEventId)) {
            return null;
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataByBindObject(ea, marketingEventId, bindObjType, bindObjId);
        if (campaignMergeDataEntity != null) {
            return campaignMergeDataEntity.getId();
        } else {
            Thread.sleep(sleepTime);
        }
        return queryCampaignMergeDataWithTry(ea, marketingEventId, bindObjType, bindObjId, --tryTime, sleepTime);
    }

    public void asyncUpdateSignInStatusAndUid(List<String> activityEnrollDataId, Integer signInStatus, String uid){
        ActivitySignOrEnrollEnum activitySignOrEnrollEnum = ActivitySignOrEnrollEnum.getByType(signInStatus);
        if (activitySignOrEnrollEnum == null || StringUtils.isBlank(activitySignOrEnrollEnum.getCampaignMergeDataApiName())) {
            return;
        }
        List<String> campaignIds = activityEnrollIdToCampaignId(activityEnrollDataId);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                if (campaignMergeDataEntity != null && StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                    Map<String, Object> editMap = Maps.newHashMap();
                    editMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, activitySignOrEnrollEnum.getCampaignMergeDataApiName());
                    editMap.put(CampaignConstants.SIGNED_TIME_API_NAME, new Date().getTime());
                    if (signInStatus.equals(ActivitySignOrEnrollEnum.SIGN_IN.getType())) {
                        editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue());
                    }
                    crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId(), editMap);

                    ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getMarketingEventId());
                    if (conference != null) {
                        if(ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(signInStatus)){
                            Map<String, Object> paramMap = Maps.newHashMap();
                            ActivityEnrollDataEntity  activityEnrollDataEntity = campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getCampaignMembersObjId());
                            if (activityEnrollDataEntity != null) {
                                CustomizeFormDataUserEntity customizeFormDataUser = customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(campaignMergeDataEntity.getId());
                                if (customizeFormDataUser != null) {
                                    paramMap.put("spreadFsUid", customizeFormDataUser.getSpreadFsUid());
                                    paramMap.put("enrollId", customizeFormDataUser.getId());
                                    paramMap.put("actionType", MarketingUserActionType.CONFERENCE_CHECK_IN.getActionType());
                                }
                            }
                            triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(campaignMergeDataEntity.getEa(), campaignMergeDataEntity.getId(), MarketingSceneType.CONFERENCE.getType(),conference.getId(), TriggerActionTypeEnum.CONFERENCE_CHECK_IN.getTriggerActionType(), paramMap);
                        }
                    }
                }
            }
        }
    }
    public void updateSignInStatusAndUid(List<String> activityEnrollDataId, Integer signInStatus, String uid) {
        if (CollectionUtils.isEmpty(activityEnrollDataId) || signInStatus == null) {
            return;
        }
        activityEnrollDataDAO.updateSignInTypeAndUidByIds(activityEnrollDataId, signInStatus,uid);
        ThreadPoolUtils.execute(() -> {
            asyncUpdateSignInStatusAndUid(activityEnrollDataId, signInStatus, uid);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void updateCampaignMembersObj(String ea, String campaignMembersObjId, Map<String, Object> dataMap) {
        try {
            crmV2Manager.editCampaignMembersObj(ea, campaignMembersObjId, dataMap);
        } catch (Exception e) {
            log.info("CampaignMergeDataManager editCampaignMembersObj edit error ea:{} campaignMembersObjId:{} dataMap:{}", ea, campaignMembersObjId, JSON.toJSONString(dataMap));
        }
    }

    @FilterLog
    public List<ObjectData> getCampaignMembersObjByMaketingEventIds(String ea, List<String> maketingEventIds) {
        if (null == maketingEventIds || maketingEventIds.isEmpty()) {
            return null;
        }
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 10000);
        paasQueryArg.addFilter("marketing_event_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), maketingEventIds);
        queryFilterArg.setQuery(paasQueryArg);
        InnerPage<ObjectData> list = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
        return list == null ? null : list.getDataList();
    }

    public List<ObjectData> getCampaignMembersObjByMaketingEventIdsWithFields(String ea, List<String> maketingEventIds) {
        if (null == maketingEventIds || maketingEventIds.isEmpty()) {
            return null;
        }

        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 10000);
        paasQueryArg.addFilter("marketing_event_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), maketingEventIds);
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id", "marketing_event_id", "marketing_watch_stauts", "marketing_playback_status");
        queryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData> list = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
        return list == null ? null : list.getDataList();
    }


    public String addCampaignMemberByCrmData(String ea, Integer fsUserId, String marketingEventId, Integer crmObjectType, String crmObjectId) {
        Optional<CampaignMergeDataEntity> optionalMergeDataEntity = buildCampaignMemberObjData(ea, crmObjectType, crmObjectId, marketingEventId, CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ);
        if (!optionalMergeDataEntity.isPresent()) {
            return null;
        }

        String CampaignMergeEntityId = null;
        CampaignMergeDataEntity campaignMergeDataEntity = optionalMergeDataEntity.get();
        Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), marketingEventId);
        String campaignDataObjId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, crmObjectType, crmObjectId, marketingEventId);
        if (campaignDataObjId != null) {
            campaignMergeDataEntity.setCampaignMembersObjId(campaignDataObjId);
            CampaignMergeEntityId = addCampaignDataOnlyUnLock(optionalMergeDataEntity.get());
        }

        return CampaignMergeEntityId;
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());

        return dataMap;
    }

    private Optional<CampaignMergeDataEntity> buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId, CampaignMergeDataSourceTypeEnum source) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return Optional.empty();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error e:{}", e);
        }
        if (objectData == null) {
            return Optional.empty();
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = this.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(source.getType());

        return Optional.of(campaignMergeDataEntity);
    }

    /**
     * 查询市场活动的活动成员数据
     * @param ea
     * @param marketingEventId
     * @return
     */
    public List<CampaignMergeDataEntity> listByMarketingEventId(String ea, String marketingEventId){
        return campaignMergeDataDAO.getCampaignMergeDataByMarketingEventIdAndEa(marketingEventId, ea);
    }

    /**
     * 根据主键id集合批量查询数据
     * @param ea
     * @param ids
     * @return
     */
    public List<CampaignMergeDataEntity> listByIds(String ea, List<String> ids){
        return campaignMergeDataDAO.getCampaignMergeDataByIds(ids);
    }

    // 这里处理 通过 活动成员 对象上的更新状态按钮更新参与状态时，要同步更新 邀约状态、审核状态、签到状态这几个字段
    public void handleCampaignMembersStatusChangeEvent(String ea, CrmEventDTO.Body body) {
        // 刷数据期间不处理，刷完之后把代码和配置去掉
        if (!"1".equals(campaignStatusEnable)) {
            return;
        }
        if (MapUtils.isEmpty(body.getAfterTriggerData()) || StringUtils.isBlank(ea)) {
            return;
        }
        String campaignMembersStatus = body.getAfterTriggerData().getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName());
        //如果参与状态没有变化 直接返回
        if (StringUtils.isBlank(campaignMembersStatus)) {
            return;
        }
        CampaignMembersObjMemberStatusEnum campaignMembersObjMemberStatusEnum = CampaignMembersObjMemberStatusEnum.getByValue(campaignMembersStatus);
        if (campaignMembersObjMemberStatusEnum == null || CampaignMembersObjMemberStatusEnum.OTHER == campaignMembersObjMemberStatusEnum) {
            return;
        }
        String invitationStatus = body.getAfterTriggerData().getString(CampaignConstants.INVITATION_STATUS_API_NAME);
        String approvalStatus = body.getAfterTriggerData().getString(CampaignConstants.APPROVAL_STATUS_API_NAME);
        String signInStatus = body.getAfterTriggerData().getString(CampaignConstants.SIGN_IN_STATUS_API_NAME);
        // 如果这几个字段有值，说明不是通过 更新状态 按钮触发的  直接返回
        if (StringUtils.isNotBlank(invitationStatus) || StringUtils.isNotBlank(approvalStatus) || StringUtils.isNotBlank(signInStatus)) {
            return;
        }
        String objectId = body.getObjectId();
        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), objectId);
        if (objectData == null) {
            log.warn("handleCampaignMembersStatusChangeEvent objectData is null ea:{} objectId:{}", ea, objectId);
            return;
        }
        // 根据市场活动获取会议
        String marketingEventId = objectData.getString(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        if (StringUtils.isBlank(marketingEventId)) {
            log.warn("handleCampaignMembersStatusChangeEvent marketingEventId is null ea:{} objectId:{}", ea, objectId);
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity == null) {
            log.warn("handleCampaignMembersStatusChangeEvent activityEntity is null ea:{} objectId:{}", ea, objectId);
            return;
        }
        // 是否开启审核开关
        boolean isNeedApproval = BooleanUtils.isTrue(activityEntity.getEnrollReview());
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataByObjId(ea, objectId);
        if (campaignMergeDataEntity == null) {
            log.info("handleCampaignMembersStatusChangeEvent campaignMergeDataEntity is null ea:{} objectId:{}", ea, objectId);
            return;
        }
        // 当前最新的状态
        String latestInvitationStatus = objectData.getString(CampaignConstants.INVITATION_STATUS_API_NAME);
        String latestApprovalStatus = objectData.getString(CampaignConstants.APPROVAL_STATUS_API_NAME);
        String latestSignInStatus = objectData.getString(CampaignConstants.SIGN_IN_STATUS_API_NAME);
        // 要更新的状态
        String updateInvitationStatus;
        String updateApprovalStatus;
        String updateSignInStatus;
        if (CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue().equals(campaignMembersStatus)) {
            // 修改参与状态 = 待邀约   --> 更新 邀约状态 = 待邀约  审核状态 = 如果开启了审核开关，审核状态 = 未审核，否则是已审核  签到状态 = 未签到
            updateInvitationStatus = CampaignMergeDataInviteStatusEnum.NOT_INVITED.getCrmOptions();
            updateApprovalStatus = isNeedApproval ?  ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getCampaignMembersObjOptions() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions();
            updateSignInStatus = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName();
        } else  if (CampaignMembersObjMemberStatusEnum.NOTICE.getValue().equals(campaignMembersStatus)) {
            // 修改参与状态 = 已邀约   --> 更新 邀约状态 = 已邀约  审核状态 = 如果开启了审核开关，审核状态 = 未审核，否则是已审核  签到状态 = 未签到
            updateInvitationStatus = CampaignMergeDataInviteStatusEnum.INVITED.getCrmOptions();
            updateApprovalStatus = isNeedApproval ?  ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getCampaignMembersObjOptions() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions();
            updateSignInStatus = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName();
        } else  if (CampaignMembersObjMemberStatusEnum.REGISTERED.getValue().equals(campaignMembersStatus)) {
            // 修改参与状态 = 已报名   --> 更新 邀约状态 = 已邀约  审核状态 = 如果开启了审核开关，审核状态 = 未审核，否则是已审核  签到状态 = 未签到
            updateInvitationStatus = CampaignMergeDataInviteStatusEnum.INVITED.getCrmOptions();
            updateApprovalStatus = isNeedApproval ?  ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getCampaignMembersObjOptions() : ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions();
            updateSignInStatus = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName();
        } else  if (CampaignMembersObjMemberStatusEnum.REVIEW.getValue().equals(campaignMembersStatus)) {
            // 修改参与状态 = 已审核   --> 更新 邀约状态 = 已邀约  审核状态 = 已审核  签到状态 = 未签到
            updateInvitationStatus = CampaignMergeDataInviteStatusEnum.INVITED.getCrmOptions();
            updateApprovalStatus = ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions();
            updateSignInStatus = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getCampaignMergeDataApiName();
        } else {
            // 修改参与状态 = 已签到  --> 更新 邀约状态 = 已邀约  审核状态 = 已审核  签到状态 = 已签到
            updateInvitationStatus = CampaignMergeDataInviteStatusEnum.INVITED.getCrmOptions();
            updateApprovalStatus = ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions();
            updateSignInStatus = ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName();
        }
        log.info("handleCampaignMembersStatusChangeEvent  ea:{} objectId:{} latestInvitationStatus:{} latestApprovalStatus:{} latestSignInStatus:{} updateInvitationStatus:{} updateApprovalStatus:{} updateSignInStatus:{}",
                ea, objectId, latestInvitationStatus, latestApprovalStatus, latestSignInStatus, updateInvitationStatus, updateApprovalStatus, updateSignInStatus);
        Map<String, Object> editMap = Maps.newHashMap();
        if (!updateInvitationStatus.equals(latestInvitationStatus)) {
            editMap.put(CampaignConstants.INVITATION_STATUS_API_NAME, updateInvitationStatus);
        }
        if (!updateApprovalStatus.equals(latestApprovalStatus)) {
            editMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME, updateApprovalStatus);
        }
        if (!updateSignInStatus.equals(latestSignInStatus)) {
            editMap.put(CampaignConstants.SIGN_IN_STATUS_API_NAME, updateSignInStatus);
        }
        // 如果都没有更新 直接返回
        if (MapUtils.isEmpty(editMap)) {
            log.info("handleCampaignMembersStatusChangeEvent  ea:{} objectId:{} 不需要更新状态", ea, objectId);
            return ;
        }
        long count = redisManager.incCampaignMembersStatusChangeCount(ea, objectId);
        if (count > 20) {
            // 为了避免死循环，这次5分钟之内更新20的话 就不处理了
            log.info("handleCampaignMembersStatusChangeEvent  ea:{} objectId:{} 更新太多次了", ea, objectId);
            return ;
        }
        crmV2Manager.editCampaignMembersObj(ea, objectId, editMap);
        ActivityEnrollDataEntity activityEnrollDataEntity = campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(ea, objectId);
        // 如果有更新签到状态，处理签到的业务逻辑
        if (editMap.containsKey(CampaignConstants.SIGN_IN_STATUS_API_NAME)) {
            if (activityEnrollDataEntity != null) {
                if (updateApprovalStatus.equals(ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName())) {
                    // 如果是签到 重新走一遍签到的逻辑
                    updateSignInStatus(Lists.newArrayList(activityEnrollDataEntity.getId()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), false);
                } else {
                    activityEnrollDataDAO.updateSignInTypeByIds(Lists.newArrayList(activityEnrollDataEntity.getId()), ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                }
            }
        }
        // 如果有更新审核状态，处理审核的业务逻辑
        if (editMap.containsKey(CampaignConstants.APPROVAL_STATUS_API_NAME)) {
            if (updateApprovalStatus.equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions())) {
                // 审核成功，重新走一遍审核的逻辑
                ChangeConferenceParticipantsReviewStatusVO vo = new ChangeConferenceParticipantsReviewStatusVO();
                vo.setEa(ea);
                vo.setCampaignIds(Lists.newArrayList(campaignMergeDataEntity.getId()));
                //这里不要更新活动成员对象了 避免死循环
                vo.setUpdateCampaignMemberObj(false);
                vo.setReviewStatus(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                vo.setUserId(SuperUserConstants.USER_ID);
                conferenceService.changeConferenceParticipantsReviewStatus(vo);
            } else {
                if (activityEnrollDataEntity != null) {
                    updateConferenceReviewStatus(Lists.newArrayList(activityEnrollDataEntity.getActivityId()), ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus(), null, false);
                }
            }
        }
        // 如果有更新邀约状态状态，处理邀约的业务逻辑
        if (editMap.containsKey(CampaignConstants.INVITATION_STATUS_API_NAME)) {
            int type = updateInvitationStatus.equals(CampaignMergeDataInviteStatusEnum.NOT_INVITED.getCrmOptions()) ? CampaignMergeDataInviteStatusEnum.NOT_INVITED.getType() : CampaignMergeDataInviteStatusEnum.INVITED.getType();
            updateCampaignMergeDataInviteStatus(type, campaignMergeDataEntity.getId(), false);
        }
    }

    @Data
    public static class AddCampaignMergeDataByMemberArgContainer implements Serializable {

        // 企业ea(新建必填)
        private String ea;

        // 市场活动id(新建必填)
        private String marketingEventId;

        // 营销活动id
        private String marketingActivityId;

        // 会员id(新建必填)
        private String memberId;

        // 关联表id(更新必填)
        private String memberAccessibleCampaignId;

        // 总表id
        private String campaignId;

        // 是否在没有绑定crm情况下创建新的总表数据
        private boolean needCreateDataIfNoBindCRM;

        // 报名姓名（新建必填）
        private String name;

        // 手机
        private String phone;

        // 物料id(新建必填)
        private String objectId;

        // 物料类型（新建必填）
        private Integer objectType;

        private Integer bindObjectType;

        private String bindObjectId;

        // 渠道值
        private String channelValue;

        // 推广人
        private Integer spreadFsUid;

        private Integer saveCrmStatus;

        private String saveCrmErrorMessage;

        private String uid;

        private String openId;

        private String wxAppId;

        private String fingerPrint;

        // 营销推广来源ID
        private String marketingPromotionSourceId;
    }

    @Data
    public static class AddCampaignMergeDataByMemberResultContainer implements Serializable {

        private String campaignMergeDataId;

        private boolean bindOldData;

        private boolean createNewData;

    }

    @Data
    public static class WxUserData implements Serializable {

        String name;

        String userAvatar;
    }

    /**
     * 删除市场活动下的参与人员
     * @param campaignId
     * @param ea
     * @param fsUserId
     * @return
     */
    @Transactional
    public com.facishare.marketing.common.result.Result<Void> delete(String campaignId, String ea, Integer fsUserId){
        if (StringUtils.isBlank(campaignId)) {
            return com.facishare.marketing.common.result.Result.newSuccess();
        }
        // 查询本地表活动成员
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
        if (campaignMergeDataEntity == null) {
            return com.facishare.marketing.common.result.Result.newSuccess();
        }

        String campaignMembersObjId = campaignMergeDataEntity.getCampaignMembersObjId();
        // 检查有无提交信息表记录，这里有2种情况：
        // 1 提交表单或者导入的数据，有表单提交记录，即customize_form_data_user
        // 2 直接在crm新建活动成员对象，无表单提交记录
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(campaignId);
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntities)) {
            // 有表单提交记录
            boolean isSaveSuccess = false;
            for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntities) {
                Integer saveCrmStatus = customizeFormDataUserEntity.getSaveCrmStatus();
                if (Objects.equals(saveCrmStatus, SaveCrmStatusEnum.SUCCESS.getValue())) {
                    isSaveSuccess = true;
                }
                // 删除表单提交记录
                customizeFormDataUserDAO.deleteById(customizeFormDataUserEntity.getId());
            }
            if (isSaveSuccess) {
                // 存入成功，需要作废掉对象数据
                String bindCrmObjectId = campaignMergeDataEntity.getBindCrmObjectId();
                Integer bindCrmObjectType = campaignMergeDataEntity.getBindCrmObjectType();
                crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, CampaignMergeDataObjectTypeEnum.getApiNameByType(bindCrmObjectType), Lists.newArrayList(bindCrmObjectId));
            }
        }
        // 删除会议关联数据
        List<String> conferenceEnrollId = this.campaignIdToActivityEnrollId(Lists.newArrayList(campaignId));
        if (CollectionUtils.isNotEmpty(conferenceEnrollId)) {
            ActivityEnrollDataEntity activityEnrollDataEntity = activityEnrollDataDAO.getActivityEnrollDataById(conferenceEnrollId.get(0));
            CustomizeTicketReceiveEntity customizeTicketReceiveEntity = customizeTicketDAO.getTicketByAssociationAndDataUserId(activityEnrollDataEntity.getActivityId(), campaignId);
            WxTicketReceiveEntity wxTicketReceiveEntity = wxTicketReceiveDAO.getWxTicketReceiveByFormDataUserId(campaignId);
            activityDAO.decEnrollCount(activityEnrollDataEntity.getActivityId());
            activityEnrollDataDAO.deleteById(activityEnrollDataEntity.getId());
            if (customizeTicketReceiveEntity != null) {
                customizeTicketDAO.deleteCustomizeTicketReceiveById(customizeTicketReceiveEntity.getId());
            }
            if (wxTicketReceiveEntity != null) {
                wxTicketReceiveDAO.deleteById(wxTicketReceiveEntity.getId());
            }
        }
        // 删除会员报名数据
        memberAccessibleCampaignDAO.deleteMemberAccessibleCampaignDataByCampaignId(campaignId);
        // 删除本地表数据
        campaignMergeDataDAO.deleteCampaignMergeData(campaignId);
        // 作废活动成员
        if (StringUtils.isNotBlank(campaignMembersObjId)) {
            crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), Lists.newArrayList(campaignMembersObjId));
        }
        return com.facishare.marketing.common.result.Result.newSuccess();
    }
}
