package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.TemplateEntity;
import com.github.mybatis.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by zhengh on 2018/12/20.
 */
public interface TemplateDAO {
    @Insert("INSERT INTO sms_template(id, ea, user_id, creator, app_id, apply_id, name, content, type, remark, status, " +
            "seq_num, create_time, update_time) VALUES(" +
            "        #{obj.id},\n" +
            "        #{obj.ea},\n" +
            "        #{obj.userId},\n" +
            "        #{obj.creator},\n" +
            "        #{obj.appId},\n" +
            "        #{obj.applyId},\n" +
            "        #{obj.name},\n" +
            "        #{obj.content},\n" +
            "        #{obj.type},\n" +
            "        #{obj.remark},\n" +
            "        #{obj.status},\n" +
            "        #{obj.seqNum},\n" +
            "        now(),\n" +
            "        now()\n" +
            "        )")
    void addTemplate(@Param("obj") TemplateEntity templateEntity);

    @Select("SELECT COUNT(*) FROM sms_template WHERE app_id=#{appId} AND ea=#{ea} And status = #{status}")
    int getTemplateCountByEaAndAppId(@Param("appId") String appId, @Param("ea") String ea, @Param("status") Integer status);

    @Select("SELECT COUNT(*) FROM sms_template WHERE app_id=#{appId} AND ea=#{ea} And (status = #{statusApplyPass} or status = #{statusApplying})")
    int getTemplateCountByEaAndAppIdAndStatusPassOrApplying(@Param("appId") String appId, @Param("ea") String ea, @Param("statusApplyPass") Integer statusApplyPass, @Param("statusApplying") Integer statusApplying);



    @Select("SELECT * FROM sms_template WHERE status = #{status}")
    List<TemplateEntity> getEntityByStatus(@Param("status") Integer status);

    @Select("SELECT * FROM sms_template WHERE status = #{status}")
    List<TemplateEntity> getEntityByEaAndStatusPassOrAppling(@Param("status") Integer status);

    @Select("SELECT * FROM sms_template WHERE id = #{id} AND ea=#{ea}")
    TemplateEntity getTemplateByEaAndTemplateId(@Param("ea") String ea, @Param("id") String id);

    @Select("SELECT * FROM sms_template WHERE id = #{id}")
    TemplateEntity getTemplateByTemplateId(@Param("id") String id);


    @Select("SELECT * FROM sms_template WHERE id=#{id}")
    TemplateEntity getEntityById(@Param("id") String id);

    @Delete("DELETE FROM sms_template WHERE id = #{id}")
    void deleteEntityById(@Param("id") String id);

    @Delete("DELETE FROM sms_template WHERE ea = #{ea}")
    void deleteEntityByEa(@Param("ea") String ea);

    @Select("SELECT * FROM sms_template WHERE ea=#{ea} AND seq_num>#{seqNum} ORDER BY seq_num ASC LIMIT 1")
    TemplateEntity getForwardSwapEntity(@Param("ea")String ea, @Param("seqNum")Integer seqNum);

    @Select("SELECT * FROM sms_template WHERE ea=#{ea} AND seq_num<#{seqNum} ORDER BY seq_num DESC LIMIT 1")
    TemplateEntity getBackwardSwapEntity(@Param("ea")String ea, @Param("seqNum")Integer seqNum);

    @Update("UPDATE sms_template set seq_num = #{seqNum}, update_time = now() where id = #{id}")
    void updateSeqNumById(@Param("id") String id, @Param("seqNum") Integer seqNum);

    @Update("UPDATE sms_template set status = #{status}, update_time = now(), reply = #{reply} where apply_id = #{id}")
    void updateStatusByApplyId(@Param("id") Integer id, @Param("status") Integer status, @Param("reply") String reply);

    @Select("SELECT * FROM sms_template WHERE ea=#{ea} ORDER BY seq_num DESC LIMIT 1")
    TemplateEntity getMaxOrderTemplate(@Param("ea")String ea);

    @Select("<script>"
            + "SELECT * FROM sms_template\n"
            + " WHERE ea = #{ea}\n"
            + " <if test=\"name != null\">\n"
            + "  AND name LIKE  CONCAT('%',#{name},'%')\n"
            + " </if>\n"
            + " <if test=\"status != 3\">\n"
            + "  AND status = #{status}\n"
            + " </if>\n"
            + " ORDER BY seq_num desc, create_time desc "
            + "</script>")
    List<TemplateEntity> queryTemplateByEa(@Param("name") String name, @Param("ea")String ea, @Param("status") Integer status, @Param("page") Page page);


}
