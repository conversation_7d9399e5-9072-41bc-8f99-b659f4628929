package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.dto.ActivityEntityDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.github.mybatis.mapper.IPaginationPostgresqlMapper;
import com.github.mybatis.pagination.Page;
import java.util.Date;
import java.util.List;

import dev.langchain4j.agent.tool.P;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2018/8/10.
 */
public interface ActivityDAO extends IPaginationPostgresqlMapper<ActivityEntity> {
    /**
     * 更新活动状态
     */
    @Update("UPDATE activity SET status = #{status} WHERE id = #{id} AND ea = #{ea}")
    void updateActivityStatus(@Param("ea") String ea, @Param("id") String id, @Param("status") int status);

    /**
     * 根据活动id拉取活动实体
     */
    @Select("SELECT * FROM activity WHERE id=#{id}")
    ActivityEntity getById(@Param("id") String id);

    @Select("SELECT * FROM activity WHERE id=#{id} and ea=#{ea}")
    ActivityEntity getByIdAndEa(@Param("id") String id, @Param("ea") String ea);

    @Select("SELECT title FROM activity WHERE id=#{id}")
    String getTitleById(@Param("id") String id);

    @Select("<script>"
            + "SELECT * FROM activity WHERE id IN "
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            +  "</script>")
    List<ActivityEntity> getByIds(@Param("ids") List<String> ids);

    @Select("<script>"
        + "SELECT id,title FROM activity WHERE id IN "
        + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        +  "</script>")
    List<ActivityEntity> getNameByIds(@Param("ids") List<String> ids);

    /**
     * 创建活动
     */
    @Update("INSERT INTO activity(id, ea, title, status, start_time, end_time, enroll_end_time, location, \"scale\", cover_image_small_url,\n"
        + "        cover_image_url, background_image_url, activity_template_id, activity_template_values, activity_enroll_describe, welcome_msg,\n"
        + "        submit_success_msg,public_account_redirect_enabled, public_account_name, public_account_app_id, public_account_url, site_redirect_enabled, site_redirect_url,\n"
        + "        product_redirect_enabled, product_redirect_url, consult_redirect_enabled, consult_customer_service, activity_detail,form_title,create_by, update_by, create_time, update_time,\n"
        + "        is_enroll_sync_to_leads,crm_lead_pool_id, is_need_sign,network_disk_enabled,network_disk_url,jump_link_enabled,jump_link_url,button_text)\n"
        + "        VALUES (#{id}, #{ea}, #{title}, #{status}, #{startTime}, #{endTime}, #{enrollEndTime}, #{location}, #{scale}, #{coverImageSmallUrl},\n"
        + "        #{coverImageUrl}, #{backgroundImageUrl}, #{activityTemplateId}, #{activityTemplateValues}, #{activityEnrollDescribe}, #{welcomeMsg},\n"
        + "        #{submitSuccessMsg},#{publicAccountRedirectEnabled}, #{publicAccountName}, #{publicAccountAppId}, #{publicAccountUrl}, #{siteRedirectEnabled}, #{siteRedirectUrl},\n"
        + "        #{productRedirectEnabled}, #{productRedirectUrl}, #{consultRedirectEnabled}, #{consultCustomerService},#{activityDetail},#{formTitle}, #{createBy}, #{updateBy}, #{createTime}, #{updateTime},\n"
        + "        #{isEnrollSyncToLeads}, #{crmLeadPoolId}, #{isNeedSign},#{networkDiskEnabled},#{networkDiskUrl},#{jumpLinkEnabled},#{jumpLinkUrl},#{buttonText})")
    boolean create(ActivityEntity entity);

    /**
     * 更新活动实体
     */
    @Update("UPDATE activity SET title=#{title}, start_time=#{startTime}, end_time=#{endTime}, enroll_end_time=#{enrollEndTime}, location=#{location},\n"
        + "         \"scale\"=#{scale}, \"cover_image_small_url\"=#{coverImageSmallUrl},\"cover_image_url\"=#{coverImageUrl}, background_image_url=#{backgroundImageUrl},\n"
        + "          activity_template_id=#{activityTemplateId}, activity_template_values=#{activityTemplateValues}, activity_enroll_describe=#{activityEnrollDescribe},\n"
        + "          welcome_msg=#{welcomeMsg},submit_success_msg=#{submitSuccessMsg},public_account_redirect_enabled=#{publicAccountRedirectEnabled},\n"
        + "          public_account_name=#{publicAccountName}, public_account_app_id=#{publicAccountAppId}, public_account_url=#{publicAccountUrl}, site_redirect_enabled=#{siteRedirectEnabled}, site_redirect_url=#{siteRedirectUrl},\n"
        + "          product_redirect_enabled=#{productRedirectEnabled}, product_redirect_url=#{productRedirectUrl}, consult_redirect_enabled=#{consultRedirectEnabled},activity_detail = #{activityDetail},form_title = #{formTitle},\n"
        + "          update_by=#{updateBy}, update_time=#{updateTime},is_enroll_sync_to_leads=#{isEnrollSyncToLeads},crm_lead_pool_id=#{crmLeadPoolId}, is_need_sign = #{isNeedSign},"
        + "          network_disk_enabled=#{networkDiskEnabled},network_disk_url=#{networkDiskUrl},jump_link_enabled=#{jumpLinkEnabled},jump_link_url=#{jumpLinkUrl},button_text=#{buttonText}  WHERE id = #{id}")
    boolean update(ActivityEntity entity);

    /**
     * @param sourceType 1-小程序 ， 2-web
     */
    @Select("<script>" + "SELECT\n" + "    id, ea,title,status,start_time,end_time,enroll_end_time,location,\"scale\",enroll_count,cover_image_small_url,\n"
        + "    cover_image_url,background_image_url,activity_template_id,welcome_msg,submit_success_msg,public_account_redirect_enabled,\n"
        + "    public_account_name,public_account_app_id,public_account_url,site_redirect_enabled,site_redirect_url,product_redirect_enabled,product_redirect_url,\n"
        + "    consult_redirect_enabled,consult_customer_service,create_by,update_by,form_title,create_time,update_time, is_need_sign " + "    FROM activity ac\n" + "    WHERE ac.ea = #{ea} AND ac.status != 3\n"
        + "    <if test=\"state != null and state != 0\">\n" + "      <if test=\"state == 2\">\n" + "        <![CDATA[AND ac.start_time >= now()]]>\n" + "      </if>\n"
        + "      <if test=\"state == 3\">\n" + "        <![CDATA[ AND ac.start_time < now() AND ac.end_time > now() ]]>\n" + "      </if>\n" + "      <if test=\"state == 4\">\n"
        + "        <![CDATA[ AND ac.end_time <= now() ]]>\n" + "      </if>\n" + "    </if>\n" + "    <if test=\"sourceType == 1\">\n" + "      AND ac.status = 1\n" + "    </if>\n"
        + "    <if test=\"title != null and title != ''\">\n" + "      AND ac.title ~ #{title}\n" + "    </if>\n" + "    ORDER BY ac.update_time DESC" + "</script>")
    List<ActivityEntity> listActivities(@Param("ea") String ea, @Param("title") String title, @Param("state") int state, @Param("sourceType") int sourceType, @Param("page") Page page);


    @Select("<script>" + "SELECT\n" + "    id, ea,title,status,start_time,end_time,enroll_end_time,location,\"scale\",enroll_count,cover_image_small_url,\n"
        + "    cover_image_url,background_image_url,activity_template_id,welcome_msg,submit_success_msg,public_account_redirect_enabled,\n"
        + "    public_account_name,public_account_app_id,public_account_url,site_redirect_enabled,site_redirect_url,product_redirect_enabled,product_redirect_url,\n"
        + "    consult_redirect_enabled,consult_customer_service,create_by,update_by,form_title,create_time,update_time, is_need_sign " + "    FROM activity ac\n" + "    WHERE ac.ea = #{ea} AND ac.status != 3\n"
        + "    <if test=\"state != null and state != 0\">\n" + "      <if test=\"state == 2\">\n" + "        <![CDATA[AND ac.start_time >= now()]]>\n" + "      </if>\n"
        + "      <if test=\"state == 3\">\n" + "        <![CDATA[ AND ac.start_time < now() AND ac.end_time > now() ]]>\n" + "      </if>\n" + "      <if test=\"state == 4\">\n"
        + "        <![CDATA[ AND ac.end_time <= now() ]]>\n" + "      </if>\n" + "    </if>\n" + "    AND ac.status = #{status}\n" + "   "
        + "    <if test=\"title != null and title != ''\">\n" + "      AND ac.title ~ #{title}\n" + "    </if>\n" + "    ORDER BY ac.update_time DESC" + "</script>")
    List<ActivityEntity> listActivitiesByStatueAndState(@Param("ea") String ea, @Param("title") String title, @Param("state") int state, @Param("status") int status, @Param("page") Page page);


    @Delete("UPDATE activity SET status = 3 WHERE id = #{id} AND ea = #{ea}")
    boolean deleteActivity(@Param("ea") String ea, @Param("id") String id);

    @Select("<script>" + "SELECT " + "    id, ea,title,status,start_time,end_time,enroll_end_time,location,\"scale\",enroll_count,cover_image_small_url,\n"
        + "    cover_image_url,background_image_url,activity_template_id,welcome_msg,submit_success_msg,public_account_redirect_enabled,\n"
        + "    public_account_name,public_account_app_id,public_account_url,site_redirect_enabled,site_redirect_url,product_redirect_enabled,product_redirect_url,\n"
        + "    consult_redirect_enabled,consult_customer_service,create_by,update_by,form_title,create_time,update_time, is_need_sign " + "    FROM activity ac WHERE ac.ea = #{ea} AND ac.status != 3\n"
        + "    <if test=\"states != null \">\n" + "      <trim prefix=\"AND (\" prefixOverrides=\"AND|OR\" suffix=\")\">\n" + "        <foreach collection=\"states\" index=\"idx\" item=\"state\">\n"
        + "          <if test=\"states[idx] == 2\">\n" + "            <![CDATA[ OR ac.start_time >= now()]]>\n" + "          </if>\n" + "          <if test=\"states[idx] == 3\">\n"
        + "            <![CDATA[ OR (ac.start_time < now() AND ac.end_time > now()) ]]>\n" + "          </if>\n" + "          <if test=\"states[idx] == 4\">\n"
        + "            <![CDATA[ OR ac.end_time <= now() ]]>\n" + "          </if>\n" + "        </foreach>\n" + "      </trim>\n" + "    </if>\n" + "    <if test=\"statuses != null \">\n"
        + "      AND (\n" + "      <trim prefix=\"\" prefixOverrides=\"AND|OR\">\n" + "        <foreach collection=\"statuses\" index=\"idx\" item=\"status\">\n"
        + "          OR ac.status = #{statuses[${idx}]}\n" + "        </foreach>\n" + "      </trim>\n" + "      )\n" + "    </if>\n" + "    <if test=\"title != null and title != ''\">\n"
        + "      AND ac.title ~ #{title}\n" + "    </if>\n" + "    ORDER BY ac.update_time DESC" + "</script>")
    List<ActivityEntity> listActivitiesByMultiCondition(@Param("ea") String fsEa, @Param("title") String title, @Param("states") List<Integer> states, @Param("statuses") List<Integer> statuses,
        @Param("page") Page page);
    
    
    @Select("SELECT \n" +
        "\tac.ID,\n" +
        "\tea,\n" +
        "\ttitle,\n" +
        "\tstatus,\n" +
        "\tstart_time,\n" +
        "\tend_time,\n" +
        "\tenroll_end_time,\n" +
        "\tLOCATION,\n" +
        "\tSCALE,\n" +
        "\tenroll_count,\n" +
        "\tcover_image_small_url,\n" +
        "\tcover_image_url,\n" +
        "\tbackground_image_url,\n" +
        "\tactivity_template_id,\n" +
        "\twelcome_msg,\n" +
        "\tsubmit_success_msg,\n" +
        "\tpublic_account_redirect_enabled,\n" +
        "\tpublic_account_name,\n" +
        "\tpublic_account_app_id,\n" +
        "\tpublic_account_url,\n" +
        "\tsite_redirect_enabled,\n" +
        "\tsite_redirect_url,\n" +
        "\tproduct_redirect_enabled,\n" +
        "\tproduct_redirect_url,\n" +
        "\tconsult_redirect_enabled,\n" +
        "\tconsult_customer_service,\n" +
        "\tcreate_by,\n" +
        "\tupdate_by,\n" +
        "\tform_title,\n" +
        "\tCASE\n" +
        "\tWHEN B.object_id IS NULL THEN\n" +
        "\tFALSE ELSE TRUE \n" +
        "\tEND AS isChoose,\n" +
        "\tac.create_time,\n" +
        "\tac.update_time \n" +
        "FROM\n" +
        "\tactivity ac LEFT JOIN distribution_material B ON ac.ID = B.object_id \n" +
        "\tAND B.plan_id = #{planId} \n" +
        "WHERE\n" +
        "\tac.ea = #{ea} \n" +
        "\tAND ac.status != 3 \n" +
        "\tAND ac.status = 1 \n" +
        "\tAND B.object_id IS NULL \n" +
        "ORDER BY\n" +
        "\tac.update_time DESC")
    List<ActivityEntityDTO> windowListActivities(@Param("ea") String ea, @Param("planId") String planId, @Param("page") Page page);

    @Select("<script> select  COALESCE(count(1),0)   from activity ac where ac.create_time  between #{startDate} and  #{endDate}\n"
        + "UNION select COALESCE(count(1),0)  from  article ar where ar.create_time    between #{startDate} and  #{endDate}\n"
        + "UNION select COALESCE(count(1),0)   from product  pr where pr.create_time   between #{startDate} and  #{endDate}    "
        + "</script>")
    List<Integer>  getMaterialsWeeklyStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Update("<script>"
        + "UPDATE activity SET enroll_count=COALESCE(enroll_count, 0)+#{inc} WHERE id=#{id}"
        + "</script>")
    int incrementEnrollCount(@Param("id") String id, @Param("inc") Integer inc);

    @Select("SELECT marketing_event_id FROM activity WHERE id=#{id}")
    String queryMarketingEventIdById(@Param("id")String id);

    @Update("<script>"
            + "UPDATE activity SET enroll_count=enroll_count - 1 WHERE id=#{id} AND enroll_count > 0"
            + "</script>")
    int decEnrollCount(@Param("id") String id);

    @Select("select * from activity where ea = #{ea} and marketing_event_id = #{marketingEventId}")
    ActivityEntity getActivityEntityByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("select * from activity where ea = #{ea}")
    List<ActivityEntity> getActivityListByEa(@Param("ea") String ea);

    @Select("<script>"
            + "SELECT DISTINCT ea FROM activity WHERE ea in\n"
            + "<foreach collection = 'eas' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<if test='startDate != null and endDate != null'>AND create_time between #{startDate} and #{endDate}</if>\n"
            + "</script>")
    List<String> getByEasAndDate(@Param("eas")List<String> eas, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +"select activity_detail_site_id from activity where ea = #{ea} and activity_detail_site_id in "
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<String> checkSite(@Param("ea") String ea,@Param("ids") List<String> ids);
}
