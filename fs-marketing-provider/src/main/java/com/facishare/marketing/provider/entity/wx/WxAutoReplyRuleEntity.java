package com.facishare.marketing.provider.entity.wx;

import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.wx.WxAutoReplyRuleStatusEnum;
import com.facishare.marketing.common.typehandlers.value.News;
import com.facishare.marketing.common.typehandlers.value.StringList;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/12/24 9:53
 * @Version 1.0
 */
@Data
public class WxAutoReplyRuleEntity implements Serializable {
    private String id;
    private String ea;
    private String wxAppId;     // 微信appId
    private String ruleName;    // 规则名称
    private Date validFrom;     // 有效期开始时间
    private Date validTo;       // 有效期截止时间
    private String keyWord;     // 关键字
    /** {@link OperatorConstants} */
    private String operator;    // 匹配规则
    /** {@link com.facishare.marketing.provider.mq.consumer.WxUserActionConsumer.WxUserActionEvent} */
    private Integer actionType; // 监听事件：1：关注公众号   5：发送公众号内容
    private String responseMsg; // 回复消息json数据
    private TagNameList tagNameList; // 标签
    /** {@link com.facishare.marketing.common.enums.wx.WxAutoReplyRuleStatusEnum} */
    private Integer status;     // 状态：0有效   -1过期
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间

    /**
     *
     * @return 返回更新后的状态
     */
    public Integer refreshStatus() {
        long now = System.currentTimeMillis();
        if (validTo == null || validFrom == null) {
            status = (status == null ? WxAutoReplyRuleStatusEnum.VALID.getStatus() : status);
            return status;
        }
        if (now > validFrom.getTime() && now < validTo.getTime()) {
            status = WxAutoReplyRuleStatusEnum.VALID.getStatus();
            return status;
        }
        status = WxAutoReplyRuleStatusEnum.INVALID.getStatus();
        return status;
    }

    public boolean isValid() {
        return status == null || WxAutoReplyRuleStatusEnum.VALID.getStatus().equals(status);
    }

    public boolean isInValid() {
        return !isValid();
    }

}
