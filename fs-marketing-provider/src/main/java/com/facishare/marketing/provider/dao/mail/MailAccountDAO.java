package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.mail.MailAccountEntity;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by zhengh on 2020/6/2.
 */
public interface MailAccountDAO {

    @Insert("INSERT INTO mail_account(id, ea, api_user, api_key, type, domain, creator, create_time, update_time) VALUES(#{entity.id},\n"
        + " #{entity.ea}, #{entity.apiUser}, #{entity.apiKey}, #{entity.type}, #{entity.domain}, #{entity.creator}, now(), now()) ON CONFLICT DO NOTHING;")
    void insert(@Param("entity") MailAccountEntity entity);

    @FilterLog
    @Select(" SELECT * FROM mail_account WHERE ea = #{ea} and type = #{type}")
    MailAccountEntity getAccountByEaAndType(@Param("ea")String ea, @Param("type") Integer type);

    @FilterLog
    @Select(" SELECT * FROM mail_account WHERE ea = #{ea}")
    List<MailAccountEntity> getByEa(@Param("ea")String ea);

    @FilterLog
    @Select(" SELECT * FROM mail_account WHERE type = #{type}")
    List<MailAccountEntity> getByType(@Param("type") Integer type);

    @Select("SELECT DISTINCT ea FROM mail_account")
    List<String> getAllEas();
 }
