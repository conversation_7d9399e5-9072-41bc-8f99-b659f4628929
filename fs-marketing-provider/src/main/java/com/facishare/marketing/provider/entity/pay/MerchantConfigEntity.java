package com.facishare.marketing.provider.entity.pay;

import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;
import java.util.Base64;

@Data
public class MerchantConfigEntity implements Serializable {
    private String ea;
    private String merchantId;
    private String name;
    private String appSecret;
    private String appV3Secret;
    private String p12Cert;
    private String serialNo; //证书序列号
    private String apiclientCert; //解密文件
    private String apiclientKey; //秘钥文件
    private String appId; //关联APPID 账号

    public byte[] getP12CertBytes(){
        if(Strings.isNullOrEmpty(p12Cert)){
            throw new IllegalStateException();
        }
        return Base64.getDecoder().decode(p12Cert);
    }
}
