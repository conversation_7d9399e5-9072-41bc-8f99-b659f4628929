package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.mail.MailSendErrorAddressEntity;
import com.github.mybatis.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2021/04/29
 **/
public interface MailSendErrorAddressDAO {

    @Insert("INSERT INTO mail_send_error_address (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"task_id\",\n"
        + "        \"address\",\n"
        + "        \"event_type\",\n"
        + "        \"sub_stat\",\n"
        + "        \"create_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.taskId},\n"
        + "        #{obj.address},\n"
        + "        #{obj.eventType},\n"
        + "        #{obj.subStat},\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    void insertMailSendErrorAddress(@Param("obj") MailSendErrorAddressEntity mailSendErrorAddressEntity);

    @Select("<script>"
        + " SELECT * FROM mail_send_error_address WHERE ea = #{ea}"
        + "<if test=\"address != null\">"
        + " AND address LIKE CONCAT('%', #{address}, '%')"
        + "</if>"
        + "<if test=\"eventType != null\">"
        + " AND event_type = #{eventType}"
        + "</if>"
        + " ORDER BY create_time desc"
        + "</script>")
    List<MailSendErrorAddressEntity> queryErrorAddressByEa(@Param("ea") String ea, @Param("address") String address, @Param("eventType") Integer eventType, @Param("page") Page page);


    @Delete("<script>"
        + " DELETE FROM mail_send_error_address WHERE id IN "
        + "<foreach collection = 'ids' item = 'id' index='num' open = '(' separator = ',' close = ')'>"
        + "#{id}"
        + "</foreach>"
        + "</script>")
    void deleteErrorAddressDataByIds(@Param("ids") List<String> ids);

    @Select("SELECT address, event_type, sub_stat FROM mail_send_error_address WHERE ea = #{ea}")
    @FilterLog
    List<MailSendErrorAddressEntity> queryAllErrorAddressByEa(@Param("ea") String ea);

    @Select("<script>" +
            "SELECT address, event_type, sub_stat FROM mail_send_error_address WHERE ea = #{ea} and address = ANY(ARRAY" +
            " <foreach collection='addressList' item='item' open='[' close=']' separator=','> " +
            "#{item}" +
            " </foreach> " +
            " ) " +
         "</script>")
    @FilterLog
    List<MailSendErrorAddressEntity> queryAllErrorAddressByEaAndAddressList(@Param("ea") String ea, @Param("addressList") List<String> addressList);

}
