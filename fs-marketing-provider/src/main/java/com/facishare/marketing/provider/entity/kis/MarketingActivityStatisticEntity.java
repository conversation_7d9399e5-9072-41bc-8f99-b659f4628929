package com.facishare.marketing.provider.entity.kis;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

/**
 * Created by ranluch on 2019/2/25.
 */
@Data
@Entity
public class MarketingActivityStatisticEntity implements Serializable {
    private String id;
    private String ea;   // 公司帐号
    private Integer fsUserId;
    private String marketingActivityId; //营销活动id
    private Integer spreadCount;   // 员工推广次数
    private Integer spreadUserCount; //员工推广人数
    private Integer forwardCount; // 转发次数，不含员工
    private Integer lookUpCount; // 访问次数，不含员工
    private Integer forwardUserCount; // 转发人数，不含员工
    private Integer lookUpUserCount; // 访问人数，不含员工
    private Integer leadAccumulationCount; // 线索累积量
    private Integer customerAccumulationCount; // 客户累积量

    private Integer countDepartment = 0;  //部门数
    private Integer countUser = 0;       // 同事数

    private Date createTime;
    private Date updateTime;
    private String upStreamEa;
}
