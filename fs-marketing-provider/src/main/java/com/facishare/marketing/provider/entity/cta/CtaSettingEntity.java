package com.facishare.marketing.provider.entity.cta;

import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CtaSettingEntity implements Serializable {
    private String id;
    private String ea;
    private String ctaId;
    private FlexibleJson configValues;    //保存企业配置信息
    //创建人
    private Integer createBy;
    // 创建时间瓬
    private Date createTime;
    //最后修改人
    private Integer updateBy;
    // 更新时间
    private Date updateTime;
}
