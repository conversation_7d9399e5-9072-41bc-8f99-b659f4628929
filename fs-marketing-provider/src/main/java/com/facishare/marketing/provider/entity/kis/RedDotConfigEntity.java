package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2019/3/21.
 */
@Data
@Entity
public class RedDotConfigEntity implements Serializable{
    private String id;
    private String ea;
    private Integer userId;
    private Integer status;   //红点状态 0：展示红点  1：消除红点
    private Integer position; //红点位置
    private Date createTime;  //创建时间
    private Date updateTime;  //更新时间
}
