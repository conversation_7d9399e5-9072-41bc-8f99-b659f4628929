package com.facishare.marketing.provider.dao.sms;

import com.facishare.marketing.provider.entity.sms.SignatureEntity;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by zhengh on 2018/12/20.
 */
public interface SignatureDAO {
    @Insert("INSERT INTO sms_signature(id, ea, user_id, creator, app_id, apply_id, content, certificate, remark, type, status, create_time, update_time) VALUES(" +
            "        #{obj.id},\n" +
            "        #{obj.ea},\n" +
            "        #{obj.userId},\n" +
            "        #{obj.creator},\n" +
            "        #{obj.appId},\n" +
            "        #{obj.applyId},\n" +
            "        #{obj.content},\n" +
            "        #{obj.certificate},\n" +
            "        #{obj.remark},\n" +
            "        #{obj.type},\n" +
            "        #{obj.status},\n" +
            "        now(),\n" +
            "        now()\n" +
            "        )")
    void addSignature(@Param("obj") SignatureEntity signatureEntity);

    @Select("SELECT * FROM sms_signature WHERE ea=#{ea}")
    List<SignatureEntity> getEntityByEa(@Param("ea") String ea);

    @Select("SELECT * FROM sms_signature WHERE ea=#{ea} and status = #{status}")
    List<SignatureEntity> getEntityByEaAndStatus(@Param("ea") String ea, @Param("status") Integer status);

    @Select("SELECT * FROM mw_sms_signature WHERE ea=#{ea} and status = #{status}")
    List<SignatureEntity> getMWEntityByEaAndStatus(@Param("ea") String ea, @Param("status") Integer status);

    @Select("SELECT * FROM sms_signature WHERE ea=#{ea} and (status = #{statusApplyPass} or status = #{statusApplying})")
    List<SignatureEntity> getEntityByEaAndStatusPassOrApplying(@Param("ea") String ea, @Param("statusApplyPass") Integer statusApplyPass, @Param("statusApplying") Integer statusApplying);

    @Select("SELECT * FROM sms_signature WHERE ea=#{ea} and status = #{statusApplyFail} order by  update_time desc")
    List<SignatureEntity> getEntityByEaAndStatusFail(@Param("ea") String ea, @Param("statusApplyFail") Integer statusApplyFail);

    @Select("select count(1) from sms_signature WHERE app_id=#{appId} and status != #{status}")
    Integer getCountOfExceptStatusSignature(@Param("appId")String appId, @Param("status")Integer status);

    @Select("SELECT * FROM sms_signature WHERE id=#{id} AND ea=#{ea}")
    SignatureEntity queryEntityByEaAndId(@Param("ea")String ea, @Param("id") String id);

    @Select("SELECT * FROM sms_signature WHERE id = #{id} AND ea=#{ea}")
    SignatureEntity queryEntityByEaAndApplyId(@Param("ea")String ea, @Param("id") String id);

    @Delete("DELETE FROM sms_signature WHERE id=#{id} AND ea=#{ea}")
    void deleteEntityByEaAndId(@Param("ea")String ea, @Param("id") String id);

    @Select("SELECT * FROM sms_signature WHERE status = #{status}")
    List<SignatureEntity> getEntityByStatus(@Param("status") Integer status);

    @Select("SELECT * FROM sms_signature WHERE apply_id=#{applyId}")
    SignatureEntity getEntityByApplyId(@Param("applyId") Integer applyId);

    @Update("<script>" +"UPDATE sms_signature set status = #{status},"
        + "<if test=\"status != 0\">"
        + "reply=#{reply}, "
        + "</if>"
        + "update_time = now() where apply_id = #{applyId} "
        + "</script>")
    void updateSignatureStatus(@Param("applyId")Integer applyId, @Param("status")Integer status, @Param("reply")String reply);

    @Update("<script>"
        + "UPDATE sms_signature \n"
        + "SET modify_cnt = #{modifyCnt}, \n"
        + "<if test=\"pic != null\">\n"
        + "certificate = #{pic}, \n"
        + "</if>\n"
        + "<if test=\"remark != null \">\n"
        + "remark = #{remark}, \n"
        + "</if>\n"
        + "<if test=\"text != null\">\n"
        + "content = #{text}, \n"
        + "</if>\n"
        + "status = #{status},\n"
        + "update_time = now(),\n"
        + "modify_time = now()\n"
        + "WHERE id = #{id}"
        + "</script>")
    void updateModifyInfo(@Param("id") String id, @Param("pic") String pic, @Param("text") String text, @Param("remark") String remark, @Param("status") int status, @Param("modifyCnt") int modifyCnt);
}
