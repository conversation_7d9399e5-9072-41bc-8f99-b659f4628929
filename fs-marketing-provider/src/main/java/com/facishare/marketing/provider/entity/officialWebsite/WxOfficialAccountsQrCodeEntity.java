package com.facishare.marketing.provider.entity.officialWebsite;

import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class WxOfficialAccountsQrCodeEntity implements Serializable {
    private String id;
    private String ea;
    private String qrCodeName;
    private String wxAppId;          //微信appid 如：wx2e8a14a356c46c27
    private String platformAppId;    //平台绑定的微信appid 如：FSAID_10b07b0c
    private Integer creator;
    private String fileUrl;
    private String marketingEventId;
    private String marketingEventName;
    private String responseMsg;
    private Integer scanCount;
    private String showUrl;
    private Integer subscribeCount;
    private TagNameList tagNames;
    private String welcomeMsg;
    private Integer isBindWebsite;//是否关联官网
    private Date updateBindStatusTime;//关联官网时间
    private Integer doBindFsUserId;//将该二维码关联官网的员工fsUserId
    private String doBindFsUserName;//将该二维码关联官网的员工姓名
    private Integer isTemp;//是否是临时二维码
    private Long sceneId; //二维码场景id，平台保存的二维码id
    private Integer isBindWebsiteLogin;//是否关联官网登录
}
