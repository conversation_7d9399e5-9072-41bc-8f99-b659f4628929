package com.facishare.marketing.provider.entity.bd;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class BdSiteEntity implements Serializable {
    private String id;

    private String ea;

    private String name;

    private String summary;

    private String iconApath;

    private String hexagonSiteId;

    private Integer status;

    private Integer createBy;

    private Integer updateBy;

    private Date createTime;

    private Date updateTime;
}