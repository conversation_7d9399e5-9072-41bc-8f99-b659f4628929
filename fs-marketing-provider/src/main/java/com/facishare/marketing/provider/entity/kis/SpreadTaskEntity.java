package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2019/2/22.
 */
@Data
@Entity
public class SpreadTaskEntity implements Serializable{
    private String id;
    private String marketingActivityId;   //营销活动id
    private String ea;                    //公司帐号
    private String upStreamEa;            //上游企业账号
    private Integer userId;               //员工id
    private Integer status;               //推广任务状态 SpreadTaskStatusEnum
    private Date createTime;              //推广任务创建时间
    private Date finishTime;              //推广任务完成时间
    private String outUserId;              //推广任务完成时间
    private Boolean lookUpStatus;          //员工是否已读
}
