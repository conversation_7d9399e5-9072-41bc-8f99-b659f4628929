package com.facishare.marketing.provider.entity.cta;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CtaQrCodeRelationEntity implements Serializable {
    //主键ID
    private String id;
    private String ea;
    private String qrCodeType;
    private String sceneId;
    private String appId;
    //CtaId
    private String ctaId;
    private Integer objectType;
    //ObjectId
    private String objectId;
    //UserMarketingId
    private String userMarketingId;
    //BrowserUserId
    private String browserUserId;
    //WxAppId
    private String wxAppId;
    //WxOpenId
    private String wxOpenId;
    //ConfigId
    private String configId;
    //State
    private String state;
    //Status
    private Integer status;
    //QrCode
    private String qrCode;
    // 创建时间瓬
    private Date createTime;
    // 更新时间
    private Date updateTime;
}