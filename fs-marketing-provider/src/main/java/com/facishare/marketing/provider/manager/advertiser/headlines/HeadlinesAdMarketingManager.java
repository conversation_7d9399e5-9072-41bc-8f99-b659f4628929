/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.advertiser.headlines;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.result.baidu.QueryAccountInfoResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.api.vo.baidu.QueryAccountInfoVO;
import com.facishare.marketing.common.contstant.CrmConstants;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.contstant.CustomizeFunctionConstants;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.SpreadChannelEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.*;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.baidu.DataRefreshStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2FieldTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult;
import com.facishare.marketing.provider.advertiser.headlines.ad.*;
import com.facishare.marketing.provider.advertiser.headlines.campaign.*;
import com.facishare.marketing.provider.advertiser.headlines.keyword.HeadlinesKeywordResultData;
import com.facishare.marketing.provider.advertiser.headlines.project.GetHeadlinesProjectResult;
import com.facishare.marketing.provider.advertiser.headlines.project.HeadlinesProjectResult;
import com.facishare.marketing.provider.advertiser.headlines.promotion.GetHeadlinesPromotionResult;
import com.facishare.marketing.provider.advertiser.headlines.report.CustomReportResult;
import com.facishare.marketing.provider.advertiser.headlines.report.GetLocalPromotionReportResult;
import com.facishare.marketing.provider.baidu.GetAccountInfoResultData;
import com.facishare.marketing.provider.baidu.GetLocalAccountInfoResultData;
import com.facishare.marketing.provider.baidu.keyword.AdKeywordManager;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsDAO;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDataDAO;
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.*;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.AdKeywordEntity;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.innerArg.CreateAdvertisingDetailObjArg;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.innerArg.crm.ExecuteCustomizeFunctionArg;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiter;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiterBuilder;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.CampaignInitDateUtil;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.DuplicateSearchResult;
import com.fxiaoke.crmrestapi.result.DuplicatesearchQueryResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import jetbrick.util.JSONUtils;
import jetbrick.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;

/**
 * Created by wangzhenyi on 2021/11/18 3:56 下午
 */
@Service("headlinesAdMarketingManager")
@Slf4j
public class HeadlinesAdMarketingManager implements AdMarketingManager {

    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private BaiduDataStatusDAO baiduDataStatusDAO;
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private AdTokenManager adTokenManager;
    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;
    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;
    @Autowired
    private CampaignApiManager campaignApiManager;
    @Autowired
    private HeadlinesCampaignDataDAO headlinesCampaignDataDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AdKeywordManager adKeywordManager;
    @Autowired
    private AdKeywordDAO adKeywordDAO;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private AdLeadsDAO adLeadsDAO;
    @Autowired
    private CustomizeFunctionManager customizeFunctionManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private UtmDataManger utmDataManger;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private AdLeadsMappingDataDAO adLeadsMappingDataDAO;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private RedisManager redisManager;
    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    // 自己注入自己，注入的是一个新对象，这样在内部通过该对象来调用@Transactional的方法事务才会生效
    // 因为Spring事务是通过代理对象来实现的，内部方法自己调用@Transactional方法Spring无法生成代理对象
    @Autowired
    private HeadlinesAdMarketingManager adMarketingManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @ReloadableProperty("headlines_campaign_list")
    private String campaignList;
    @ReloadableProperty("headlines_campaign_list_en")
    private String campaignListEN;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @Autowired
    private ClueManagementManager clueManagementManager;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    private String START_TIME = "2016-10-26";
    private String WEILAI = "visionnav";
    private String WEILAI_START_TIME = "2021-09-15";

    private static final String LOCAL_SYNC_TIME_KEY = "mk:lst:%s:%s";

    public synchronized YxtRRateLimiter getYxtRRateLimiter(String accountId) {
        String name = accountId + ":mk:rateLimiter:v3";
        return YxtRRateLimiterBuilder.getYxtRRateLimiter(name, 70, 1,true,30L, TimeUnit.DAYS);
    }

    @Override
    public Result<List<QueryAccountInfoResult>> queryAccountInfo(QueryAccountInfoVO vo) {
        List<QueryAccountInfoResult> resultList = Lists.newArrayList();
        // adAccountId若为空查的是该企业下所有对应source的广告账户
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEaAndSource(vo.getEa(), vo.getAdAccountId(), AdSourceEnum.getSourceByValue(vo.getSource()), true);
        if(dataPermissionManager.getNewDataPermissionSetting(vo.getEa())){
            List<String> websiteIds = dataPermissionManager.findAccessibleAdvertiseIds(vo.getEa(), vo.getFsUserId());
            if(CollectionUtils.isEmpty(websiteIds)){
                QueryAccountInfoResult result = new QueryAccountInfoResult();
                result.setEa(vo.getEa());
                result.setStatus(AccountStatusEnum.NOT_BIND_ACCOUNT.getStatus());
                resultList.add(result);
                return Result.newSuccess(resultList);
            }
            if(!websiteIds.contains(defaultAllChannel)){
                //过滤可见范围
                adAccountEntityList = adAccountEntityList.stream().filter(adAccountEntity -> websiteIds.contains(adAccountEntity.getId())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            QueryAccountInfoResult result = new QueryAccountInfoResult();
            result.setEa(vo.getEa());
            result.setStatus(AccountStatusEnum.NOT_BIND_ACCOUNT.getStatus());
            resultList.add(result);
            return Result.newSuccess(resultList);
        }


        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            QueryAccountInfoResult result = new QueryAccountInfoResult();
            BaiduDataStatusEntity statusEntity = baiduDataStatusDAO.queryRefreshStatus(vo.getEa(), adAccountEntity.getId(), adAccountEntity.getSource());
            if (statusEntity == null || statusEntity.getRefreshStatus() == null) {
                result.setRefreshStatus(DataRefreshStatusEnum.NONE.getStatus());
            } else {
                result.setRefreshStatus(statusEntity.getRefreshStatus());
                result.setRefreshSuccessTime(statusEntity.getRefreshSuccessTime() == null ? null : statusEntity.getRefreshSuccessTime().getTime());
                result.setRefreshTime(statusEntity.getRefreshTime() == null ? null : statusEntity.getRefreshTime().getTime());
                if (statusEntity.getRefreshStatus() == 1 && adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
                    adAccountEntity.setStatus(AccountStatusEnum.NORMAL.getStatus());
                }
            }
            result.setId(adAccountEntity.getId());
            result.setEa(vo.getEa());
            result.setStatus(adAccountEntity.getStatus());
            QueryAccountInfoResult.AccountInfo accountInfo = new QueryAccountInfoResult.AccountInfo();
            accountInfo.setAccountId(adAccountEntity.getAccountId());
            accountInfo.setUsername(adAccountEntity.getUsername());
            double balance;
            if (adAccountEntity.getBalance() == 0) {
                balance = 0;
            } else {
                BigDecimal balanceBigDecimal = BigDecimal.valueOf(adAccountEntity.getBalance());
                balance = balanceBigDecimal.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue(); // 四舍五入保留2位小数
            }
            accountInfo.setBalance(balance);
            accountInfo.setBudget(adAccountEntity.getBudget());
            accountInfo.setAuthUserName(adAccountEntity.getAuthUserName() == null ? adAccountEntity.getUsername() : adAccountEntity.getAuthUserName());
            if (adAccountEntity.getCost() != null) {
                accountInfo.setCost(Math.round(adAccountEntity.getCost() * 100d) / 100d);
            }
            accountInfo.setBudgetType(adAccountEntity.getBudgetType());
            accountInfo.setUserStat(adAccountEntity.getUserStat());
            accountInfo.setType(adAccountEntity.getType() == null ? HeadlinesAdAccountTypeEnum.AD.getType() : adAccountEntity.getType()); // 为空的话默认是巨量竞价广告
            result.setAccountInfo(accountInfo);
            resultList.add(result);
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public SHErrorCode isValidAccount(AdAccountEntity adAccountEntity) {
        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
        HeadlinesRequestResult<GetAccountInfoResultData> headlinesRequestResult = accountApiManager.getHeadlinesAccountInfo(adAccountEntity.getAccountId(), accessToken);
        if (Objects.isNull(headlinesRequestResult)) {
            return SHErrorCode.HEADLINES_API_REQUEST_ERROR;
        }
        return campaignDataManager.queryHeadlinesAccountInvalidCode(headlinesRequestResult);
    }

    @Override
    public Result<GetDataOverviewResult> getDataOverview(GetDataOverviewVO vo) {
        return getNewDataOverView(vo);
    }

    private Result<GetDataOverviewResult> getNewDataOverView(GetDataOverviewVO arg) {
        GetDataOverviewResult dataResult = new GetDataOverviewResult();
        List<Long> campaignIdList = null;
        if (StringUtils.isNotBlank(arg.getAdGroupName())) {
            campaignIdList = headlinesCampaignDAO.pageCampaignIdByName(arg.getEa(), arg.getAdAccountId(), arg.getAdGroupName());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return Result.newSuccess(dataResult);
            }
        }
        HeadlinesAdvertiserDataOverviewEntity advertiserDataOverview = headlinesAdDAO.queryAdDataOverview
                (arg.getEa(), arg.getAdAccountId(), arg.getStartTime(), arg.getEndTime(), arg.getAdPlanName(), campaignIdList);
        if (advertiserDataOverview == null) {
            return Result.newSuccess(dataResult);
        }
        dataResult.setClick(advertiserDataOverview.getClick());
        dataResult.setPv(advertiserDataOverview.getShow());
        if (advertiserDataOverview.getCost() != null) {
            dataResult.setCost((advertiserDataOverview.getCost() * 100) / 100d);
        }
        if (advertiserDataOverview.getClick() != null && advertiserDataOverview.getCost() != null && advertiserDataOverview.getCost() > 0 && advertiserDataOverview.getClick() > 0) {
            BigDecimal costBigDecimal = BigDecimal.valueOf(advertiserDataOverview.getCost());
            BigDecimal clickBigDecimal = BigDecimal.valueOf(advertiserDataOverview.getClick());
            // 使用 BigDecimal 进行精确计算 四舍五入
            BigDecimal clickPriceBigDecimal = costBigDecimal.divide(clickBigDecimal, 2, RoundingMode.HALF_UP);
            dataResult.setClickPrice(clickPriceBigDecimal.doubleValue());
        }
        List<String> marketingEventIds = headlinesAdDAO.getAdLinkSubMarketingEventIds(arg.getEa(), arg.getAdAccountId(), AdSourceEnum.SOURCE_JULIANG.getSource());
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            marketingEventIds = marketingEventIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(marketingEventIds)) {
            dataResult.setLeads(0);
            return Result.newSuccess(dataResult);
        }
        int clueCount = campaignDataManager.getLeadsCountByMarketingEvent(arg.getEa(), marketingEventIds, arg.getStartTime().getTime(), arg.getEndTime().getTime(), null);
        dataResult.setLeads(clueCount);
        return Result.newSuccess(dataResult);
    }


    @Override
    public boolean refreshAccountInfo(AdAccountEntity accountInfo) {
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        HeadlinesRequestResult<GetAccountInfoResultData> headlinesRequestResult = accountApiManager.getHeadlinesAccountInfo(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo));
        log.info("CampaignDataManager refreshAccount getAccountInfo headlinesRequestResult:{}", headlinesRequestResult);
        if (headlinesRequestResult.isSuccess() && headlinesRequestResult.getData() != null) {
            GetAccountInfoResultData resultData = headlinesRequestResult.getData();
            accountInfo.setUserStat(resultData.getUserStat());
            accountInfo.setCost(resultData.getCost() == null ? 0.0 : Double.parseDouble(decimalFormat.format(resultData.getCost())));
            accountInfo.setBudget(resultData.getBudget() == null ? 0.0 : Double.parseDouble(decimalFormat.format(resultData.getBudget())));
            accountInfo.setBudgetType(resultData.getBudgetType());
            accountInfo.setBalance(resultData.getBalance() == null ? 0.0 : Double.parseDouble(decimalFormat.format(resultData.getBalance())));
            adAccountManager.updateAccountRefreshData(accountInfo);
            return true;
        } else {
            log.info("HeadlinesAdMarketingManager refreshAccountInfo getHeadlinesAccountInfo failed, headlinesRequestResult:{}", headlinesRequestResult);
        }
        return false;
    }

    @Override
    public boolean refreshCampaignInfo(AdAccountEntity accountInfo) {
        int pageNum = 1;
        int pageSize = 100;
        List<HeadlinesCampaignResult> headlinesCampaignResultList = Lists.newArrayList();
        try {
            refreshHeadlinesProject(accountInfo);
            syncHeadlinesCampaignToMarketingEventObj(accountInfo.getEa(), accountInfo, AdSourceEnum.SOURCE_JULIANG.getSource());
            // 下面这些 在23年底干掉
            HeadlinesRequestResult<GetHeadlinesCampaignResult> requestResult = campaignApiManager.getHeadlinesCampaign(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), pageNum, pageSize);
            if (requestResult == null || !requestResult.isSuccess() || requestResult.getData() == null || CollectionUtils.isEmpty(requestResult.getData().getList()) || requestResult.getData().getPageInfo() == null) {
                log.info("HeadlinesAdMarketingManager refreshCampaignInfo getHeadlinesCampaign fail, requestResult:{}", requestResult);
                return false;
            }
            headlinesCampaignResultList.addAll(requestResult.getData().getList());
            PageInfo pageInfo = requestResult.getData().getPageInfo();
            if (pageInfo != null && pageInfo.getPage() > 1) {
                for (int i = 2; i <= requestResult.getPageInfo().getTotalPage(); i++) {
                    pageNum = i;
                    HeadlinesRequestResult<GetHeadlinesCampaignResult> headlinesCampaignResult = campaignApiManager.getHeadlinesCampaign(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), pageNum, pageSize);
                    headlinesCampaignResultList.addAll(headlinesCampaignResult.getData().getList());
                }
            }
            log.info("HeadlinesAdMarketingManager refreshCampaignInfo headlinesCampaignResultList: {}", headlinesCampaignResultList);
            headlinesCampaignResultList.forEach(campaignResultData -> {
                try {
                    HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryCampaignByCampaignId(accountInfo.getEa(), campaignResultData.getCampaignId());
                    if (headlinesCampaignEntity == null) {
                        headlinesCampaignEntity = new HeadlinesCampaignEntity();
                        headlinesCampaignEntity.setId(UUIDUtil.getUUID());
                        headlinesCampaignEntity.setCampaignId(campaignResultData.getCampaignId());
                        headlinesCampaignEntity.setEa(accountInfo.getEa());
                        headlinesCampaignEntity.setAdAccountId(accountInfo.getId());
                        headlinesCampaignEntity.setCreateTime(StringUtils.isEmpty(campaignResultData.getCreateTime()) ? new Date() : DateUtil.parse(campaignResultData.getCreateTime()));
                        headlinesCampaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                        headlinesCampaignEntity.setType(TypeEnum.HEADLINES_CAMPAIGN.getCode());
                        headlinesCampaignDAO.addHeadlinesCampaignIgnore(headlinesCampaignEntity);
                    }
                    headlinesCampaignEntity.setBudget(campaignResultData.getBudget());
                    headlinesCampaignEntity.setCampaignName(campaignResultData.getCampaignName());
                    headlinesCampaignEntity.setDeliveryMode(DeliveryModeEnum.fromStr(campaignResultData.getDeliveryMode()));
                    headlinesCampaignEntity.setBudgetMode(BudgetModeEnum.getStatusByName(campaignResultData.getBudgetMode()));
                    headlinesCampaignEntity.setLandingType(LandingTypeEnum.fromStr(campaignResultData.getLandingType()));
                    headlinesCampaignEntity.setStatus(HeadlinesCampaignStatusEnum.fromStr(campaignResultData.getStatus()));
                    headlinesCampaignEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.getStatusByName(campaignResultData.getMarketingPurpose()));
                    headlinesCampaignEntity.setRefreshTime(new Date());
                    headlinesCampaignEntity.setUpdateTime(StringUtils.isEmpty(campaignResultData.getModifyTime()) ? new Date() : DateUtil.parse(campaignResultData.getModifyTime()));
                    headlinesCampaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                    if (headlinesCampaignEntity.getAdAccountId() == null) {
                        headlinesCampaignEntity.setAdAccountId(accountInfo.getId());
                    }
                    headlinesCampaignDAO.updateCampaignForRefresh(headlinesCampaignEntity);
                } catch (Exception e) {
                    log.error("HeadlinesAdMarketingManager refreshHeadlinesCampaignInfo campaignResultData: {}, exception: ", campaignResultData, e);
                }
            });
            syncHeadlinesCampaignToMarketingEventObj(accountInfo.getEa(), accountInfo, AdSourceEnum.SOURCE_JULIANG.getSource());
            return true;
        } catch (Exception e) {
            log.error("HeadlinesAdMarketingManager refreshHeadlinesCampaignInfo accountInfo: {}, exception: ", accountInfo, e);
        }
        return false;
    }

    private boolean refreshOldCampaignData(AdAccountEntity accountInfo, int day) {
        if (day == -1) {
            List<String> dateList = DateUtil.initMonthDateList();
            for (int i = 0; i < dateList.size() - 1; i++) {
                refreshHeadlinesCampaignData(accountInfo, dateList.get(i), dateList.get(i + 1));
            }
            return true;
        } else {
            List<String> dateList = CampaignInitDateUtil.initDateList(day);
            return refreshHeadlinesCampaignData(accountInfo, dateList.get(0), dateList.get(day - 1));
        }
    }
    @Override
    public boolean refreshCampaignData(AdAccountEntity accountInfo, int day) {
        try {
            refreshHeadlineProjectData(accountInfo, day);
            refreshOldCampaignData(accountInfo, day);
        } catch (Exception e) {
            log.error("headline refreshCampaignData error, account: {} day: {}", accountInfo, day);
            return false;
        }
       return true;
    }

    @Override
    public boolean refreshMarketingEventLeads(AdAccountEntity accountInfo, Integer day, List<Date> dateList) {
//        List<HeadlinesAdEntity> adEntityList = headlinesAdDAO.listRelateMarketingEventAd(accountInfo.getEa(), accountInfo.getId(), accountInfo.getSource());
//        if (CollectionUtils.isEmpty(adEntityList)) {
//            return true;
//        }
//        List<List<HeadlinesAdEntity>> partitionList = Lists.partition(adEntityList, 200);
//        for (List<HeadlinesAdEntity> subAdEntityList : partitionList) {
//            Map<String, Map<String, CampaignInitDateUtil.DataUnit>> subMarketingEventDataMap = Maps.newHashMap();
//            try {
//                subAdEntityList.forEach(ad -> {
//                    subMarketingEventDataMap.put(ad.getSubMarketingEventId(), CollectionUtils.isNotEmpty(dateList) ? CampaignInitDateUtil.initDateMapWithAdId(dateList, ad.getAdId()) : CampaignInitDateUtil.initDateMapWithAdId(day, ad.getAdId()));
//                });
//                Set<String> marketingEventIdSets = subMarketingEventDataMap.keySet();
//                List<String> marketingEventIdList = marketingEventIdSets.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(marketingEventIdList)) {
//                    return true;
//                }
//                List<String> timeList = Lists.newArrayList();
//                Date startTime = CollectionUtils.isNotEmpty(dateList) ? DateUtil.getSomeDay(new Date(), -30) : DateUtil.getSomeDay(new Date(), -day);
//                Date endTime = new Date();
//                timeList.add(DateUtil.getTimesMorning(startTime) + "");
//                timeList.add(DateUtil.getTimesMorning(endTime) + "");
//                int totalCount = crmV2Manager.getCrmObjectEntityTotalCount(accountInfo.getEa(), LeadsFieldContants.API_NAME, marketingEventIdList, timeList);
//                int pageSize = 1000;
//                int totalPage = totalCount / pageSize + totalCount % pageSize;
//                for (int pageNum = 0; pageNum < totalPage; pageNum++) {
//                    SearchQuery searchQuery = new SearchQuery();
//                    searchQuery.setLimit(pageSize);
//                    searchQuery.setOffset(pageNum * pageSize);
//                    searchQuery.addFilter(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventIdList, OperatorConstants.IN);
//                    searchQuery.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), timeList, OperatorConstants.BETWEEN);
//                    Page<ObjectData> page = crmV2Manager.getList(accountInfo.getEa(), -10000, LeadsFieldContants.API_NAME, searchQuery);
//                    List<ObjectData> dataList = page.getDataList();
//                    if (CollectionUtils.isEmpty(dataList)) {
//                        log.info("HeadlinesAdMarketingManager refreshMarketingEventLeads crmV2Manager.getList fail, dataList is null");
//                        continue;
//                    }
//                    for (ObjectData objectData : dataList) {
//                        LeadsData leadsData = LeadsData.wrap(objectData);
//                        try {
//                            String subMarketingEventId = leadsData.getString(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName());
//                            String createTime = DateUtil.parse(new Date(leadsData.getCreateTime()), DateUtil.DATE_FORMAT_DAY);
//                            Map<String, CampaignInitDateUtil.DataUnit> dataUnitMap = subMarketingEventDataMap.get(subMarketingEventId);
//                            if (dataUnitMap == null) {
//                                continue;
//                            }
//                            CampaignInitDateUtil.DataUnit dataUnit = dataUnitMap.get(createTime);
//                            if (dataUnit == null) {
//                                continue;
//                            }
//                            dataUnit.increaseLeads();
//                        } catch (Exception e) {
//                            log.error("HeadlinesAdMarketingManager queryLeads handler objectData:{}, exception:", objectData, e);
//                        }
//                    }
//                    for (Map<String, CampaignInitDateUtil.DataUnit> dataUnitMap : subMarketingEventDataMap.values()) {
//                        saveAdData(accountInfo.getEa(), accountInfo.getId(), Lists.newArrayList(dataUnitMap.values()));
//                    }
//                }
//                return true;
//
//            } catch (Exception e) {
//                log.error("HeadlinesAdMarketingManager queryHeadlinesLeads crmV2Manager.getList, exception:", e);
//            }
//        }
//        return false;
        return true;
    }


    @Override
    public void refreshAllData(String ea, String adAccountId, String source) {
        HeadlinesRequestResult<GetAccountInfoResultData> headlinesAccountResult = null;
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("refreshBaiduData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }

        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }

        if (!adAccountEntity.getStatus().equals(AccountStatusEnum.NORMAL.getStatus()) && !adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
            log.info("syncCampaignDataToCRM account status is not normal accountEntity:{}", adAccountEntity);
            return;
        }

        // 先去看看当前的广告账户类型是否是本地推账户，是就只刷本地推账户的数据
        try {
            if (adAccountEntity.getType() != null && HeadlinesAdAccountTypeEnum.LOCAL.getType() == adAccountEntity.getType()) {
                refreshLocalAccountData(adAccountEntity);
                return;
            }
        } catch (Exception e) {
            log.error("同步巨量引擎本地推广告数据和线索出错，ea: {} accountId: {}", adAccountEntity.getEa(), adAccountEntity.getId(), e);
            return;
        }

        try {
            headlinesAccountResult = accountApiManager.getHeadlinesAccountInfo(adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity));
            if (campaignDataManager.queryHeadlinesAccountInvalidCode(headlinesAccountResult) != SHErrorCode.SUCCESS) {
                log.info("refreshBaiduData baidu account is invalid accountInfo:{}", adAccountEntity);
                return;
            }
        } catch (Exception e) {
            log.error("refreshAllData getHeadlinesAccountInfo exception ea:{} adAccountId:{} source:{} e:", ea, adAccountId, source, e);
            return;
        }
        try {
            // 同步巨量引擎数据
            refreshHeadlinesCampaign(adAccountEntity, source);
            refreshOldCampaignData(adAccountEntity, 7);
            refreshHeadlinesAd(adAccountEntity, source);
            refreshHeadlinesAdData(adAccountEntity, 7);
        } catch (Exception e) {
            log.error("同步头条旧版数据出错，ea: {} accountId: {}", ea, adAccountId, e);
        }

        try {
            // 同步头条的项目
            refreshHeadlinesProject(adAccountEntity);
            syncHeadlinesCampaignToMarketingEventObj(ea, adAccountEntity, source);
            // 同步头条的广告
            refreshHeadlinesPromotion(adAccountEntity);
            syncHeadlinesAdToSubMarketingEventObj(ea, adAccountEntity, source);
            // 同步头条项目和广告的展点消
            refreshHeadlineProjectData(adAccountEntity, 7);
            refreshHeadlinePromotionData(adAccountEntity, 7);
            syncClueDataToClueObj(ea, adAccountEntity.getId(), source);
        } catch (Exception e) {
            log.error("同步头条新版数据出错，ea: {} accountId: {}", ea, adAccountId, e);
        }

    }

    @Override
    public void refreshKeywordData(String ea, String adAccountId, String source) {
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            log.info("headlines refreshKeywordData failed check no marketing_integration_app version ea:{}", ea);
            return;
        }
        if (!adCommonManager.isSyncAdKeyword(ea)) {
            log.info("headlines isSyncAdKeyword ea: {} is not sync keyword:", ea);
            return;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }

        if (!adAccountEntity.getStatus().equals(AccountStatusEnum.NORMAL.getStatus()) && !adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
            log.info("headlines refreshKeywordData account status is not normal accountEntity:{}", adAccountEntity);
            return;
        }
        if (adAccountEntity.getType() != null && HeadlinesAdAccountTypeEnum.LOCAL.getType() == adAccountEntity.getType()) {
            log.info("headlines local is not sync keyword, adAccountEntity: {}", adAccountEntity);
            return;
        }
        refreshHeadlinesKeyWord(adAccountEntity, source);
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(ea, adAccountEntity.getId(), source);
    }

    public void refreshLocalAccountData(AdAccountEntity adAccountEntity) {
        /**
         * 同步不了本地推的项目列表和广告列表了，因为巨量引擎本地推拉取项目和广告列表的接口不支持拉取推广目的为“获取线索”的项目和广告列表
         * 所以只在获取广告的报表数据的时候来存项目列表和广告列表了
         * 并且获取的报表数据中只有id和名称这两个字段，因此项目和广告列表只能存这两个值了
         */
        // 同步巨量引擎本地推的项目
        //refreshHeadlinesLocalProject(adAccountEntity);
        // 同步巨量引擎本地推的广告
        //refreshHeadlinesLocalPromotion(adAccountEntity);
        // 定时刷新本地推账户余额
        refreshLocalAccountInfo(adAccountEntity);
        // 同步巨量引擎本地推广告的展点消
        refreshHeadlineLocalPromotionData(adAccountEntity, 1); // day为1表示刷新前1天的广告数据
    }

    // 获取上一次本地推线索的同步时间
    public Date getLastLocalClueSyncTime(String ea, String adAccountId) {
        String key = String.format(LOCAL_SYNC_TIME_KEY, ea, adAccountId);
        String timeStr = redisManager.get(key);
        return StringUtils.isNotBlank(timeStr) ? DateUtil.parse(timeStr) : null;
    }

    public void setLastLocalClueSyncTime(String ea, String adAccountId, Date time) {
        String key = String.format(LOCAL_SYNC_TIME_KEY, ea, adAccountId);
        redisManager.set(key, 60 * 60 * 24, DateUtil.format(time));
    }


    public void syncHeadlinesLocalClueDataToClueObj(AdAccountEntity adAccountEntity, Date startTime, Date endTime) {
        try {
            int page = 1;
            int pageSize = 100; // 一次最多可拉100条
            HeadlinesRequestResult<GetHeadlinesLocalClueResult> result = campaignApiManager.getHeadlinesLocalClueData(adAccountEntity, startTime, endTime, page, pageSize);
            if (result == null || !result.isSuccess()) {
                log.warn("syncHeadlinesLocalClueDataToClueObj fail adAccount:{}, result:{}", adAccountEntity, result);
                return;
            }
            GetHeadlinesLocalClueResult resultData = result.getData();
            if (resultData == null || CollectionUtils.isEmpty(resultData.getHeadlinesLocalClueResultList())) {
                log.info("syncHeadlinesLocalClueDataToClueObj empty adAccount:{}, resultData:{}", adAccountEntity, result.getData());
                setLastLocalClueSyncTime(adAccountEntity.getEa(), adAccountEntity.getId(), endTime);
                return;
            }
            List<GetHeadlinesLocalClueResult.HeadlinesLocalClueResult> localClueResultList = resultData.getHeadlinesLocalClueResultList();
            // 先同步第一页
            localClueResultList.forEach(localClueResult -> saveLocalLeadsToCrm(localClueResult, adAccountEntity));
            LocalCluePageInfo pageInfo = resultData.getPageInfo();
            if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
                setLastLocalClueSyncTime(adAccountEntity.getEa(), adAccountEntity.getId(), endTime);
                return;
            }
            log.info("HeadlinesAdMarketingManager syncHeadlinesLocalClueDataToClueObj sync more page info");
            for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
                result = campaignApiManager.getHeadlinesLocalClueData(adAccountEntity, startTime, endTime, i, pageSize);
                if (result == null || !result.isSuccess()) {
                    log.warn("syncHeadlinesLocalClueDataToClueObj fail adAccount:{}, result:{}", adAccountEntity, result);
                    continue;
                }
                resultData = result.getData();
                if (resultData == null || CollectionUtils.isEmpty(resultData.getHeadlinesLocalClueResultList())) {
                    log.info("syncHeadlinesLocalClueDataToClueObj empty adAccount:{}, resultData:{}", adAccountEntity, result.getData());
                    continue;
                }
                localClueResultList = resultData.getHeadlinesLocalClueResultList();
                localClueResultList.forEach(localClueResult -> saveLocalLeadsToCrm(localClueResult, adAccountEntity));
            }
            setLastLocalClueSyncTime(adAccountEntity.getEa(), adAccountEntity.getId(), endTime);
        } catch (Exception e) {
            log.error("HeadlinesAdMarketingManager syncHeadlinesLocalClueDataToClueObj fail ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity, e);
        }
    }


    public Result<Void> saveLocalLeadsToCrm(GetHeadlinesLocalClueResult.HeadlinesLocalClueResult data, AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        log.info("headlinesAdMarketingManager saveLocalLeadsToCrm ea:{} adAccount:{} data:{}", ea, adAccountEntity, data);
        // 获取分布式锁来同步线索数据，防止线索重复 mk:local:lead:sync:obj
        // key加上ea，避免那种一个头条账号 绑定多个crm账号，去竞争同一把锁的情况
        String redisKey = "mk:mllso:" + ea + ":" + data.getClueId();
        try {
            if (StringUtils.isEmpty(data.getClueId())) {
                log.info("HeadlinesAdMarketing saveLocalLeadsToCrm fail: clue_id is null ea:{}, adAccount:{}. data:{}", ea, adAccountEntity, data);
                return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_182));
            }
            boolean lock = redisManager.lock(redisKey, 300);
            if (!lock) { // 获取锁失败，说明有其它线程在同步改条线索数据
                log.info("HeadlinesAdMarketingManager saveLocalLeadsToCrm get redis lock fail; other thread is syncing ea:{}, adAccount:{}, data:{}", adAccountEntity.getEa(), adAccountEntity, data);
                return Result.newSuccess();
            }
            // 查询广告线索表看是否已同步过
            AdLeadsEntity adLeadsEntity = adLeadsDAO.queryLeadsByEaAndSourceLeadIdAndSource(ea, data.getClueId(), AdSourceEnum.SOURCE_JULIANG.getSource());
            if (adLeadsEntity != null) {
                log.warn("HeadlinesAdMarketingManager sync clue is exited ea:{}, adAccount:{}, data:{}", ea, adAccountEntity, data);
                //tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                return Result.newSuccess();
            }
            // 根据线索字段插件映射设置得到要存入线索对象的参数
            Map<String, Object> params = buildLeadObjFieldByFormMapping(data, ea);
            if (params == null) {
                log.info("HeadlinesAdMarketingManager buildLeadObjFieldByFormMapping param is null ea:{}, adAccount:{}, data:{}", ea, adAccountEntity, data);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_182));
                return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_183));
            }
            // 添加广告id对应的市场活动id
            String subMarketingEventId = headlinesAdDAO.querySubMarketingEventIdByAdId(ea, adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), data.getPromotionId());
            // 这里获取的市场活动id可能是空的，因为广告列表每天凌晨4点才会创建新的广告列表并同步市场活动，
            // 所以如果当天刚创建的广告列表还没来得及同步到市场活动，这个时候获取的市场活动id就是为空的
            // 因此这里重新再去主动同步市场活动
            if (StringUtils.isBlank(subMarketingEventId)) {
                // 同步今天刚创建的项目和广告列表及其市场活动
                refreshTodayProjectAndPromotionByPromotionReportData(adAccountEntity, data.getPromotionId());
                // 再获取一次该条广告的市场活动id
                subMarketingEventId = headlinesAdDAO.querySubMarketingEventIdByAdId(ea, adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), data.getPromotionId());
                if (StringUtils.isBlank(subMarketingEventId)) { // 如果这次市场活动id还是为空就记录一条错误日志
                    log.error("HeadlinesAdMarketingManager saveLocalLeadsToCrm refreshTodayProjectAndPromotionByPromotionReportData subMarketingEventId is still null ea:{}, adAccount:{}, data:{}", ea, adAccountEntity, data);
                }
            }

            if (StringUtils.isNotBlank(subMarketingEventId)) {
                params.put("marketing_event_id", subMarketingEventId);
            }
            //设置线索推广渠道promotion_channel
            params.put("promotion_channel", SpreadChannelEnum.AD.getCode()); // 推广渠道就是广告

            ObjectData objectData = new ObjectData();
            objectData.putAll(params);
            objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());
            // 添加营销推广来源
            String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByLocalLeadData(data, adAccountEntity, subMarketingEventId);
            objectData.put(CrmV2LeadFieldEnum.MarketingPromotionSourceId.getNewFieldName(), marketingPromotionSourceId);

            Integer createBy = clueDefaultSettingService.getClueCreator(subMarketingEventId, ea, ClueDefaultSettingTypeEnum.AD.getType());
            List<String> dataOwnOrganizationList = clueManagementManager.getDataOwnOrganization(ea, createBy);
            if (CollectionUtils.isNotEmpty(dataOwnOrganizationList)) {
                objectData.put(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION, dataOwnOrganizationList); // 获取归属组织吗
            }

            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);
            ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
            optionInfo.setIsDuplicateSearch(true);
            optionInfo.setCalculateDefaultValue(true);
            actionAddArg.setOptionInfo(optionInfo);
            // 添加到营销线索对象
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(ea, createBy), CrmObjectApiNameEnum.CRM_LEAD.getName(), false, actionAddArg);
            if (result != null && result.isSuccess()) {
                String leadId = result.getData().getObjectData().getId();
                baiduAdMarketingManager.syncCampaignMember(ea, leadId, AdSourceEnum.SOURCE_JULIANG.getSource(), data.getPromotionName(), null, false, null, null, null, subMarketingEventId);
                log.info("HeadlinesAdMarketingManager saveLocalLeadsToCrm success");
                tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, leadId, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_750));
                // 插入线索到ad_leads表
                insertAdLeadsEntity(adAccountEntity, leadId, data.getClueId());
                return Result.newSuccess();
            } else if (result != null && result.getCode() == CrmConstants.REPEAT_CODE) {
                log.warn("HeadlinesAdMarketingManager saveLocalLeadsToCrm fail duplicate lead in CRM data:{}, result:{}", data, result);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                return Result.newSuccess();
            } else {
                log.warn("HeadlinesAdMarketingManager saveLocalLeadsToCrm add local clue to crm fail ea:{}, adAccount:{}, result:{}", ea, adAccountEntity, result);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, result != null && result.getMessage() != null ? result.getMessage() : I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
                return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_368));
            }
        } catch (Exception e) {
            log.error("headlinesAdMarketingManager saveLocalLeadsToCrm fail ea:{}, adAccount:{}, data:{}", ea, adAccountEntity, data, e);
            String message = e.getMessage() == null ? I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR) : e.getMessage();
            tryCreateOrUpdateMarketingLeadSyncRecordObj(ea, data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
            return Result.newError(-1, message);
        } finally {
            redisManager.unLock(redisKey);
        }
    }

    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(String ea, GetHeadlinesLocalClueResult.HeadlinesLocalClueResult data, String leadId, String status, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg arg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        arg.setEa(ea);
        arg.setRemark(remark);
        String mobile = data.getTelephone();
        if (StringUtils.isNotBlank(mobile) && !PhoneNumberCheck.isPhoneLegal(mobile)) {
            mobile = null;
        }
        arg.setMobile(mobile);
        arg.setSyncData(JSONObject.toJSONString(data));
        arg.setOutPlatformDataId(data.getClueId()); // 外部平台数据ID设置为外部线索ID
        arg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_284));
        arg.setOutPlatformType(MarketingLeadSyncRecordObjManager.AD_PLATFORM); // 平台类型为广告
        arg.setLeadName(data.getPromotionName());
        arg.setSyncStatus(status);
        arg.setLeadId(leadId);

        // 查询当前外部线索是否已经同步过营销线索同步记录对象
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_LEAD_SYNC_RECORD_OBJ.getName());
        paasQueryFilterArg.setSelectFields(Lists.newArrayList("_id", "sync_status"));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("out_platform_data_id", FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(data.getClueId()));
        paasQueryArg.addFilter("out_platform_name", FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_284)));
        paasQueryFilterArg.setQuery(paasQueryArg);
        InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg, 1, 1);
        List<ObjectData> objectDataList = innerPage.getDataList();
        if (CollectionUtils.isNotEmpty(objectDataList) && StringUtils.isNotEmpty(objectDataList.get(0).getId())
                && objectDataList.get(0).get("sync_status") != null && objectDataList.get(0).get("sync_status").equals(MarketingLeadSyncRecordObjManager.SUCCESS_STATUS)) { // 有该线索的营销同步记录且同步状态为SUCCESS不再记录
            return;
        }
        if (CollectionUtils.isNotEmpty(objectDataList) && StringUtils.isNotEmpty(objectDataList.get(0).getId())) { // 有该线索的营销同步记录但同步状态不为SUCCESS则更新该同步记录状态
            arg.setMarketingLeadSyncRecordObjId(objectDataList.get(0).getId());
        }
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(arg);

    }

    private void insertAdLeadsEntity(AdAccountEntity adAccountEntity, String leadId, String sourceLeadId) {
        AdLeadsEntity adLeadsEntity = new AdLeadsEntity();
        adLeadsEntity.setId(UUIDUtil.getUUID());
        adLeadsEntity.setEa(adAccountEntity.getEa());
        adLeadsEntity.setLeadId(leadId);
        adLeadsEntity.setCreateTime(new Date());
        adLeadsEntity.setUpdateTime(new Date());
        adLeadsEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        adLeadsEntity.setSource_lead_id(sourceLeadId);
        adLeadsEntity.setAdAccountId(adAccountEntity.getId());
        adLeadsDAO.addAdLeads(adLeadsEntity);
    }

    private Map<String, Object> buildLeadObjFieldByFormMapping(GetHeadlinesLocalClueResult.HeadlinesLocalClueResult data, String ea) {
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPluginFieldMap(ea, MarketingPluginTypeEnum.HEADLINES_AD.getType(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (marketingPluginConfigEntity == null || marketingPluginConfigEntity.getCrmFormFieldMap() == null) {
            log.info("HeadlinesAdMarketingManager buildLeadObjFieldByFormMapping marketingPluginConfig is null ea:{}", ea);
            return null;
        }
        FieldMappings crmFormFieldMap = marketingPluginConfigEntity.getCrmFormFieldMap();
        Map<String, Object> objectData = Maps.newHashMap();
        // 获取字段描述
        Map<String, CrmUserDefineFieldVo> fieldNameMap = Maps.newHashMap(); // 主要用于当字段为多选类型的时候，如果需要设置默认值需要用list来承载数据
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
        if (CollectionUtils.isNotEmpty(crmUserDefineFieldVoList)) {
            fieldNameMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, e -> e, (v1, v2) -> v1));
        }
        for (FieldMappings.FieldMapping fieldMapping : crmFormFieldMap) {
            if (StringUtils.isNotEmpty(fieldMapping.getCrmFieldName())) {
                Object fieldValue = data.getFieldValueByName(fieldMapping.getMankeepFieldName());
                if (fieldValue == null) {
                    fieldValue = fieldNameMap.get(fieldMapping.getCrmFieldName()).getFieldTypeName().equals(CrmV2FieldTypeEnum.SelectMany.getName()) ?
                            Lists.newArrayList(fieldMapping.getDefaultValue()) : fieldMapping.getDefaultValue();
                }
                objectData.put(fieldMapping.getCrmFieldName(), fieldValue);
            }
        }
        objectData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), marketingPluginConfigEntity.getCrmPoolId());
        objectData.put(CrmV2LeadFieldEnum.RecordType.getNewFieldName(), marketingPluginConfigEntity.getCrmRecordType());
        log.info("HeadlinesAdMarketingManager createObjectDataToCrmLeadFieldDataMap objectData:{}", objectData);
        return objectData;
    }

    public void refreshHeadlineLocalPromotionData(AdAccountEntity adAccountEntity, int day) {
        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("refreshHeadlinesLocalPromotionData accessToken is null adAccount:{}", adAccountEntity);
            return;
        }
        int page = 1;
        int pageSize = 100; // 巨量引擎每次最多可获取100条广告数据
        // 项目列表去重set
        Set<String> existedCampaignSet = Sets.newHashSet();
        Date now = new Date();
        Date startDate = DateUtil.minusDay(now, day);
        while (startDate.before(now)) {
            Date endDate = DateUtil.plusDay(startDate, 7); // 每次最多获取7天跨度的广告数据 文档接口那边当time_granularity = TIME_GRANULARITY_HOURLY时，时间跨度不能超过7天
            if (endDate.after(now)) {
                endDate = now;
            }
            HeadlinesRequestResult<GetLocalPromotionReportResult> requestResult = campaignApiManager.getLocalPromotionReportResult(adAccountEntity.getAccountId(), accessToken, startDate, endDate, page, pageSize, null);
            if (requestResult == null || !requestResult.isSuccess()) {
                log.warn("refreshHeadlinesLocalPromotionData fail startDate:{}, endDate:{}, adAccount:{}, requestResult:{}", startDate, endDate, adAccountEntity, requestResult);
                startDate = DateUtil.plusDay(endDate, 1); // 让开始时间指向结束时间的下一天开始新一轮的时间范围数据查询
                continue;
            }
            GetLocalPromotionReportResult resultData = requestResult.getData();
            if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalPromotionReportResultList())) {
                log.info("refreshHeadlinesLocalPromotionData empty startDate:{}, endDate:{}, adAccount:{}, resultData:{}", startDate, endDate, adAccountEntity, resultData);
                startDate = DateUtil.plusDay(endDate, 1);
                continue;
            }
            List<GetLocalPromotionReportResult.LocalPromotionReportResult> localPromotionReportResultList = resultData.getLocalPromotionReportResultList();
            // 先处理第一页的数据
            // 自己注入自己，注入的是一个新对象，这样在内部通过该对象来调用@Transactional的方法事务才会生效
            // 因为Spring事务是通过代理对象来实现的，内部方法自己调用@Transactional方法Spring无法生成代理对象
            adMarketingManager.handleLocalPromotionReportData(adAccountEntity, localPromotionReportResultList, existedCampaignSet);
            PageInfo pageInfo = resultData.getPageInfo();
            if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
                startDate = DateUtil.plusDay(endDate, 1);
                continue;
            }
            for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
                requestResult = campaignApiManager.getLocalPromotionReportResult(adAccountEntity.getAccountId(), accessToken, startDate, endDate, i, pageSize, null);
                if (requestResult == null || !requestResult.isSuccess()) {
                    log.warn("refreshLocalPromotionReportData fail startDate:{}, endDate:{}, adAccount:{}, requestResult:{}", startDate, endDate, adAccountEntity, requestResult);
                    break;
                }
                resultData = requestResult.getData();
                if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalPromotionReportResultList())) {
                    log.info("refreshLocalPromotionReportDate empty startDate:{}, endDate:{}, adAccount:{}, resultData:{}", startDate, endDate, adAccountEntity, resultData);
                    break;
                }
                localPromotionReportResultList = resultData.getLocalPromotionReportResultList();
                adMarketingManager.handleLocalPromotionReportData(adAccountEntity, localPromotionReportResultList, existedCampaignSet);
            }
            startDate = DateUtil.plusDay(endDate, 1);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleLocalPromotionReportData(AdAccountEntity adAccountEntity,
                                               List<GetLocalPromotionReportResult.LocalPromotionReportResult> localPromotionReportResultList,
                                               Set<String> existedCampaignSet) {
        if (CollectionUtils.isEmpty(localPromotionReportResultList)) {
            return;
        }
        List<HeadlinesAdDataEntity> entityList = Lists.newArrayList();
        List<HeadlinesCampaignEntity> headlinesCampaignEntityList = Lists.newArrayList();
        List<HeadlinesAdEntity> headlinesAdEntityList = Lists.newArrayList();
        localPromotionReportResultList.forEach(localPromotionReport -> {
            if (!checkSingleIsAllZero(localPromotionReport)) { // 过滤掉展点消都为0的数据
                HeadlinesAdDataEntity adDataEntity = new HeadlinesAdDataEntity();
                adDataEntity.setId(UUIDUtil.getUUID());
                adDataEntity.setEa(adAccountEntity.getEa());
                adDataEntity.setAdId(localPromotionReport.getPromotionId());
                adDataEntity.setCampaignId(localPromotionReport.getProjectId());
                adDataEntity.setStatDatetime(DateUtil.parse(localPromotionReport.getStatTimeDay(), "yyyy-MM-dd"));
                adDataEntity.setShow(localPromotionReport.getShowCnt() == null ? 0L : localPromotionReport.getShowCnt());
                adDataEntity.setClick(localPromotionReport.getClickCnt() == null ? 0L : localPromotionReport.getClickCnt());
                adDataEntity.setCost(localPromotionReport.getStatCost() == null ? 0.0D : localPromotionReport.getStatCost());
                adDataEntity.setAvgClickCost(localPromotionReport.getCpcPlatForm() == null ? 0.0D : localPromotionReport.getCpcPlatForm());
                adDataEntity.setCreateTime(new Date());
                adDataEntity.setUpdateTime(new Date());
                adDataEntity.setAdAccountId(adAccountEntity.getId());
                adDataEntity.setType(TypeEnum.HEADLINES_LOCAL_PROMOTION.getCode());
                entityList.add(adDataEntity);
            }

            // 本地推项目列表
            if (!existedCampaignSet.contains(localPromotionReport.getProjectId() + localPromotionReport.getProjectName())) { // 一个Project下面可能会有n个Promotion，为避免创建过多的project对象进行去重
                HeadlinesCampaignEntity headlinesCampaignEntity = new HeadlinesCampaignEntity();
                headlinesCampaignEntity.setId(UUIDUtil.getUUID());
                headlinesCampaignEntity.setEa(adAccountEntity.getEa());
                headlinesCampaignEntity.setAdAccountId(adAccountEntity.getId());
                headlinesCampaignEntity.setCampaignId(localPromotionReport.getProjectId());
                headlinesCampaignEntity.setCampaignName(localPromotionReport.getProjectName());
                headlinesCampaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                headlinesCampaignEntity.setType(TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
                headlinesCampaignEntity.setRefreshTime(new Date());
                // 设置默认值防止更新sql报错
                headlinesCampaignEntity.setBudget(0.0);
                headlinesCampaignEntity.setBudgetMode(-1);
                headlinesCampaignEntity.setLandingType(-1);
                headlinesCampaignEntity.setStatus(-1);
                headlinesCampaignEntity.setMarketingPurpose(-2);
                headlinesCampaignEntity.setDeliveryMode(-1);
                headlinesCampaignEntityList.add(headlinesCampaignEntity);
                existedCampaignSet.add(localPromotionReport.getProjectId() + localPromotionReport.getProjectName());
            }

                // 本地推广告列表
                HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
                headlinesAdEntity.setId(UUIDUtil.getUUID());
                headlinesAdEntity.setEa(adAccountEntity.getEa());
                headlinesAdEntity.setAdId(localPromotionReport.getPromotionId());
                headlinesAdEntity.setAdName(localPromotionReport.getPromotionName());
                headlinesAdEntity.setCampaignId(localPromotionReport.getProjectId());
                headlinesAdEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                headlinesAdEntity.setAdAccountId(adAccountEntity.getId());
                headlinesAdEntity.setType(TypeEnum.HEADLINES_LOCAL_PROMOTION.getCode());
                headlinesAdEntity.setRefreshTime(new Date());
                headlinesAdEntity.setUpdateTime(new Date());
                // 以下默认设置防止批量更新sql报错
                headlinesAdEntity.setStatus(-1);
                headlinesAdEntity.setOptStatus(-1);
                headlinesAdEntity.setDeliveryRange(-1);
                headlinesAdEntity.setInventoryCatalog(-1);
                headlinesAdEntity.setInventoryType(Lists.newArrayList(""));
                headlinesAdEntityList.add(headlinesAdEntity);

        });
        // 要插入或更新的项目列表
        List<HeadlinesCampaignEntity> insertHeadlinesCampaignEntityList = Lists.newArrayList();
        List<HeadlinesCampaignEntity> updateHeadlinesCampaignEntityList = Lists.newArrayList();
        // 要插入或更新的广告列表
        List<HeadlinesAdEntity> insertHeadlinesAdEntityList = Lists.newArrayList();
        List<HeadlinesAdEntity> updateHeadlinesAdEntityList = Lists.newArrayList();
        // 已存在的本地推项目列表
        List<HeadlinesCampaignEntity> existedHeadlinesCampaignList = headlinesCampaignDAO.queryCampaignListByCampaignIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource()
                , headlinesCampaignEntityList.stream().map(HeadlinesCampaignEntity::getCampaignId).collect(Collectors.toList()), TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
        Map<Long, HeadlinesCampaignEntity> existedHeadlinesCampaignMap = existedHeadlinesCampaignList.stream().collect(Collectors.toMap(HeadlinesCampaignEntity::getCampaignId, e -> e, (v1, v2) -> v1));
        // 已存在的本地推广告列表
        List<HeadlinesAdEntity> existedHeadlinesAdEntityList = headlinesAdDAO.queryAdListByAdIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource()
                , headlinesAdEntityList.stream().map(HeadlinesAdEntity::getAdId).collect(Collectors.toList()));
        Map<Long, HeadlinesAdEntity> existedHeadlinesAdMap = existedHeadlinesAdEntityList.stream().collect(Collectors.toMap(HeadlinesAdEntity::getAdId, e -> e, (v1, v2) -> v1));

        for (HeadlinesCampaignEntity campaignEntity : headlinesCampaignEntityList) {
            HeadlinesCampaignEntity existedCampaignEntity = existedHeadlinesCampaignMap.get(campaignEntity.getCampaignId());
            if (existedCampaignEntity == null) { // 加入插入列表
                insertHeadlinesCampaignEntityList.add(campaignEntity);
            } else { // 加入更新列表
                campaignEntity.setId(existedCampaignEntity.getId());
                updateHeadlinesCampaignEntityList.add(campaignEntity);
            }
        }
        for (HeadlinesAdEntity adEntity : headlinesAdEntityList) {
            HeadlinesAdEntity existedAdEntity = existedHeadlinesAdMap.get(adEntity.getAdId());
            if (existedAdEntity == null) { // 加入插入列表
                insertHeadlinesAdEntityList.add(adEntity);
            } else { // 加入更新列表
                adEntity.setId(existedAdEntity.getId());
                updateHeadlinesAdEntityList.add(adEntity);
            }
        }

        boolean havingSaveOrUpdateCampaign = false; // 广告列表没有做这样的控制，因为只同步昨天一天的广告数据，所以广告列表不会有重复
        if (CollectionUtils.isNotEmpty(insertHeadlinesCampaignEntityList)) { // 插入项目列表
            havingSaveOrUpdateCampaign = true;
            headlinesCampaignDAO.batchAddHeadlinesCampaign(insertHeadlinesCampaignEntityList);
        }
        if (CollectionUtils.isNotEmpty(updateHeadlinesCampaignEntityList)) { // 更新项目列表
            havingSaveOrUpdateCampaign = true;
            headlinesCampaignDAO.batchUpdateCampaign(adAccountEntity.getEa(), adAccountEntity.getId(), updateHeadlinesCampaignEntityList);
        }
        if (CollectionUtils.isNotEmpty(insertHeadlinesAdEntityList)) { // 插入广告列表
            headlinesAdDAO.batchAddHeadlinesAd(insertHeadlinesAdEntityList);
        }
        if (CollectionUtils.isNotEmpty(updateHeadlinesAdEntityList)) { // 更新广告列表
            headlinesAdDAO.batchUpdateAd(adAccountEntity.getEa(), adAccountEntity.getId(), updateHeadlinesAdEntityList);
        }

        // 同步市场活动到crm
        // 现在只能通过拉取广告数据的方式来同步项目和广告列表，而对于一整批广告数据，（例如一天有一万条广告数据要同步，但是每次只能同步100条）
        // 只要项目列表在这一次的同步一万条广告数据时某个轮询中已经同步过，那后续就不会再同步，也不会再同步市场活动
        // 而项目列表的名字是可能发生变更的，因此使用 projectId + projectName 作为Key来去重，这样项目列表名变更了也会更新市场活动
        if (havingSaveOrUpdateCampaign) {
            syncHeadlinesCampaignToMarketingEventObj(adAccountEntity.getEa(), adAccountEntity, AdSourceEnum.SOURCE_JULIANG.getSource());
        }
        syncHeadlinesAdToSubMarketingEventObj(adAccountEntity.getEa(), adAccountEntity, AdSourceEnum.SOURCE_JULIANG.getSource());

        // 处理广告报表数据
        Set<Long> adDataIdSet = Sets.newHashSet();
        Set<Date> statDatetimeSet = Sets.newHashSet();
        entityList.forEach(entity -> {
            adDataIdSet.add(entity.getAdId());
            statDatetimeSet.add(entity.getStatDatetime());
        });
        if (CollectionUtils.isEmpty(adDataIdSet)) {
            return;
        }
        // 通过广告id和数据起始时间查询出已存在的广告数据
        List<HeadlinesAdDataEntity> headlinesAdDataEntityList = headlinesAdDataDAO.queryByAdIdAndStatTimeDate(adAccountEntity.getEa()
                , adAccountEntity.getId(), Lists.newArrayList(adDataIdSet), Lists.newArrayList(statDatetimeSet), TypeEnum.HEADLINES_LOCAL_PROMOTION.getCode());
        // 广告id+数据起始时间stat_datetime来确定某一天的一条广告数据
        Map<String, HeadlinesAdDataEntity> exitedMap = headlinesAdDataEntityList.stream().collect(Collectors.toMap(e -> e.getAdId() + DateUtil.parse(e.getStatDatetime(), DateUtil.DATE_FORMAT_DAY), e -> e, (v1, v2) -> v1));
        List<HeadlinesAdDataEntity> insertList = Lists.newArrayList();
        List<HeadlinesAdDataEntity> updateList = Lists.newArrayList();
        entityList.forEach(entity -> {
            String key = entity.getAdId() + DateUtil.parse(entity.getStatDatetime(), DateUtil.DATE_FORMAT_DAY);
            if (exitedMap.containsKey(key)) { // 更新
                // 取回原来表中的id
                entity.setId(exitedMap.get(key).getId());
                updateList.add(entity);
            } else {
                insertList.add(entity);
            }
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            headlinesAdDataDAO.batchUpdateHeadlinesAdDataRefreshData(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            headlinesAdDataDAO.batchInsert(insertList);
        }
        // 创建或更新广告明细对象
        buildAdvertisingDetailsObj(adAccountEntity.getEa(), adAccountEntity.getId(), entityList);

    }

    private boolean checkSingleIsAllZero(GetLocalPromotionReportResult.LocalPromotionReportResult localPromotionReport) {
        // 接口返回数据："show_cnt":0,"click_cnt":0,"stat_cost":0.0,"cpc_platform":0.0
        return localPromotionReport.getShowCnt() == null ||
               localPromotionReport.getClickCnt() == null ||
               localPromotionReport.getStatCost() == null ||
               localPromotionReport.getCpcPlatForm() == null ||
                (localPromotionReport.getShowCnt() == 0 && localPromotionReport.getClickCnt() == 0 && localPromotionReport.getStatCost() == 0.0 && localPromotionReport.getCpcPlatForm() == 0.0);
    }

    /**
     * 通过获取当天的一条广告报表数据来创建项目或广告列表
     * @param adAccountEntity 广告账户
     * @param promotionId 巨量引擎的广告id
     */
    public void refreshTodayProjectAndPromotionByPromotionReportData(AdAccountEntity adAccountEntity, Long promotionId) {
        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("refreshHeadlinesLocalPromotionData accessToken is null adAccount:{}", adAccountEntity);
            return;
        }
        int page = 1;
        int pageSize = 1; // 只获取一条广告报表数据用于创建项目或广告列表
        Date startDate = new Date();
        Date endDate = new Date();
        HeadlinesRequestResult<GetLocalPromotionReportResult> requestResult = campaignApiManager.getLocalPromotionReportResult(adAccountEntity.getAccountId(), accessToken, startDate, endDate, page, pageSize, promotionId);
        if (requestResult == null || !requestResult.isSuccess()) {
            log.warn("refreshTodayProjectAndPromotionByPromotionData fail startDate:{}, endDate:{}, adAccount:{}, requestResult:{}", startDate, endDate, adAccountEntity, requestResult);
            return;
        }
        GetLocalPromotionReportResult resultData = requestResult.getData();
        if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalPromotionReportResultList())) {
            log.info("refreshTodayProjectAndPromotionByPromotionData empty startDate:{}, endDate:{}, adAccount:{}, resultData:{}", startDate, endDate, adAccountEntity, resultData);
            return;
        }
        GetLocalPromotionReportResult.LocalPromotionReportResult localPromotionReportResult = resultData.getLocalPromotionReportResultList().get(0);
        // 先处理第一页的数据
        // 自己注入自己，注入的是一个新对象，这样在内部通过该对象来调用@Transactional的方法事务才会生效
        // 因为Spring事务是通过代理对象来实现的，内部方法自己调用@Transactional方法Spring无法生成代理对象
        adMarketingManager.handleLocalPromotionReportDataForNewPromotion(adAccountEntity, localPromotionReportResult);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleLocalPromotionReportDataForNewPromotion(AdAccountEntity adAccountEntity, GetLocalPromotionReportResult.LocalPromotionReportResult localPromotionReportResult) {
        Long projectId = localPromotionReportResult.getProjectId();
        Long promotionId = localPromotionReportResult.getPromotionId();
        // 查询项目是否已经存在
        List<HeadlinesCampaignEntity> existedCampaignEntityList = headlinesCampaignDAO.queryCampaignListByCampaignIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), Lists.newArrayList(projectId), TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
        if (CollectionUtils.isEmpty(existedCampaignEntityList)) { // 不存在项目列表说明项目也是当天新创建的
            HeadlinesCampaignEntity campaignEntity = new HeadlinesCampaignEntity();
            campaignEntity.setId(UUIDUtil.getUUID());
            campaignEntity.setEa(adAccountEntity.getEa());
            campaignEntity.setAdAccountId(adAccountEntity.getId());
            campaignEntity.setCampaignId(projectId);
            campaignEntity.setCampaignName(localPromotionReportResult.getProjectName());
            campaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
            campaignEntity.setType(TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
            campaignEntity.setRefreshTime(new Date());
            // 设置默认值防止更新sql报错
            campaignEntity.setBudget(0.0);
            campaignEntity.setBudgetMode(-1);
            campaignEntity.setLandingType(-1);
            campaignEntity.setStatus(-1);
            campaignEntity.setMarketingPurpose(-2);
            campaignEntity.setDeliveryMode(-1);
            headlinesCampaignDAO.batchAddHeadlinesCampaign(Lists.newArrayList(campaignEntity));
            // 只同步单个项目的市场活动
            refreshDataManager.batchSyncCampaignToMarketingEventObj(adAccountEntity.getEa(), adAccountEntity, Lists.newArrayList(campaignEntity), AdSourceEnum.SOURCE_JULIANG.getSource());
        }
        // 查询当前广告列表是否存在，避免在同步来自同一广告列表的线索时重复添加广告列表
        List<HeadlinesAdEntity> existedAdEntityList = headlinesAdDAO.queryAdListByAdIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), Lists.newArrayList(promotionId));
        if (CollectionUtils.isEmpty(existedAdEntityList)) {
            HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
            headlinesAdEntity.setId(UUIDUtil.getUUID());
            headlinesAdEntity.setEa(adAccountEntity.getEa());
            headlinesAdEntity.setAdId(promotionId);
            headlinesAdEntity.setAdName(localPromotionReportResult.getPromotionName());
            headlinesAdEntity.setCampaignId(projectId);
            headlinesAdEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
            headlinesAdEntity.setAdAccountId(adAccountEntity.getId());
            headlinesAdEntity.setType(TypeEnum.HEADLINES_LOCAL_PROMOTION.getCode());
            headlinesAdEntity.setRefreshTime(new Date());
            headlinesAdEntity.setUpdateTime(new Date());
            // 以下默认设置防止批量更新sql报错
            headlinesAdEntity.setStatus(-1);
            headlinesAdEntity.setOptStatus(-1);
            headlinesAdEntity.setDeliveryRange(-1);
            headlinesAdEntity.setInventoryCatalog(-1);
            headlinesAdEntity.setInventoryType(Lists.newArrayList(""));
            headlinesAdDAO.batchAddHeadlinesAd(Lists.newArrayList(headlinesAdEntity));
            // 只同步单个项目的市场活动
            refreshDataManager.batchSyncAdGroupToMarketingEventObj(adAccountEntity.getEa(), adAccountEntity, Lists.newArrayList(headlinesAdEntity), AdSourceEnum.SOURCE_JULIANG.getSource());
        }
    }

    /*
        以下获取项目列表和广告列表的功能暂时注掉，因为目前巨量引擎本地推接口那边不支持获取推广目的为“获取线索”的项目列表和广告列表
        现在的方案是获取广告列表数据的时候来同步项目列表和广告列表
        所以暂时先不用这两个接口来获取列表，但未来可能开放，所以开发的代码先暂时注释掉

        总共注释掉了四个方法：refreshHeadlinesLocalPromotion、handleLocalPromotionData、refreshHeadlinesLocalProject、handleLocalProjectData
    */
//    private void refreshHeadlinesLocalPromotion(AdAccountEntity adAccountEntity) {
//        int page = 1;
//        int pageSize = 10;
//        Long localAccountId = adAccountEntity.getAccountId();
//        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
//        if (StringUtils.isBlank(accessToken)) {
//            log.warn("refreshHeadlinesLocalPromotion accessToken is null ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity);
//            return;
//        }
//        HeadlinesRequestResult<GetHeadlinesLocalPromotionResult> headlinesRequestResult = campaignApiManager.getHeadlinesLocalPromotion(localAccountId, accessToken, page, pageSize);
//        if (headlinesRequestResult == null || !headlinesRequestResult.isSuccess()) {
//            log.warn("refreshHeadlineLocalPromotion fail adAccount:{}, headlinesRequestResult:{}", adAccountEntity, headlinesRequestResult);
//            return;
//        }
//        GetHeadlinesLocalPromotionResult resultData = headlinesRequestResult.getData();
//        if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalPromotionResultList())) {
//            log.info("refreshHeadlineLocalPromotion empty adAccount:{}, resultData:{}", adAccountEntity, resultData);
//            return;
//        }
//        List<GetHeadlinesLocalPromotionResult.HeadlinesLocalPromotionResult> localPromotionList = resultData.getLocalPromotionResultList();
//        // 先处理第一页数据
//        handleLocalPromotionData(adAccountEntity, localPromotionList);
//        PageInfo pageInfo = resultData.getPageInfo();
//        if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
//            return;
//        }
//        log.info("refresh more page headlines local promotion pageInfo:{}", pageInfo);
//        for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
//            accessToken = adTokenManager.getAccessToken(adAccountEntity); // 避免循环过程token过期，因此每次循环都查一次token
//            if (StringUtils.isBlank(accessToken)) {
//                log.warn("refreshHeadlinesLocalPromotion accessToken is null ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity);
//                return;
//            }
//            headlinesRequestResult = campaignApiManager.getHeadlinesLocalPromotion(localAccountId, accessToken, i, pageSize);
//            if (headlinesRequestResult == null || !headlinesRequestResult.isSuccess()) {
//                log.warn("refreshHeadlinesLocalPromotion fail adAccount:{}, headlinesRequestResult:{}", adAccountEntity, headlinesRequestResult);
//                break;
//            }
//            resultData = headlinesRequestResult.getData();
//            if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalPromotionResultList())) {
//                log.info("refreshHeadlineLocalPromotion empty adAccount:{}, resultData:{}", adAccountEntity, resultData);
//                break;
//            }
//            localPromotionList = resultData.getLocalPromotionResultList();
//            handleLocalPromotionData(adAccountEntity, localPromotionList);
//        }
//    }
//
//    private void handleLocalPromotionData(AdAccountEntity adAccountEntity, List<GetHeadlinesLocalPromotionResult.HeadlinesLocalPromotionResult> localPromotionList) {
//        if(CollectionUtils.isEmpty(localPromotionList)) {
//            return;
//        }
//        // 根据广告id查询已存在的广告列表
//        List<Long> promotionIds = localPromotionList.stream().map(GetHeadlinesLocalPromotionResult.HeadlinesLocalPromotionResult::getPromotionId).collect(Collectors.toList());
//        List<HeadlinesAdEntity> exitedHeadlinesAdEntityList = headlinesAdDAO.queryAdListByAdIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), promotionIds);
//        Map<Long, HeadlinesAdEntity> exitedMap = exitedHeadlinesAdEntityList.stream().collect(Collectors.toMap(HeadlinesAdEntity::getAdId, e -> e, (v1, v2) -> v1));
//        List<HeadlinesAdEntity> insertList = Lists.newArrayList();
//        List<HeadlinesAdEntity> updateList = Lists.newArrayList();
//        localPromotionList.forEach(localPromotion -> {
//            HeadlinesAdEntity updateEntity = exitedMap.get(localPromotion.getPromotionId());
//            if (updateEntity != null) { // 更新
//                updateEntity.setAdName(localPromotion.getPromotionName());
//                updateEntity.setRefreshTime(new Date());
//                updateEntity.setUpdateTime(DateUtil.parse(localPromotion.getPromotionModifyTime()));
//                updateList.add(updateEntity);
//            } else { // 插入
//                HeadlinesAdEntity insertEntity = new HeadlinesAdEntity();
//                insertEntity.setId(UUIDUtil.getUUID());
//                insertEntity.setEa(adAccountEntity.getEa());
//                insertEntity.setAdId(localPromotion.getPromotionId());
//                insertEntity.setAdName(localPromotion.getPromotionName());
//                insertEntity.setCampaignId(localPromotion.getProjectId());
//                insertEntity.setStatus(-1);
//                insertEntity.setOptStatus(-1);
//                insertEntity.setDeliveryRange(-1);
//                insertEntity.setInventoryCatalog(0);
//                insertEntity.setInventoryType(Lists.newArrayList(""));
//                insertEntity.setRefreshTime(new Date());
//                insertEntity.setCreateTime(DateUtil.parse(localPromotion.getPromotionCreateTime()));
//                insertEntity.setUpdateTime(DateUtil.parse(localPromotion.getPromotionModifyTime()));
//                insertEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
//                insertEntity.setAdAccountId(adAccountEntity.getId());
//                insertEntity.setType(TypeEnum.HEADLINES_LOCAL_PROMOTION.getCode());
//            }
//        });
//        if (CollectionUtils.isNotEmpty(updateList)) {
//            headlinesAdDAO.batchUpdateAd(adAccountEntity.getEa(), adAccountEntity.getId(), updateList);
//        }
//        if (CollectionUtils.isNotEmpty(insertList)) {
//            headlinesAdDAO.batchAddHeadlinesAd(insertList);
//        }
//    }
//
//    private void refreshHeadlinesLocalProject(AdAccountEntity adAccountEntity) {
//        int page = 1;
//        int pageSize = 10; // 每次页处理10条
//        Long localAccountId = adAccountEntity.getAccountId();
//        String accessToken = adTokenManager.getAccessToken(adAccountEntity);
//        if (StringUtils.isBlank(accessToken)) {
//            log.warn("refreshHeadlinesLocalProject accessToken is null ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity);
//            return;
//        }
//        HeadlinesRequestResult<GetHeadlinesLocalProjectResult> headlineRequestResult = campaignApiManager.getHeadlinesLocalProject(localAccountId, accessToken, page, pageSize);
//        if (headlineRequestResult == null || !headlineRequestResult.isSuccess()) {
//            log.warn("refreshHeadlinesLocalProject fail adAccountEntity:{}, headlineRequestResult:{}", adAccountEntity, headlineRequestResult);
//            return;
//        }
//        GetHeadlinesLocalProjectResult resultData = headlineRequestResult.getData();
//        if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalProjectList())) {
//            log.info("refreshHeadlinesLocalProject empty adAccountEntity:{}, resultData:{}", adAccountEntity, resultData);
//            return;
//        }
//        List<HeadlinesLocalProjectResult> localProjectList = resultData.getLocalProjectList();
//        // 先处理第一页
//        handleLocalProjectData(adAccountEntity, localProjectList);
//        PageInfo pageInfo = resultData.getPageInfo();
//        if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
//            return;
//        }
//        log.info("refresh more pageInfo HeadlinesLocalProject pageInfo:{}", pageInfo);
//        for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
//            accessToken = adTokenManager.getAccessToken(adAccountEntity); // 避免在循环的时候token过期，因此每次循环都查一次token
//            if (StringUtils.isBlank(accessToken)) {
//                log.warn("refreshHeadlinesLocalProject accessToken is null ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity);
//                return;
//            }
//            headlineRequestResult = campaignApiManager.getHeadlinesLocalProject(localAccountId, accessToken, i, pageSize);
//            if (headlineRequestResult == null || !headlineRequestResult.isSuccess()) {
//                log.warn("refreshHeadlinesLocalProject fail adAccount:{}, headlinesRequestResult:{}", adAccountEntity, headlineRequestResult);
//                break;
//            }
//            resultData = headlineRequestResult.getData();
//            if (resultData == null || CollectionUtils.isEmpty(resultData.getLocalProjectList())) {
//                log.info("refreshHeadlinesLocalProject empty adAccount:{}, resultData:{}", adAccountEntity, resultData);
//                break;
//            }
//            localProjectList = resultData.getLocalProjectList();
//            handleLocalProjectData(adAccountEntity, localProjectList);
//        }
//
//    }
//
//    private void handleLocalProjectData(AdAccountEntity adAccountEntity, List<HeadlinesLocalProjectResult> localProjectList) {
//        if (CollectionUtils.isEmpty(localProjectList)) {
//            return;
//        }
//        // 根据项目id查询数据库中已存在的广告项目
//        List<Long> localProjectIds = localProjectList.stream().map(HeadlinesLocalProjectResult::getProjectId).collect(Collectors.toList());
//        List<HeadlinesCampaignEntity> exitedLocalProjects = headlinesCampaignDAO.queryCampaignListByCampaignIds(adAccountEntity.getEa(), adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), localProjectIds, TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
//        Map<Long, HeadlinesCampaignEntity> exitedMap = exitedLocalProjects.stream().collect(Collectors.toMap(HeadlinesCampaignEntity::getCampaignId, e -> e, (v1, v2) -> v1));
//        // 要批量插入的数据
//        List<HeadlinesCampaignEntity> insertList = Lists.newArrayList();
//        // 要批量更新的数据
//        List<HeadlinesCampaignEntity> updateList = Lists.newArrayList();
//        localProjectList.forEach(localProject -> {
//            if (exitedMap.get(localProject.getProjectId()) != null) { // 更新
//                HeadlinesCampaignEntity updateEntity = exitedMap.get(localProject.getProjectId());
//                updateEntity.setCampaignName(localProject.getName());
//                updateEntity.setBudget(localProject.getBudget() == null ? 0D : localProject.getBudget());
//                updateEntity.setBudgetMode(localProject.getBudgetMode() == null ? BudgetModeEnum.DEFAULT.getStatus() : Optional.ofNullable(BudgetModeEnum.getStatusByName(localProject.getBudgetMode())).orElse(BudgetModeEnum.DEFAULT.getStatus()));
//                updateEntity.setLandingType(localProject.getLocalDeliveryScene() != null ? LandingTypeEnum.fromStr(localProject.getLocalDeliveryScene()) : null);
//                updateEntity.setStatus(localProject.getStatus() != null ? HeadlinesCampaignStatusEnum.fromStr(localProject.getStatus()) : null);
//                updateEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.DEFAULT.getStatus());
//                updateEntity.setDeliveryMode(null);
//                updateEntity.setRefreshTime(new Date());
//                updateEntity.setUpdateTime(DateUtil.parse(localProject.getModifyTime()));
//                updateEntity.setAdType(localProject.getAdType());
//                updateList.add(updateEntity);
//            } else { // 插入
//                HeadlinesCampaignEntity insertEntity = new HeadlinesCampaignEntity();
//                insertEntity.setId(UUIDUtil.getUUID());
//                insertEntity.setEa(adAccountEntity.getEa());
//                insertEntity.setCampaignId(localProject.getProjectId());
//                insertEntity.setCampaignName(localProject.getName());
//                insertEntity.setBudget(localProject.getBudget() == null ? 0D : localProject.getBudget());
//                insertEntity.setBudgetMode(localProject.getBudgetMode() == null ? BudgetModeEnum.DEFAULT.getStatus() : Optional.ofNullable(BudgetModeEnum.getStatusByName(localProject.getBudgetMode())).orElse(BudgetModeEnum.DEFAULT.getStatus()));
//                insertEntity.setLandingType(localProject.getLocalDeliveryScene() != null ? LandingTypeEnum.fromStr(localProject.getLocalDeliveryScene()) : null);
//                insertEntity.setStatus(localProject.getStatus() != null ? HeadlinesCampaignStatusEnum.fromStr(localProject.getStatus()) : null);
//                insertEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.DEFAULT.getStatus());
//                insertEntity.setDeliveryMode(null);
//                insertEntity.setRefreshTime(new Date());
//                insertEntity.setCreateTime(DateUtil.parse(localProject.getCreateTime()));
//                insertEntity.setUpdateTime(DateUtil.parse(localProject.getModifyTime()));
//                insertEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
//                insertEntity.setAdAccountId(adAccountEntity.getId());
//                insertEntity.setType(TypeEnum.HEADLINES_LOCAL_PROJECT.getCode());
//                insertEntity.setAdType(localProject.getAdType());
//                insertList.add(insertEntity);
//            }
//        });
//        if (CollectionUtils.isNotEmpty(updateList)) {
//            headlinesCampaignDAO.batchUpdateCampaign(adAccountEntity.getEa(), adAccountEntity.getId(), updateList);
//        }
//        if (CollectionUtils.isNotEmpty(insertList)) {
//            headlinesCampaignDAO.batchAddHeadlinesCampaign(insertList);
//        }
//
//    }

    public void refreshHeadlinePromotionData(AdAccountEntity accountInfo, int day) {
        if (day == -1) {
            String headlineV2BeginTime = "2023-01-01 00:00:00";
            day = DateUtil.getDaysBetween(DateUtil.parse(headlineV2BeginTime).getTime(), System.currentTimeMillis()).intValue();
        }
        Date now = new Date();
        Date beginTime = DateUtil.minusDay(now, day);
        List<String> dimensions = Lists.newArrayList("stat_time_day", "cdp_promotion_id", "cdp_project_id");
        while (beginTime.before(now)) {
            Date endTime = DateUtil.plusDay(beginTime, 30);
            if (endTime.after(now)) {
                endTime = now;
            }
            int pageNum = 1;
            // 头条限制最多100
            int pageSize = 100;
            CustomReportArg customReportArg = buildCustomReportArg(accountInfo, dimensions, beginTime, endTime, pageNum, pageSize);
            String token = adTokenManager.getAccessToken(accountInfo);
            if (StringUtils.isBlank(token)) {
                return;
            }
            HeadlinesRequestResult<CustomReportResult> customReportResult = campaignApiManager.getHeadlinesCustomReport(token, customReportArg);
            if (customReportResult == null || !customReportResult.isSuccess()) {
                log.warn("refreshHeadlinePromotionData fail account: {} arg: {}", accountInfo, customReportArg);
                beginTime = DateUtil.plusDay(endTime, 1);
                continue;
            }
            CustomReportResult reportData = customReportResult.getData();
            handlePromotionReportData(accountInfo, reportData.getRows());

            PageInfo pageInfo = reportData.getPageInfo();
            if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
                beginTime = DateUtil.plusDay(endTime, 1);
                continue;
            }
            log.info("refreshHeadlinePromotionData more one page, adAccountEntity: {} page: {}", accountInfo, pageInfo);
            for (int i = 1; i <= pageInfo.getTotalPage(); i++) {
                customReportArg = buildCustomReportArg(accountInfo, dimensions, beginTime, endTime, i, pageSize);
                token = adTokenManager.getAccessToken(accountInfo);
                if (StringUtils.isBlank(token)) {
                    return;
                }
                customReportResult = campaignApiManager.getHeadlinesCustomReport(token, customReportArg);
                if (customReportResult == null || !customReportResult.isSuccess()) {
                    log.warn("refreshHeadlinePromotionData fail account: {} arg: {}", accountInfo, customReportArg);
                    break;
                }
                reportData = customReportResult.getData();
                handlePromotionReportData(accountInfo, reportData.getRows());
            }
            beginTime = DateUtil.plusDay(endTime, 1);
        }
    }

    public void handlePromotionReportData(AdAccountEntity accountInfo, List<CustomReportResult.RowData> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        String ea = accountInfo.getEa();
        List<HeadlinesAdDataEntity> entityList = Lists.newArrayList();
        for (CustomReportResult.RowData row : rows) {
            Map<String, String> dimensions = row.getDimensions();
            Map<String, String> metrics = row.getMetrics();
            if (MapUtils.isEmpty(dimensions) || MapUtils.isEmpty(metrics)) {
                continue;
            }
            HeadlinesAdDataEntity entity = new HeadlinesAdDataEntity();
            entity.setEa(ea);
            entity.setAdAccountId(accountInfo.getId());
            entity.setId(UUIDUtil.getUUID());
            entity.setType(TypeEnum.HEADLINES_PROMOTION.getCode());
            String promotionId = dimensions.get("cdp_promotion_id");
            entity.setAdId(Long.parseLong(promotionId));
            String projectId = dimensions.get("cdp_project_id");
            entity.setCampaignId(Long.parseLong(projectId));
            String statTime = dimensions.get("stat_time_day");
            entity.setStatDatetime(DateUtil.parse(statTime, "yyyy-MM-dd"));

            String click = metrics.getOrDefault("click_cnt", "0");
            entity.setClick(Long.parseLong(click));
            String show = metrics.getOrDefault("show_cnt", "0");
            entity.setShow(Long.parseLong(show));
            String cost = metrics.getOrDefault("stat_cost", "0.0");
            entity.setCost(Double.parseDouble(cost));
            String cpcPlatform = metrics.getOrDefault("cpc_platform", "0.0");
            entity.setAvgClickCost(Double.parseDouble(cpcPlatform));
            entityList.add(entity);
        }
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        Set<Long> promotionIdSet = Sets.newHashSet();
        Set<String> dateSet = Sets.newHashSet();
        List<Date> dateList = Lists.newArrayList();
        for (HeadlinesAdDataEntity headlinesAdDataEntity : entityList) {
            promotionIdSet.add(headlinesAdDataEntity.getAdId());
            Date actionDate = headlinesAdDataEntity.getStatDatetime();
            String date = DateUtil.format(actionDate, "yyyy-MM-dd");
            if (!dateSet.contains(date)) {
                dateSet.add(date);
                dateList.add(actionDate);
            }
        }
        List<HeadlinesAdDataEntity> existList = headlinesAdDataDAO.queryByAdIdAndStatTimeDate(ea, accountInfo.getId(), Lists.newArrayList(promotionIdSet), dateList, TypeEnum.HEADLINES_PROMOTION.getCode());
        Map<String, HeadlinesAdDataEntity> existMap = Maps.newHashMap();
        for (HeadlinesAdDataEntity existEntity : existList) {
            String key = existEntity.getAdId() + DateUtil.format2(existEntity.getStatDatetime());
            existMap.put(key, existEntity);
        }
        List<HeadlinesAdDataEntity> insertEntityList = Lists.newArrayList();
        for (HeadlinesAdDataEntity entity : entityList) {
            String key = entity.getAdId() + DateUtil.format2(entity.getStatDatetime());
            HeadlinesAdDataEntity exist = existMap.get(key);
            if (exist != null) {
                entity.setId(exist.getId());
                headlinesAdDataDAO.updateHeadlinesAdDataRefreshData(entity);
            } else {
                insertEntityList.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(insertEntityList)) {
            headlinesAdDataDAO.batchInsert(insertEntityList);
        }
        buildAdvertisingDetailsObj(ea, accountInfo.getId(), entityList);
    }

    public void refreshHeadlineProjectData(AdAccountEntity accountInfo, int day) {
        if (day == -1) {
            String headlineV2BeginTime = "2023-01-01 00:00:00";
            day = DateUtil.getDaysBetween(DateUtil.parse(headlineV2BeginTime).getTime(), System.currentTimeMillis()).intValue();
        }
        Date now = new Date();
        Date beginTime = DateUtil.minusDay(now, day);
        List<String> dimensions = Lists.newArrayList("stat_time_day", "cdp_project_id");
        while (beginTime.before(now)) {
            Date endTime = DateUtil.plusDay(beginTime, 30);
            if (endTime.after(now)) {
                endTime = now;
            }
            int pageNum = 1;
            // 头条限制最多100
            int pageSize = 100;
            CustomReportArg customReportArg = buildCustomReportArg(accountInfo, dimensions, beginTime, endTime, pageNum, pageSize);
            HeadlinesRequestResult<CustomReportResult> customReportResult = campaignApiManager.getHeadlinesCustomReport(adTokenManager.getAccessToken(accountInfo), customReportArg);
            if (customReportResult == null || !customReportResult.isSuccess()) {
                log.warn("refreshHeadlineProjectData fail account: {} arg: {}", accountInfo, customReportArg);
                beginTime = DateUtil.plusDay(endTime, 1);
                continue;
            }
            CustomReportResult reportData = customReportResult.getData();
            handleProjectReportData(accountInfo, reportData.getRows());

            PageInfo pageInfo = reportData.getPageInfo();
            if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
                beginTime = DateUtil.plusDay(endTime, 1);
                continue;
            }
            log.info("refreshHeadlineProjectData more one page, adAccountEntity: {} page: {}", accountInfo, pageInfo);
            for (int i = 1; i <= pageInfo.getTotalPage(); i++) {
                customReportArg = buildCustomReportArg(accountInfo, dimensions, beginTime, endTime, i, pageSize);
                customReportResult = campaignApiManager.getHeadlinesCustomReport(adTokenManager.getAccessToken(accountInfo), customReportArg);
                if (customReportResult == null || !customReportResult.isSuccess()) {
                    log.warn("refreshHeadlineProjectData fail account: {} arg: {}", accountInfo, customReportArg);
                    continue;
                }
                reportData = customReportResult.getData();
                handleProjectReportData(accountInfo, reportData.getRows());
            }
            beginTime = DateUtil.plusDay(endTime, 1);
        }
    }

    public void handleProjectReportData(AdAccountEntity accountInfo, List<CustomReportResult.RowData> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        String ea = accountInfo.getEa();
        List<HeadlinesCampaignDataEntity> entityList = Lists.newArrayList();
        for (CustomReportResult.RowData row : rows) {
            Map<String, String> dimensions = row.getDimensions();
            Map<String, String> metrics = row.getMetrics();
            if (MapUtils.isEmpty(dimensions) || MapUtils.isEmpty(metrics)) {
                continue;
            }
            HeadlinesCampaignDataEntity entity = new HeadlinesCampaignDataEntity();
            entity.setEa(ea);
            entity.setAdAccountId(accountInfo.getId());
            entity.setId(UUIDUtil.getUUID());
            entity.setType(TypeEnum.HEADLINES_PROJECT.getCode());

            String projectId = dimensions.get("cdp_project_id");
            entity.setCampaignId(Long.parseLong(projectId));
            String statTime = dimensions.get("stat_time_day");
            entity.setActionDate(DateUtil.parse(statTime, "yyyy-MM-dd"));

            String click = metrics.getOrDefault("click_cnt", "0");
            entity.setClick(Long.parseLong(click));
            String show = metrics.getOrDefault("show_cnt", "0");
            entity.setPv(Long.parseLong(show));
            String cost = metrics.getOrDefault("stat_cost", "0.0");
            entity.setCost(Double.parseDouble(cost));
            String cpcPlatform = metrics.getOrDefault("cpc_platform", "0.0");
            entity.setAvgClickCost(Double.parseDouble(cpcPlatform));
            entityList.add(entity);
        }
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        Set<Long> projectISet = Sets.newHashSet();
        Set<String> dateSet = Sets.newHashSet();
        List<Date> dateList = Lists.newArrayList();
        for (HeadlinesCampaignDataEntity headlinesCampaignDataEntity : entityList) {
            projectISet.add(headlinesCampaignDataEntity.getCampaignId());
            Date actionDate = headlinesCampaignDataEntity.getActionDate();
            String date = DateUtil.format(actionDate, "yyyy-MM-dd");
            if (!dateSet.contains(date)) {
                dateSet.add(date);
                dateList.add(actionDate);
            }
        }
        List<HeadlinesCampaignDataEntity> existList = headlinesCampaignDataDAO.queryByCampaignIdAndActionDate(ea, accountInfo.getId(), Lists.newArrayList(projectISet), dateList, TypeEnum.HEADLINES_PROJECT.getCode());
        Map<String, HeadlinesCampaignDataEntity> existMap = Maps.newHashMap();
        for (HeadlinesCampaignDataEntity existEntity : existList) {
            String key = existEntity.getCampaignId() + DateUtil.format2(existEntity.getActionDate());
            existMap.put(key, existEntity);
        }

        List<HeadlinesCampaignDataEntity> insertList = Lists.newArrayList();
        for (HeadlinesCampaignDataEntity entity : entityList) {
            String key = entity.getCampaignId() + DateUtil.format2(entity.getActionDate());
            HeadlinesCampaignDataEntity existEntity = existMap.get(key);
            if (existEntity != null) {
                headlinesCampaignDataDAO.updateCampaignRefreshData(existEntity.getId(), existEntity.getAdAccountId(),
                        entity.getPv(), entity.getClick(), entity.getCost(), entity.getAvgClickCost(), existEntity.getLeads(), entity.getActionDate());
            } else {
                insertList.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            headlinesCampaignDataDAO.batchInsert(insertList);
        }
    }


    private CustomReportArg buildCustomReportArg(AdAccountEntity accountInfo, List<String> dimensions, Date beginTime, Date endTime, int pageNum, int pageSize) {
        List<String> metrics = Lists.newArrayList("stat_cost", "show_cnt", "click_cnt", "cpc_platform");
        CustomReportArg customReportArg = new CustomReportArg();
        customReportArg.setDimensions(dimensions);
        customReportArg.setMetrics(metrics);
        CustomReportArg.OrderBy orderBy = new CustomReportArg.OrderBy();
        orderBy.setField("stat_cost");
        orderBy.setType("DESC");
        customReportArg.setStartTime(DateUtil.format2(beginTime));
        customReportArg.setEndTime(DateUtil.format2(endTime));
        customReportArg.setPageNum(pageNum);
        customReportArg.setPageSize(pageSize);
        customReportArg.setAdvertiserId(accountInfo.getAccountId());
        customReportArg.setOrderBy(Lists.newArrayList(orderBy));
        return customReportArg;
    }

    public void refreshHeadlinesPromotion(AdAccountEntity adAccountEntity) {
        int pageNum = 1;
        // 这个接口最多每页10条 这似乎少了点吧
        int pageSize = 10;
        // 先处理第一页
        HeadlinesRequestResult<GetHeadlinesPromotionResult> promotionResult = campaignApiManager.getHeadlinesPromotion(adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity), pageNum, pageSize);
        if (promotionResult == null || !promotionResult.isSuccess()) {
            log.warn("refreshHeadlinesPromotion fail, adAccountEntity: {} result: {}", adAccountEntity, promotionResult);
            return;
        }
        GetHeadlinesPromotionResult resultData = promotionResult.getData();
        if (resultData == null || CollectionUtils.isEmpty(resultData.getList())) {
            log.info("refreshHeadlinesPromotion result is empty, adAccountEntity: {} result: {}", adAccountEntity, promotionResult);
            return;
        }
        List<GetHeadlinesPromotionResult.HeadlinesPromotion> promotionList = resultData.getList();
        handlePromotionData(adAccountEntity, promotionList);
        // 处理其他页
        PageInfo pageInfo = resultData.getPageInfo();
        if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
            return;
        }
        log.info("refreshHeadlinesPromotion more one page, adAccountEntity: {} page: {}", adAccountEntity, pageInfo);
        int totalPage = pageInfo.getTotalPage();
        for (int i = 2; i <= totalPage; i++) {
            promotionResult = campaignApiManager.getHeadlinesPromotion(adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity), i, pageSize);
            if (promotionResult == null || !promotionResult.isSuccess()) {
                log.warn("refreshHeadlinesPromotion fail, pageNum: {} adAccountEntity: {} result: {}", i, adAccountEntity, promotionResult);
                break;
            }
            resultData = promotionResult.getData();
            if (resultData == null || CollectionUtils.isEmpty(resultData.getList())) {
                log.info("refreshHeadlinesPromotion result is empty, pageNum: {} adAccountEntity: {} result: {}", i, adAccountEntity, promotionResult);
                break;
            }
            promotionList = resultData.getList();
            handlePromotionData(adAccountEntity, promotionList);
        }
    }

    private void handlePromotionData(AdAccountEntity adAccountEntity, List<GetHeadlinesPromotionResult.HeadlinesPromotion> promotionList) {
        if (CollectionUtils.isEmpty(promotionList)) {
            return;
        }
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        List<Long> promotionIdList = promotionList.stream().map(GetHeadlinesPromotionResult.HeadlinesPromotion::getId).collect(Collectors.toList());
        List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryAdListByAdIds(ea, adAccountId, AdSourceEnum.SOURCE_JULIANG.getSource(), promotionIdList);
        Map<Long, HeadlinesAdEntity> existPromotionMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(headlinesAdEntityList)) {
            headlinesAdEntityList.forEach(e -> existPromotionMap.put(e.getAdId(), e));
        }
        List<HeadlinesAdEntity> addAdList = Lists.newArrayList();
        List<HeadlinesAdEntity> updateAdList = Lists.newArrayList();
        for (GetHeadlinesPromotionResult.HeadlinesPromotion headlinesPromotion : promotionList) {
            if (StringUtils.isBlank(headlinesPromotion.getName())) {
                log.warn("headline is crazy again, adAccount: {} headlinesPromotion: {}", adAccountEntity, headlinesPromotion);
                continue;
            }
            HeadlinesAdEntity existAdEntity = existPromotionMap.get(headlinesPromotion.getId());
            if (existAdEntity != null) {
                existAdEntity.setUpdateTime(DateUtil.parse(headlinesPromotion.getModifyTime()));
                existAdEntity.setAdName(headlinesPromotion.getName());
                existAdEntity.setRefreshTime(new Date());
                Integer optStatus = HeadlinesOptStatusEnum.getStatusByName(headlinesPromotion.getOptStatus());
                existAdEntity.setOptStatus(optStatus == null ? -1 : optStatus);
                Integer status = HeadlinesAdStatusEnum.getStatusByName(headlinesPromotion.getStatus());
                existAdEntity.setStatus(status == null ? -1 : status);
                existAdEntity.setInventoryCatalog(-1);
                existAdEntity.setDeliveryRange(-1);
                existAdEntity.setInventoryType(Lists.newArrayList(""));
                updateAdList.add(existAdEntity);
            } else {
                HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
                headlinesAdEntity.setId(UUIDUtil.getUUID());
                headlinesAdEntity.setEa(ea);
                headlinesAdEntity.setAdAccountId(adAccountId);
                headlinesAdEntity.setAdId(headlinesPromotion.getId());
                headlinesAdEntity.setAdName(headlinesPromotion.getName());
                headlinesAdEntity.setCampaignId(headlinesPromotion.getProjectId());
                headlinesAdEntity.setStatus(HeadlinesAdStatusEnum.getStatusByName(headlinesPromotion.getStatus()));
                headlinesAdEntity.setOptStatus(HeadlinesOptStatusEnum.getStatusByName(headlinesPromotion.getOptStatus()));
                headlinesAdEntity.setCreateTime(DateUtil.parse(headlinesPromotion.getCreateTime()));
                headlinesAdEntity.setUpdateTime(DateUtil.parse(headlinesPromotion.getModifyTime()));
                headlinesAdEntity.setRefreshTime(new Date());
                headlinesAdEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                headlinesAdEntity.setType(TypeEnum.HEADLINES_PROMOTION.getCode());
                headlinesAdEntity.setInventoryType(Lists.newArrayList(""));
                addAdList.add(headlinesAdEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(updateAdList)) {
            headlinesAdDAO.batchUpdateAd(ea, adAccountId, updateAdList);
        }
        if (CollectionUtils.isNotEmpty(addAdList)) {
            headlinesAdDAO.batchAddHeadlinesAd(addAdList);
        }
    }

    public void refreshHeadlinesProject(AdAccountEntity adAccountEntity) {
        int pageNum = 1;
        int pageSize = 10;
        // 先处理第一页
        HeadlinesRequestResult<GetHeadlinesProjectResult> projectResult = campaignApiManager.getHeadlinesProject(adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity), pageNum, pageSize);
        if (projectResult == null || !projectResult.isSuccess()) {
            log.warn("refreshHeadlinesProject fail, adAccountEntity: {} result: {}", adAccountEntity, projectResult);
            return;
        }
        GetHeadlinesProjectResult resultData = projectResult.getData();
        if (resultData == null || CollectionUtils.isEmpty(resultData.getList())) {
            log.info("refreshHeadlinesProject result is empty, adAccountEntity: {} result: {}", adAccountEntity, projectResult);
            return;
        }
        List<HeadlinesProjectResult> projectList = resultData.getList();
        handleProjectData(adAccountEntity, projectList);
        // 处理其他页
        PageInfo pageInfo = resultData.getPageInfo();
        if (pageInfo == null || pageInfo.getTotalPage() == null || pageInfo.getTotalPage() <= 1) {
            return;
        }
        log.info("refreshHeadlinesProject more one page, adAccountEntity: {} page: {}", adAccountEntity, pageInfo);
        int totalPage = pageInfo.getTotalPage();
        for (int i = 2; i <= totalPage; i++) {
            projectResult = campaignApiManager.getHeadlinesProject(adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity), i, pageSize);
            if (projectResult == null || !projectResult.isSuccess()) {
                log.warn("refreshHeadlinesProject fail, pageNum: {} adAccountEntity: {} result: {}", i, adAccountEntity, projectResult);
                break;
            }
            resultData = projectResult.getData();
            if (resultData == null || CollectionUtils.isEmpty(resultData.getList())) {
                log.info("refreshHeadlinesProject result is empty, pageNum: {} adAccountEntity: {} result: {}", i, adAccountEntity, projectResult);
                break;
            }
            projectList = resultData.getList();
            handleProjectData(adAccountEntity, projectList);
        }

    }

    private void handleProjectData(AdAccountEntity adAccountEntity, List<HeadlinesProjectResult> projectList) {
        if (CollectionUtils.isEmpty(projectList)) {
            return;
        }
        List<Long> projectIdList = projectList.stream().map(HeadlinesProjectResult::getProjectId).collect(Collectors.toList());
        ;
        List<HeadlinesCampaignEntity> headlinesCampaignEntityList = headlinesCampaignDAO.queryCampaignListByCampaignIds(adAccountEntity.getEa(),
                adAccountEntity.getId(), AdSourceEnum.SOURCE_JULIANG.getSource(), projectIdList, TypeEnum.HEADLINES_PROJECT.getCode());
        Map<Long, HeadlinesCampaignEntity> existProjectMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(headlinesCampaignEntityList)) {
            headlinesCampaignEntityList.forEach(e -> existProjectMap.put(e.getCampaignId(), e));
        }
        String ea = adAccountEntity.getEa();
        String adAccountId = adAccountEntity.getId();
        // 新增项目
        List<HeadlinesCampaignEntity> addProjectDataList = Lists.newArrayList();
        // 更新项目
        List<HeadlinesCampaignEntity> updateProjectDataList = Lists.newArrayList();
        for (HeadlinesProjectResult projectResult : projectList) {
            Long projectId = projectResult.getProjectId();
            HeadlinesCampaignEntity existProject = existProjectMap.get(projectId);
            if (existProject != null) {
                existProject.setCampaignName(projectResult.getName());
                existProject.setCampaignId(projectResult.getProjectId());
                existProject.setLandingType(LandingTypeEnum.fromStr(projectResult.getLandingType()));
                existProject.setDeliveryMode(DeliveryModeEnum.fromStr(projectResult.getDeliveryMode()));
                existProject.setStatus(HeadlinesCampaignStatusEnum.fromStr(projectResult.getStatus()));
                existProject.setRefreshTime(new Date());
                existProject.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                existProject.setType(TypeEnum.HEADLINES_PROJECT.getCode());
                // 项目没有这个字段 这里直接写-2 要不然sql报错
                existProject.setMarketingPurpose(CampaignMarketingPurposeEnum.DEFAULT.getStatus());
                HeadlinesProjectResult.DeliverySetting deliverySetting = projectResult.getDeliverySetting();
                if (deliverySetting != null) {
                    Integer budgetMode = BudgetModeEnum.getStatusByName(deliverySetting.getBudgetMode());
                    existProject.setBudgetMode(budgetMode == null ? BudgetModeEnum.DEFAULT.getStatus() : budgetMode);
                    existProject.setBudget(deliverySetting.getBudget() == null ? 0D : deliverySetting.getBudget());
                } else {
                    existProject.setBudgetMode(BudgetModeEnum.DEFAULT.getStatus());
                    existProject.setBudget(0D);
                }
                existProject.setAdType(projectResult.getAdType());
                updateProjectDataList.add(existProject);
            } else {
                HeadlinesCampaignEntity headlinesCampaignEntity = new HeadlinesCampaignEntity();
                headlinesCampaignEntity.setId(UUIDUtil.getUUID());
                headlinesCampaignEntity.setEa(ea);
                headlinesCampaignEntity.setAdAccountId(adAccountId);
                headlinesCampaignEntity.setCampaignId(projectResult.getProjectId());
                headlinesCampaignEntity.setCampaignName(projectResult.getName());
                headlinesCampaignEntity.setStatus(HeadlinesCampaignStatusEnum.fromStr(projectResult.getStatus()));
                headlinesCampaignEntity.setLandingType(LandingTypeEnum.fromStr(projectResult.getLandingType()));
                headlinesCampaignEntity.setDeliveryMode(DeliveryModeEnum.fromStr(projectResult.getDeliveryMode()));
                headlinesCampaignEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                headlinesCampaignEntity.setRefreshTime(new Date());
                headlinesCampaignEntity.setUpdateTime(DateUtil.parse(projectResult.getModifyTime()));
                headlinesCampaignEntity.setCreateTime(DateUtil.parse(projectResult.getCreateTime()));
                headlinesCampaignEntity.setType(TypeEnum.HEADLINES_PROJECT.getCode());
                headlinesCampaignEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.DEFAULT.getStatus());
                headlinesCampaignEntity.setAdType(projectResult.getAdType());
                HeadlinesProjectResult.DeliverySetting deliverySetting = projectResult.getDeliverySetting();
                if (deliverySetting != null) {
                    Integer budgetMode = BudgetModeEnum.getStatusByName(deliverySetting.getBudgetMode());
                    headlinesCampaignEntity.setBudgetMode(budgetMode == null ? BudgetModeEnum.DEFAULT.getStatus() : budgetMode);
                    headlinesCampaignEntity.setBudget(deliverySetting.getBudget() == null ? 0D : deliverySetting.getBudget());
                } else {
                    headlinesCampaignEntity.setBudgetMode(BudgetModeEnum.DEFAULT.getStatus());
                    headlinesCampaignEntity.setBudget(0D);
                }
                addProjectDataList.add(headlinesCampaignEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(updateProjectDataList)) {
            headlinesCampaignDAO.batchUpdateCampaign(ea, adAccountId, updateProjectDataList);
        }
        if (CollectionUtils.isNotEmpty(addProjectDataList)) {
            headlinesCampaignDAO.batchAddHeadlinesCampaign(addProjectDataList);
        }
    }


    public void refreshHeadlinesCampaign(AdAccountEntity headlinesAccountEntity, String source) {
        int pageNum = 1;
        int pageSize = 100;
        List<HeadlinesCampaignResult> campaignResultList = Lists.newArrayList();
        try {
            HeadlinesRequestResult<GetHeadlinesCampaignResult> campaignRequestResult = campaignApiManager.getHeadlinesCampaign(headlinesAccountEntity.getAccountId(), adTokenManager.getAccessToken(headlinesAccountEntity), pageNum, pageSize);
            if (campaignRequestResult == null || !campaignRequestResult.isSuccess() || campaignRequestResult.getData() == null || campaignRequestResult.getData().getPageInfo() == null) {
                log.info("refresh headlines campaign fail get campaign null; ea:{}, campaignRequestResult:{}", headlinesAccountEntity.getEa(), campaignRequestResult);
                return;
            }
            campaignResultList.addAll(campaignRequestResult.getData().getList());
            Integer totalPage = campaignRequestResult.getData().getPageInfo().getTotalPage();
            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    pageNum = i;
                    HeadlinesRequestResult<GetHeadlinesCampaignResult> headlinesCampaign = campaignApiManager.getHeadlinesCampaign(headlinesAccountEntity.getAccountId(), adTokenManager.getAccessToken(headlinesAccountEntity), pageNum, pageSize);
                    campaignResultList.addAll(headlinesCampaign.getData().getList());
                }
            }
        } catch (Exception e) {
            log.error("refreshHeadlinesCampaign getHeadlinesCampaign exception headlinesAccountEntity:{} source:{} e:", headlinesAccountEntity, source, e);
            return;
        }
        PageUtil<HeadlinesCampaignResult> page = new PageUtil<>(campaignResultList, 100);
        for (int i = 1; i < page.getPageCount() + 1; i++) {
            List<HeadlinesCampaignResult> currentPage = page.getPagedList(i);
            List<Long> campaignIds = currentPage.stream().map(HeadlinesCampaignResult::getCampaignId).collect(Collectors.toList());
            List<HeadlinesCampaignEntity> headlinesCampaignEntities = headlinesCampaignDAO.queryCampaignListByCampaignIds(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), source, campaignIds, TypeEnum.HEADLINES_CAMPAIGN.getCode());
            Map<Long, HeadlinesCampaignEntity> existCampaignMap = null;
            if (CollectionUtils.isNotEmpty(headlinesCampaignEntities)) {
                existCampaignMap = headlinesCampaignEntities.stream().collect(Collectors.toMap(HeadlinesCampaignEntity::getCampaignId, Function.identity(), (v1, v2) -> v2));
            }
            List<HeadlinesCampaignResult> addCampaignDataList = Lists.newArrayList();  //新增广告推广
            List<HeadlinesCampaignEntity> updateCampaignDataList = Lists.newArrayList(); //更新广告推广
            for (HeadlinesCampaignResult campaignResultData : currentPage) {
                if (existCampaignMap == null || existCampaignMap.get(campaignResultData.getCampaignId()) == null) {
                    addCampaignDataList.add(campaignResultData);
                } else {
                    HeadlinesCampaignEntity updateEntity = existCampaignMap.get(campaignResultData.getCampaignId());
                    updateEntity.setCampaignName(campaignResultData.getCampaignName());
                    updateEntity.setCampaignId(campaignResultData.getCampaignId());
                    updateEntity.setDeliveryMode(DeliveryModeEnum.fromStr(campaignResultData.getDeliveryMode()));
                    updateEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.getStatusByName(campaignResultData.getMarketingPurpose()));
                    updateEntity.setLandingType(LandingTypeEnum.fromStr(campaignResultData.getLandingType()));
                    updateEntity.setDeliveryMode(DeliveryModeEnum.fromStr(campaignResultData.getDeliveryMode()));
                    updateEntity.setStatus(HeadlinesCampaignStatusEnum.fromStr(campaignResultData.getStatus()));
                    updateEntity.setBudget(campaignResultData.getBudget());
                    updateEntity.setRefreshTime(new Date());
                    updateEntity.setSource(source);
                    updateEntity.setType(TypeEnum.HEADLINES_CAMPAIGN.getCode());
                    updateCampaignDataList.add(updateEntity);
                }
            }
            syncAddHeadlinesCampaign(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), addCampaignDataList, source);
            syncUpdateHeadlinesCampaign(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), updateCampaignDataList);
            log.info("refreshHeadlinesCampaign add ea:{} source:{} campaign size:{} update size:{}", headlinesAccountEntity.getEa(), source, addCampaignDataList.size(), updateCampaignDataList.size());
        }
    }


    private void syncAddHeadlinesCampaign(String ea, String adAccountId, List<HeadlinesCampaignResult> addCampaignDataList, String source) {
        if (CollectionUtils.isEmpty(addCampaignDataList)) {
            return;
        }
        List<HeadlinesCampaignEntity> campaignList = Lists.newArrayList();
        addCampaignDataList.forEach(campaignResult -> {
            HeadlinesCampaignEntity headlinesCampaignEntity = new HeadlinesCampaignEntity();
            headlinesCampaignEntity.setId(UUIDUtil.getUUID());
            headlinesCampaignEntity.setEa(ea);
            headlinesCampaignEntity.setAdAccountId(adAccountId);
            headlinesCampaignEntity.setCampaignId(campaignResult.getCampaignId());
            headlinesCampaignEntity.setCampaignName(campaignResult.getCampaignName());
            headlinesCampaignEntity.setStatus(HeadlinesCampaignStatusEnum.fromStr(campaignResult.getStatus()));
            headlinesCampaignEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.getStatusByName(campaignResult.getMarketingPurpose()));
            headlinesCampaignEntity.setLandingType(LandingTypeEnum.fromStr(campaignResult.getLandingType()));
            headlinesCampaignEntity.setDeliveryMode(DeliveryModeEnum.fromStr(campaignResult.getDeliveryMode()));
            headlinesCampaignEntity.setSource(source);
            headlinesCampaignEntity.setBudget(campaignResult.getBudget());
            headlinesCampaignEntity.setBudgetMode(BudgetModeEnum.getStatusByName(campaignResult.getBudgetMode()));
            headlinesCampaignEntity.setRefreshTime(new Date());
            headlinesCampaignEntity.setUpdateTime(DateUtil.parse(campaignResult.getModifyTime()));
            headlinesCampaignEntity.setCreateTime(new Date());
            headlinesCampaignEntity.setType(TypeEnum.HEADLINES_CAMPAIGN.getCode());
            campaignList.add(headlinesCampaignEntity);
        });
        headlinesCampaignDAO.batchAddHeadlinesCampaign(campaignList);
    }


    private void syncUpdateHeadlinesCampaign(String ea, String adAccountId, List<HeadlinesCampaignEntity> updateCampaignDataList) {
        if (CollectionUtils.isEmpty(updateCampaignDataList)) {
            return;
        }
        headlinesCampaignDAO.batchUpdateCampaign(ea, adAccountId, updateCampaignDataList);
    }


    private void refreshHeadlinesAd(AdAccountEntity headlinesAccountEntity, String source) {
        int pageNum = 1;
        int pageSize = 100;
        List<GetHeadlinesAdResult.HeadlinesAd> headlinesAdResultList = Lists.newArrayList();
        HeadlinesRequestResult<GetHeadlinesAdResult> headlinesAdResult = campaignApiManager.getHeadlinesAd(headlinesAccountEntity.getAccountId(), adTokenManager.getAccessToken(headlinesAccountEntity), pageNum, pageSize);
        if (headlinesAdResult == null || !headlinesAdResult.isSuccess() || headlinesAdResult.getData() == null || CollectionUtils.isEmpty(headlinesAdResult.getData().getList()) || headlinesAdResult.getData().getPageInfo() == null) {
            log.info("refresh headlines ad fail get ad null ; ea:{}, headlinesAdResult:{}", headlinesAccountEntity.getEa(), headlinesAdResult);
            return;
        }
        headlinesAdResultList.addAll(headlinesAdResult.getData().getList());
        Integer totalPage = headlinesAdResult.getData().getPageInfo().getTotalPage();
        if (totalPage > 1) {
            for (int i = 2; i <= totalPage; i++) {
                pageNum = i;
                HeadlinesRequestResult<GetHeadlinesAdResult> headlinesAd = campaignApiManager.getHeadlinesAd(headlinesAccountEntity.getAccountId(), adTokenManager.getAccessToken(headlinesAccountEntity), pageNum, pageSize);
                if (headlinesAd != null && headlinesAd.getData() != null && CollectionUtils.isNotEmpty(headlinesAd.getData().getList())) {
                    headlinesAdResultList.addAll(headlinesAd.getData().getList());
                }
            }
        }
        PageUtil<GetHeadlinesAdResult.HeadlinesAd> page = new PageUtil<>(headlinesAdResultList, 100);
        for (int i = 1; i < page.getPageCount() + 1; i++) {
            // 老哥啊，你这代码是跑都不跑一下啊?
            List<GetHeadlinesAdResult.HeadlinesAd> currentPage = page.getPagedList(i);
            List<Long> adIds = currentPage.stream().map(GetHeadlinesAdResult.HeadlinesAd::getAdId).collect(Collectors.toList());
            List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryAdListByAdIds(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), source, adIds);
            Map<Long, HeadlinesAdEntity> existAdMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(headlinesAdEntityList)) {
                headlinesAdEntityList.forEach(e -> existAdMap.put(e.getAdId(), e));
            }
            List<GetHeadlinesAdResult.HeadlinesAd> addAdList = Lists.newArrayList();
            List<HeadlinesAdEntity> updateAdList = Lists.newArrayList();
            for (GetHeadlinesAdResult.HeadlinesAd adResult : currentPage) {
                HeadlinesAdEntity headlinesAdEntity = existAdMap.get(adResult.getAdId());
                if (headlinesAdEntity == null) {
                    addAdList.add(adResult);
                } else {
                    headlinesAdEntity.setId(headlinesAdEntity.getId());
                    headlinesAdEntity.setUpdateTime(DateUtil.parse(adResult.getModifyTime()));
                    headlinesAdEntity.setAdName(adResult.getAdName());
                    headlinesAdEntity.setRefreshTime(new Date());
                    Integer inventory = InventoryCataLogEnum.fromStr(adResult.getInventoryCatalog());
                    headlinesAdEntity.setInventoryCatalog(inventory == null ? -1 : inventory);
                    Integer deliver = DeliveryModeEnum.fromStr(adResult.getDeliveryRange());
                    headlinesAdEntity.setDeliveryRange(deliver == null ? -1 : deliver);
                    Integer optStatus = HeadlinesOptStatusEnum.getStatusByName(adResult.getOptStatus());
                    headlinesAdEntity.setOptStatus(optStatus == null ? -1 : optStatus);
                    Integer status = HeadlinesAdStatusEnum.getStatusByName(adResult.getStatus());
                    headlinesAdEntity.setStatus(status == null ? -1 : status);
                    headlinesAdEntity.setInventoryType(CollectionUtils.isEmpty(adResult.getInventoryType()) ? Lists.newArrayList("") : adResult.getInventoryType());
                    updateAdList.add(headlinesAdEntity);
                }
            }
            syncAddHeadlinesAd(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), addAdList, source);
            syncUpdateHeadlinesAd(headlinesAccountEntity.getEa(), headlinesAccountEntity.getId(), updateAdList, source);
        }
    }


    private void syncAddHeadlinesAd(String ea, String adAccountId, List<GetHeadlinesAdResult.HeadlinesAd> addAdList, String source) {
        if (CollectionUtils.isEmpty(addAdList)) {
            return;
        }
        List<HeadlinesAdEntity> headlinesAdEntityList = Lists.newArrayList();
        addAdList.forEach(ad -> {
            HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
            headlinesAdEntity.setId(UUIDUtil.getUUID());
            headlinesAdEntity.setEa(ea);
            headlinesAdEntity.setAdAccountId(adAccountId);
            headlinesAdEntity.setAdId(ad.getAdId());
            headlinesAdEntity.setAdName(ad.getAdName());
            headlinesAdEntity.setCampaignId(ad.getCampaignId());
            headlinesAdEntity.setStatus(HeadlinesAdStatusEnum.getStatusByName(ad.getStatus()));
            headlinesAdEntity.setOptStatus(HeadlinesOptStatusEnum.getStatusByName(ad.getOptStatus()));
            headlinesAdEntity.setDeliveryRange(HeadlinesDeliveryRangeEnum.fromStr(ad.getDeliveryRange()));
            headlinesAdEntity.setInventoryCatalog(InventoryCataLogEnum.fromStr(ad.getInventoryCatalog()));
            headlinesAdEntity.setInventoryType(CollectionUtils.isEmpty(ad.getInventoryType()) ? Lists.newArrayList("") : ad.getInventoryType());
            headlinesAdEntity.setCreateTime(new Date());
            headlinesAdEntity.setUpdateTime(DateUtil.parse(ad.getModifyTime()));
            headlinesAdEntity.setRefreshTime(new Date());
            headlinesAdEntity.setSource(source);
            headlinesAdEntity.setType(TypeEnum.HEADLINES_AD_PLAN.getCode());
            headlinesAdEntityList.add(headlinesAdEntity);
        });
        headlinesAdDAO.batchAddHeadlinesAd(headlinesAdEntityList);
    }


    private void syncUpdateHeadlinesAd(String ea, String adAccountId, List<HeadlinesAdEntity> updateAdList, String source) {
        if (CollectionUtils.isEmpty(updateAdList)) {
            return;
        }
        headlinesAdDAO.batchUpdateAd(ea, adAccountId, updateAdList);
    }


    public void refreshHeadlinesKeyWord(AdAccountEntity adAccountEntity, String source) {
        if (adAccountEntity == null) {
            return;
        }
        int totalCount = headlinesAdDAO.getRefreshAdTotalCount(adAccountEntity.getEa(), adAccountEntity.getId(), source);
        if (totalCount == 0) {
            return;
        }
        int pageSize = AdKeywordManager.MAX_AD_ID_NUMS;
        int totalPageCount = totalCount / pageSize;
        if (totalCount % pageSize != 0) {
            totalPageCount++;
        }
        String ea = adAccountEntity.getEa();
        for (int i = 1; i < totalPageCount + 1; i++) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, AdKeywordManager.MAX_AD_ID_NUMS);
            List<Long> adIds = headlinesAdDAO.pageRefreshAdIds(ea, adAccountEntity.getId(), source, page);
            List<HeadlinesKeywordResultData.HeadlinesKeyword> adKeywordDataList = adKeywordManager.getHeadlinesKeywordResultDataList(adIds, adAccountEntity.getAccountId(), adTokenManager.getAccessToken(adAccountEntity));
            syncKeywordByAdId(adAccountEntity.getEa(), adAccountEntity.getId(), adKeywordDataList);
        }
    }


    private void syncKeywordByAdId(String ea, String adAccountId, List<HeadlinesKeywordResultData.HeadlinesKeyword> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        List<HeadlinesKeywordResultData.HeadlinesKeyword> addKeywordList = Lists.newArrayList();
        List<AdKeywordEntity> updateKeywordList = Lists.newArrayList();
        PageUtil pageUtil = new PageUtil(data, 500);

        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<HeadlinesKeywordResultData.HeadlinesKeyword> currentPageData = pageUtil.getPagedList(i);
            List<Long> keywordIds = currentPageData.stream().map(HeadlinesKeywordResultData.HeadlinesKeyword::getKeywordId).collect(Collectors.toList());
            List<AdKeywordEntity> keywordEntityList = adKeywordDAO.queryAdKeywordByIds(ea, adAccountId, keywordIds);
            if (CollectionUtils.isEmpty(keywordEntityList)) {
                addKeywordList.addAll(currentPageData);
            } else {
                Map<Long, AdKeywordEntity> existKeywordMap = keywordEntityList.stream().collect(Collectors.toMap(AdKeywordEntity::getKeywordId, Function.identity(), (k1, k2) -> k1));
                currentPageData.forEach(keywordData -> {
                    if (existKeywordMap.get(keywordData.getKeywordId()) == null) {
                        addKeywordList.add(keywordData);
                    } else {
                        AdKeywordEntity entity = existKeywordMap.get(keywordData.getKeywordId());
                        entity.setKeyword(keywordData.getWord());
                        entity.setStatus(keywordData.getStatus());
                        updateKeywordList.add(entity);
                    }
                });
            }
        }

        batchAddHeadlinesKeyword(ea, adAccountId, addKeywordList);
        batchUpdateHeadlinesKeyword(ea, adAccountId, updateKeywordList);
    }


    private void batchAddHeadlinesKeyword(String ea, String adAccountId, List<HeadlinesKeywordResultData.HeadlinesKeyword> addKeywordList) {
        if (CollectionUtils.isEmpty(addKeywordList)) {
            return;
        }
        PageUtil pageUtil = new PageUtil(addKeywordList, 500);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<HeadlinesKeywordResultData.HeadlinesKeyword> addData = pageUtil.getPagedList(i);
            List<AdKeywordEntity> addKeywordEntityList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(addData)) {
                addData.forEach(headlinesKeywordResultData -> {
                    AdKeywordEntity entity = new AdKeywordEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    entity.setAdAccountId(adAccountId);
                    entity.setAdId(headlinesKeywordResultData.getAdId());
                    entity.setKeywordId(headlinesKeywordResultData.getKeywordId());
                    entity.setKeyword(headlinesKeywordResultData.getWord());
                    entity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                    entity.setStatus(headlinesKeywordResultData.getStatus());
                    addKeywordEntityList.add(entity);
                });
            }
            if (CollectionUtils.isNotEmpty(addKeywordEntityList)) {
                List<AdKeywordEntity> insertList = addKeywordEntityList.stream()
                        .filter(e -> (e.getCampaignId() != null && e.getAdgroupId() != null && e.getAdId() == null) || e.getCampaignId() == null && e.getAdgroupId() == null && e.getAdId() != null)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(insertList)) {
                    adKeywordDAO.batchInsertHeadlinesKeyword(insertList);
                }
            }
        }
    }


    private void batchUpdateHeadlinesKeyword(String ea, String adAccountId, List<AdKeywordEntity> updateKeywordList) {
        if (CollectionUtils.isEmpty(updateKeywordList)) {
            return;
        }
        Map<String, AdKeywordEntity> map = updateKeywordList.stream().collect(Collectors.toMap(AdKeywordEntity::getId, e -> e, (v1, v2) -> v1));
        updateKeywordList = Lists.newArrayList(map.values());
        PageUtil pageUtil = new PageUtil(updateKeywordList, 100);
        for (int i = 1; i < pageUtil.getPageCount(); i++) {
            List<AdKeywordEntity> currentList = pageUtil.getPagedList(i);
            if (CollectionUtils.isNotEmpty(currentList)) {
                log.info("batchUpdateHeadlinesKeyword arg: {}", JSONUtils.toJSONString(currentList));
                adKeywordDAO.batchUpdate(currentList, ea, adAccountId);
            }
        }
    }


    public void syncHeadlinesCampaignToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, String source) {
        int campaignTotalCount = headlinesCampaignDAO.queryCampaignTotalCount(ea, adAccountEntity.getId(), source);
        if (campaignTotalCount == 0) {
            return;
        }
        int pageSize = 100;
        int totalPageCount = campaignTotalCount / pageSize;
        if (campaignTotalCount % pageSize != 0) {
            totalPageCount++;
        }
        for (int i = 0; i < totalPageCount; i++) {
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, pageSize);
            List<HeadlinesCampaignEntity> headlinesCampaignEntities = headlinesCampaignDAO.pageCampaignEntityData(ea, adAccountEntity.getId(), source, page);
            List<AdCampaignEntity> adCampaignEntities = Lists.newArrayList();
            adCampaignEntities.addAll(headlinesCampaignEntities);
            refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntities, source);
        }

    }


    public void syncHeadlinesAdToSubMarketingEventObj(String ea, AdAccountEntity adAccountEntity, String source) {
        String adAccountId = adAccountEntity.getId();
        int adTotalCount = headlinesAdDAO.queryAdTotalCount(ea, adAccountId, source);
        if (adTotalCount == 0) {
            return;
        }
        int pageSize = 100;
        int totalPageCount = adTotalCount / pageSize;
        if (adTotalCount % pageSize != 0) {
            totalPageCount++;
        }
        for (int i = 0; i < totalPageCount; i++) {
            int currentPage = i + 1;
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(currentPage, pageSize);
            List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.pageAdEntityData(ea, adAccountId, source, page);
            List<AdvertiserAdEntity> adEntityList = Lists.newArrayList();
            adEntityList.addAll(headlinesAdEntityList);
            refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, adEntityList, source);
        }
    }

    public void syncClueDataToClueObj(String ea, String adAccountId, String source) {
        List<AdLeadsMappingDataEntity> leadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(ea, source);
        if (CollectionUtils.isEmpty(leadsMappingDataEntities)) {
            return;
        }
        Integer page = 1;
        Integer pageSize = 100;
        String syncStartTime = START_TIME;
        if (org.apache.commons.lang.StringUtils.equals(ea, WEILAI)) {
            syncStartTime = WEILAI_START_TIME;
        }
        try {
            AdAccountEntity accountEntity = adAccountManager.queryEnableAccountById(adAccountId);
            HeadlinesRequestResult<GetHeadlinesClueResult> clueRequestResult = campaignApiManager.getHeadlinesClueData(accountEntity.getAccountId(), adTokenManager.getAccessToken(accountEntity), page, pageSize, syncStartTime, DateUtil.format("yyyy-MM-dd", new Date()));
            if (clueRequestResult.isSuccess() && clueRequestResult.getData().getPageInfo() != null) {
                PageInfo pageInfo = clueRequestResult.getData().getPageInfo();
                for (page = 1; page <= pageInfo.getTotalPage(); page++) {
                    clueRequestResult = campaignApiManager.getHeadlinesClueData(accountEntity.getAccountId(), adTokenManager.getAccessToken(accountEntity), page, pageSize, syncStartTime, DateUtil.format("yyyy-MM-dd", new Date()));
                    syncClueData(clueRequestResult, ea, adAccountId, source, leadsMappingDataEntities);
                }
            }
        } catch (Exception e) {
            log.error("RefreshDataManager.syncClueDataToClueObj error ea: {} adAccountId: {} source: {}", ea, adAccountId, source, e);
        }
    }


    private void syncClueData(HeadlinesRequestResult<GetHeadlinesClueResult> clueRequestResult, String ea, String adAccountId, String source, List<AdLeadsMappingDataEntity> leadsMappingDataEntities) {
        String leadId = null;
        Boolean isDuplicateSearch = false;
        ActionAddArg actionAddArg = new ActionAddArg();
        if (clueRequestResult.isSuccess()) {
            List<GetHeadlinesClueResult.HeadlinesClue> headlinesClueDataList = clueRequestResult.getData().getList();
            if (CollectionUtils.isEmpty(headlinesClueDataList)) {
                return;
            }

            Map<Long, HeadlinesAdEntity> adEntityMap = new HashMap<>();
            String customFuncApiName = leadsMappingDataEntities.get(0).getCustomFuncApiName();
            HeadlinesAdEntity adEntity = null;
            for (GetHeadlinesClueResult.HeadlinesClue clueResult : headlinesClueDataList) {
                // 线索查重
                AdLeadsEntity entity = adLeadsDAO.queryLeadsByEaAndSourceLeadIdAndSource(ea, clueResult.getClueId(), source);
                if (entity != null && StringUtils.isNotEmpty(entity.getLeadId())) {
                    continue;
                }

                if (adEntityMap.get(Long.valueOf(clueResult.getAdId())) == null) {
                    adEntity = headlinesAdDAO.queryAdByAdId(ea, adAccountId, Long.valueOf(clueResult.getAdId()));
                    if (adEntity == null) {
                        log.info("syncClueDataToClueObj 该线索未关联广告计划 clueResult:{}", clueResult);
                        continue;
                    }
                    adEntityMap.put(Long.valueOf(clueResult.getAdId()), adEntity);
                } else {
                    adEntity = adEntityMap.get(Long.valueOf(clueResult.getAdId()));
                }

                Map<String, Object> param = new HashMap<>();
                if (leadsMappingDataEntities.get(0).getCrmPoolId() != null) {
                    param.put("leads_pool_id", leadsMappingDataEntities.get(0).getCrmPoolId());
                }
                if (org.apache.commons.lang.StringUtils.isNotEmpty(leadsMappingDataEntities.get(0).getCrmRecordType())) {
                    param.put("record_type", leadsMappingDataEntities.get(0).getCrmRecordType());
                }
                param.put("marketing_event_id", adEntity.getSubMarketingEventId());
                if (clueResult.getCreateTimeDetail() == null) {
                    param.put(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), System.currentTimeMillis());
                } else {
                    param.put(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), DateUtil.parse(clueResult.getCreateTimeDetail(), "yyyy-MM-dd HH:mm:ss").getTime());
                }

                param.put(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), source);
                param.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
                param.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());
                //设置线索来源渠道promotion_channel
                param.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("广告"));
                List<String> createByList = Lists.newArrayList();
                Integer createBy = clueDefaultSettingService.getClueCreator(adEntity.getSubMarketingEventId(), ea, ClueDefaultSettingTypeEnum.AD.getType());
                createByList.add(String.valueOf(createBy));
                param.put("created_by", createByList);
                param.put("status", "1");

                if (org.apache.commons.lang.StringUtils.isNotEmpty(customFuncApiName)) {
                    try {
                        Map<String, Object> customFuncApiParamMap = objectToMap(clueResult);
                        Map<String, Object> objectMap = syncLeadCallFunc(ea, ObjectData.convert(customFuncApiParamMap), customFuncApiName);
                        Object object = objectMap.get("functionResult");
                        if (object instanceof Map) {
                            param.putAll(ObjectData.convert((Map) object));
                        } else {
                            log.warn("syncLeadCallFunc result error ea:{} source:{} clueResult:{} e:", ea, customFuncApiParamMap, clueResult);
                        }
                    } catch (IllegalAccessException e) {
                        log.error("syncClueData objectToMap IllegalAccessException error ea:{} source:{} clueResult:{} e:", ea, source, clueResult, e);
                    } catch (Exception e) {
                        log.error("syncClueData objectToMap error ea:{} source:{} clueResult:{} e:", ea, source, clueResult, e);
                    }

                }
                ObjectData objectData = ObjectData.convert(param);
                actionAddArg.setObjectData(objectData);
                ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
                optionInfo.setIsDuplicateSearch(true);
                optionInfo.setCalculateDefaultValue(true);
                actionAddArg.setOptionInfo(optionInfo);

                CreateLeadResult createLeadResult = new CreateLeadResult();
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(ea, -10000), LeadsFieldContants.API_NAME, false, actionAddArg);
                if (result != null && result.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
                    leadId = (String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName());
                    // 插入线索去重表
                    AdLeadsEntity adLeadsEntity = new AdLeadsEntity();
                    adLeadsEntity.setId(UUIDUtil.getUUID());
                    adLeadsEntity.setEa(ea);
                    adLeadsEntity.setAdAccountId(adAccountId);
                    adLeadsEntity.setLeadId(leadId);
                    adLeadsEntity.setSource(source);
                    adLeadsEntity.setCreateTime(new Date());
                    adLeadsEntity.setSource_lead_id(clueResult.getClueId());
                    adLeadsDAO.addAdLeads(adLeadsEntity);
                    log.info("syncHeadlinesLeadData createLead success data:{} leadId:{}", actionAddArg, leadId);
                } else  if (result != null && result.getCode() == CrmConstants.REPEAT_CODE) {
                    log.info("syncHeadlinesLeadData failed duplicate lead data:{}", headlinesClueDataList);
                    continue;
                } else {
                    createLeadResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
                    log.info("syncHeadlinesLeadData createLead failed data:{} message:{}", actionAddArg, result.getMessage());
                    continue;
                }

                //生成活动成员
                if (org.apache.commons.lang.StringUtils.isNotEmpty(leadId)) {
                    syncCampaignMember(ea, adAccountId, leadId, source, adEntity.getAdName(), null, isDuplicateSearch);
                }
            }
        }
    }


    public void syncCampaignMember(String ea, String adAccountId, String leadId, String source, String campaignName, String keyword,
                                   boolean isDuplicateSearch) {
        Optional<String> optional = syncLeadROIFieldAndGetMarketingEventId(ea, adAccountId, leadId, source, campaignName, keyword, isDuplicateSearch);
        if (!optional.isPresent()) {
            return;
        }

        String marketingEventId = optional.get();
        Optional<CampaignMergeDataEntity> optionalMergeDataEntity = buildCampaignMemberObjData(ea, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
        if (!optionalMergeDataEntity.isPresent()) {
            return;
        }

        CampaignMergeDataEntity campaignMergeDataEntity = optionalMergeDataEntity.get();
        Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
        String campaignMemberId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
        if (campaignMemberId != null) {
            campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberId);
            campaignMergeDataManager.addCampaignDataOnlyUnLock(optionalMergeDataEntity.get());
        }
    }


    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity
            campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error apiName: {} objectId: {}", campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId(), e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }


    private Optional<String> syncLeadROIFieldAndGetMarketingEventId(String ea, String adAccountId, String leadId, String source, String campaignName, String keyword, Boolean allowDuplicate) {
        log.info("syncLeadROIField ea:{} leadId:{}, source:{}, campaignName:{}, keyword:{}", ea, leadId, source, campaignName, keyword);
        if (org.apache.commons.lang.StringUtils.isEmpty(campaignName)) {
            return Optional.empty();
        }

        if (!org.apache.commons.lang.StringUtils.equals(source, AdSourceEnum.SOURCE_BAIDU.getSource()) && !org.apache.commons.lang.StringUtils.equals(source, AdSourceEnum.SOURCE_JULIANG.getSource())) {
            return Optional.empty();
        }
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            return Optional.empty();
        }
        AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());

        //同步市场活动
        Optional<String> marketingEventIdOpt = utmDataManger.utmCreateMarketingEvent(ea, campaignName, source, mappingEntity, true);
        if (!marketingEventIdOpt.isPresent()) {
            log.info("syncLeadROIField create marketingEvent failed ea:{} leadId:{} campaignName:{} keyword:{}", ea, leadId, campaignName, keyword);
            return Optional.empty();
        }
        String marketingEventId = marketingEventIdOpt.get();

        //同步关键词
        Optional<String> marketingKeywordOpt = utmDataManger.utmCreateMarketingKeyword(ea, keyword, true);
        if (!marketingKeywordOpt.isPresent()) {
            log.info("syncLeadROIField create marketingEventKeyword failed ea:{} leadId:{} campaignName:{} keyword:{}", ea, leadId, campaignName, keyword);
            return Optional.of(marketingEventId);
        }
        String marketingKeywordId = marketingKeywordOpt.get();

        //同步关键词计划
        Optional<String> marketingKeywordPlanOpt = utmDataManger.utmCreateMarketingKeywordPlan(ea, marketingEventId, campaignName, marketingKeywordId, keyword, true, false, null);
        String marketingKeywordPlanId = null;
        if (marketingKeywordPlanOpt.isPresent()) {
            marketingKeywordPlanId = marketingKeywordPlanOpt.get();
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(marketingEventId) && org.apache.commons.lang.StringUtils.isNotEmpty(marketingKeywordId) && org.apache.commons.lang.StringUtils.isNotEmpty(marketingKeywordPlanId)) {
            //更新线索ROI
            utmDataManger.updateLeadROIData(ea, -10000, leadId, marketingKeywordId, marketingKeywordPlanId, marketingEventId, allowDuplicate, campaignName, keyword);
        } else {
            //更新线索市场活动
            ActionEditArg actionEditArg = new ActionEditArg();
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectMap.put("object_describe_id", CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectMap.put("_id", leadId);
            objectMap.put("marketing_event_id", marketingEventId);
            if (StringUtils.isNotBlank(keyword)) {
                objectMap.put("utm_term__c", keyword);
            }
            if (StringUtils.isNotBlank(campaignName)) {
                objectMap.put("utm_campaign__c", campaignName);
            }
            int ei = eieaConverter.enterpriseAccountToId(ea);
            objectMap.put("tenant_id", ei);
            actionEditArg.setObjectData(ObjectData.convert(objectMap));
            if (allowDuplicate != null) {
                ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
                optionInfo.setIsDuplicateSearch(allowDuplicate);
                optionInfo.setCalculateDefaultValue(true);
                actionEditArg.setOptionInfo(optionInfo);
            }
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.CRM_LEAD.getName(), true, true, actionEditArg);
            if (!editResult.isSuccess()) {
                return Optional.empty();
            }
        }
        return Optional.of(marketingEventId);
    }


    // 构建活动成员对象
    private Optional<CampaignMergeDataEntity> buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return Optional.empty();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error apiName: {} objectId: {}", campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId, e);
        }
        if (objectData == null) {
            return Optional.empty();
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = campaignMergeDataManager.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.AD_SYNC.getType());

        return Optional.of(campaignMergeDataEntity);
    }


    protected HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<String, Object>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            map.put(fieldName, value);
        }
        return map;
    }


    public Map<String, Object> syncLeadCallFunc(String ea, ObjectData objectData, String funcApiName) {
        ExecuteCustomizeFunctionArg executeCustomizeFunctionArg = new ExecuteCustomizeFunctionArg();
        List<ExecuteCustomizeFunctionArg.Parameters> parametersList = Lists.newArrayList();
        executeCustomizeFunctionArg.setApiName(funcApiName);
        executeCustomizeFunctionArg.setParameters(parametersList);
        ExecuteCustomizeFunctionArg.Parameters parameters = new ExecuteCustomizeFunctionArg.Parameters();
        parameters.setName(CustomizeFunctionConstants.ENROLL_DATA_NAME);
        parameters.setType(CustomizeFunctionConstants.PARAMETERS_TYPE.get("Map"));
        parameters.setValue(objectData);
        parametersList.add(parameters);

        Object result = customizeFunctionManager.executeCustomizeFunction(GsonUtil.getGson().toJson(executeCustomizeFunctionArg), ea, -10000);
        if (result == null) {
            log.warn("CustomizeFormDataServiceImpl.executeEnrollCustomizeFunction result is null arg:{}", executeCustomizeFunctionArg);
            return null;
        }
        Map<String, Object> resultMap = GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(result), new TypeToken<Map>() {
        }.getType());

        return resultMap;
    }


    public boolean refreshHeadlinesCampaignData(AdAccountEntity accountInfo, String startTime, String endTime) {
        int pageNum = 1;
        int pageSize = 100;
        List<GetHeadlinesCampaignDataResponse.HeadlinesCampaignData> headlinesCampaignDataResultList = Lists.newArrayList();
        try {
            HeadlinesRequestResult<GetHeadlinesCampaignDataResponse> headlinesCampaignDataResult = campaignApiManager.getHeadlinesCampaignData(accountInfo, startTime, endTime, pageNum, pageSize, adTokenManager.getAccessToken(accountInfo));
            if (headlinesCampaignDataResult == null || !headlinesCampaignDataResult.isSuccess() || headlinesCampaignDataResult.getData() == null || CollectionUtils.isEmpty(headlinesCampaignDataResult.getData().getList()) || headlinesCampaignDataResult.getData().getPageInfo() == null) {
                log.info("HeadlinesAdMarketingManager refreshCampaignData accountInfo :{}, headlinesCampaignDataResult:{}", accountInfo, headlinesCampaignDataResult);
                return false;
            }
            headlinesCampaignDataResultList.addAll(headlinesCampaignDataResult.getData().getList());
            PageInfo pageInfo = headlinesCampaignDataResult.getData().getPageInfo();
            // 单页100条 总数大于100条查多次
            if (pageInfo.getTotalPage() > 1) {
                for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
                    pageNum = i;
                    HeadlinesRequestResult<GetHeadlinesCampaignDataResponse> headlinesCampaignData = campaignApiManager.getHeadlinesCampaignData(accountInfo, startTime, endTime, pageNum, pageSize, adTokenManager.getAccessToken(accountInfo));
                    headlinesCampaignDataResultList.addAll(headlinesCampaignData.getData().getList());
                }
            }
            List<CampaignInitDateUtil.DataUnit> dataUnits = Lists.newArrayList();
            headlinesCampaignDataResultList.forEach(campaignDataResult -> {
                try {
                    CampaignInitDateUtil.DataUnit dataUnit = new CampaignInitDateUtil.DataUnit(campaignDataResult.getCampaignId(), DateUtil.parse(campaignDataResult.getStatDatetime()));
                    dataUnit.setPv(campaignDataResult.getShow());
                    dataUnit.setClick(campaignDataResult.getClick());
                    dataUnit.setCost(campaignDataResult.getCost());
                    dataUnit.setAvgClickCost(campaignDataResult.getAvgClickCost());
                    dataUnit.setActionDate(StringUtils.isEmpty(campaignDataResult.getStatDatetime()) ? new Date() : DateUtil.parse(campaignDataResult.getStatDatetime()));
                    dataUnits.add(dataUnit);
                } catch (Exception e) {
                    log.error("HeadlinesAdMarketingManager refreshCampaignData accountInfo: {}, realTimeResult :{}, exception:", accountInfo, campaignDataResult, e);
                }
            });
            saveHeadlinesCampaignData(accountInfo, dataUnits);
            return true;
        } catch (Exception e) {
            log.error("HeadlinesAdMarketingManager refreshCampaignData accountInfo :{}", accountInfo, e);
        }
        return false;
    }


    private void saveHeadlinesCampaignData(AdAccountEntity accountInfo, List<CampaignInitDateUtil.DataUnit> dataUnits) {
        if (dataUnits.isEmpty()) {
            return;
        }
        dataUnits.forEach(dataUnit -> {
            try {
                HeadlinesCampaignDataEntity dataEntity = headlinesCampaignDataDAO.queryCampaignDataByDate(accountInfo.getEa(), accountInfo.getId(), dataUnit.getCampaignId(), dataUnit.getActionDate());
                if (dataEntity == null) {
                    headlinesCampaignDataDAO.insertCampaignDataIgnore(UUIDUtil.getUUID(), accountInfo.getEa(), dataUnit.getCampaignId(), dataUnit.getActionDate(), accountInfo.getId(), TypeEnum.HEADLINES_PROJECT.getCode());
                    dataEntity = headlinesCampaignDataDAO.queryCampaignDataByDate(accountInfo.getEa(), accountInfo.getId(), dataUnit.getCampaignId(), dataUnit.getActionDate());
                }
                headlinesCampaignDataDAO.updateCampaignRefreshData(dataEntity.getId(), accountInfo.getId(), dataUnit.getPv(), dataUnit.getClick(), dataUnit.getCost(), dataUnit.getAvgClickCost(), dataUnit.getLeads(), dataUnit.getActionDate());
            } catch (Exception e) {
                log.error("HeadlinesAdMarketingManager saveHeadlinesCampaignData failed, dataUnit={}, exception:{}", dataUnit, e.fillInStackTrace());
            }
        });
    }

    public boolean refreshHeadlinesAd(AdAccountEntity accountInfo, int day) {
        int pageNum = 1;
        int pageSize = 100;
        List<GetHeadlinesAdResult.HeadlinesAd> headlinesAdResultList = Lists.newArrayList();
        try {
            HeadlinesRequestResult<GetHeadlinesAdResult> requestResult = campaignApiManager.getHeadlinesAd(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), pageNum, pageSize);
            if (requestResult == null || !requestResult.isSuccess() || requestResult.getData() == null || requestResult.getData().getPageInfo() == null || CollectionUtils.isEmpty(requestResult.getData().getList())) {
                log.info("HeadlinesAdMarketingManager refreshHeadlinesAd campaignApiManager requestResult: {}", requestResult);
                return false;
            }
            log.info("HeadlinesAdMarketingManager refreshCampaignAd requestResult: {}", requestResult);
            headlinesAdResultList.addAll(requestResult.getData().getList());

            if (requestResult.getPageInfo() != null && requestResult.getPageInfo().getTotalPage() > 1) {
                for (int i = 2; i <= requestResult.getPageInfo().getTotalPage(); i++) {
                    pageNum = i;
                    HeadlinesRequestResult<GetHeadlinesAdResult> headlinesAdResult = campaignApiManager.getHeadlinesAd(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), pageNum, pageSize);
                    if (headlinesAdResult == null || !headlinesAdResult.isSuccess() || headlinesAdResult.getData() == null || headlinesAdResult.getData().getPageInfo() == null || CollectionUtils.isEmpty(headlinesAdResult.getData().getList())) {
                        log.info("HeadlinesAdMarketingManager refreshHeadlinesAd campaignApiManager requestResult: {}", requestResult);
                        break;
                    }
                    headlinesAdResultList.addAll(headlinesAdResult.getData().getList());
                }
            }
            headlinesAdResultList.forEach(headlinesAdResultData -> {
                try {
                    HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.queryAdByAdId(accountInfo.getEa(), accountInfo.getId(), headlinesAdResultData.getAdId());

                    Date updateTime = null;
                    if (headlinesAdResultData.getModifyTime() != null) {
                        updateTime = DateUtil.parse(headlinesAdResultData.getModifyTime(), "yyyy-MM-dd");
                    }
                    Date createTime = null;
                    if (headlinesAdResultData.getCreateTime() != null) {
                        createTime = DateUtil.parse(headlinesAdResultData.getCreateTime(), "yyyy-MM-dd");
                    }
                    if (headlinesAdEntity == null) {
                        headlinesAdEntity = new HeadlinesAdEntity();
                        headlinesAdEntity.setId(UUIDUtil.getUUID());
                        headlinesAdEntity.setAdId(headlinesAdResultData.getAdId());
                        headlinesAdEntity.setAdAccountId(accountInfo.getId());
                        headlinesAdEntity.setAdName(headlinesAdResultData.getAdName());
                        headlinesAdEntity.setCampaignId(headlinesAdResultData.getCampaignId());
                        headlinesAdEntity.setEa(accountInfo.getEa());
                        headlinesAdEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                        headlinesAdEntity.setInventoryType(headlinesAdResultData.getInventoryType());
                        headlinesAdEntity.setUpdateTime(updateTime == null ? new Date() : updateTime);
                        headlinesAdEntity.setCreateTime(createTime == null ? new Date() : createTime);
                        headlinesAdEntity.setType(TypeEnum.HEADLINES_AD_PLAN.getCode());
                        headlinesAdDAO.addHeadlinesAdIgnore(headlinesAdEntity);
                    }
                    headlinesAdEntity.setAdName(headlinesAdResultData.getAdName());
                    headlinesAdEntity.setAdAccountId(accountInfo.getId());
                    headlinesAdEntity.setStatus(HeadlinesAdStatusEnum.getStatusByName(headlinesAdResultData.getStatus()));
                    headlinesAdEntity.setOptStatus(HeadlinesOptStatusEnum.getStatusByName(headlinesAdResultData.getOptStatus()));
                    headlinesAdEntity.setDeliveryRange(HeadlinesDeliveryRangeEnum.fromStr(headlinesAdResultData.getDeliveryRange()));
                    headlinesAdEntity.setInventoryCatalog(InventoryCataLogEnum.fromStr(headlinesAdResultData.getInventoryCatalog()));
                    headlinesAdEntity.setUpdateTime(DateUtil.parse(headlinesAdResultData.getModifyTime()));
                    headlinesAdEntity.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
                    headlinesAdEntity.setRefreshTime(new Date());
                    headlinesAdEntity.setUpdateTime(updateTime == null ? new Date() : updateTime);
                    if (createTime != null) {
                        // 这里还是要更新这个创建时间 因为以前的旧数据搞错哦了
                        headlinesAdEntity.setCreateTime(createTime);
                    }
                    headlinesAdDAO.updateAdForRefresh(headlinesAdEntity);
                } catch (Exception e) {
                    log.error("HeadlinesAdMarketingManager refreshHeadlinesAd campaignResultData: {}, exception: ", headlinesAdResultData, e);
                }
            });
            syncHeadlinesAdToSubMarketingEventObj(accountInfo.getEa(), accountInfo, AdSourceEnum.SOURCE_JULIANG.getSource());
            return true;
        } catch (Exception e) {
            log.error("HeadlinesAdMarketingManager refreshHeadlinesAd accountInfo: {}, exception:", accountInfo, e);
        }
        return false;
    }


    public boolean refreshHeadlinesAdData(AdAccountEntity accountInfo, int day) {
        // 调用头条远程接口获取广告计划数据
        if (day == -1) {
            // 日期list 2016-10-26 到今天
            List<String> dateList = DateUtil.initMonthDateList();
            for (int i = 0; i < dateList.size() - 1; i++) {
                refreshHeadlinesAdDataByDay(accountInfo, dateList.get(i), dateList.get(i + 1));
            }
            return true;
        } else {
            List<String> dateList = CampaignInitDateUtil.initDateList(day);
            return refreshHeadlinesAdDataByDay(accountInfo, dateList.get(0), dateList.get(day - 1));
        }
    }


    private boolean refreshHeadlinesAdDataByDay(AdAccountEntity accountInfo, String startTime, String endTime) {
        int pageNum = 1;
        int pageSize = 100;
        List<HeadlinesAdDataResult> headlinesAdDataResultList = Lists.newArrayList();
        try {
            HeadlinesRequestResult<GetHeadlinesAdDataResult> requestResult = campaignApiManager.getHeadlinesAdData(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), startTime, endTime, pageNum, pageSize);
            if (requestResult == null || !requestResult.isSuccess() || requestResult.getData() == null || requestResult.getData().getPageInfo() == null || CollectionUtils.isEmpty(requestResult.getData().getHeadlinesAdDataList())) {
                log.info("HeadlinesAdMarketingManager refreshHeadlinesAdData campaignApiManager requestResult: {}", requestResult);
                return false;
            }
            log.info("HeadlinesAdMarketingManager refreshCampaignAdData requestResult: {}", requestResult);
            headlinesAdDataResultList.addAll(requestResult.getData().getHeadlinesAdDataList());
            PageInfo pageInfo = requestResult.getData().getPageInfo();

            if (pageInfo != null && pageInfo.getTotalPage() > 1) {
                for (int i = 2; i <= pageInfo.getTotalPage(); i++) {
                    pageNum = i;
                    HeadlinesRequestResult<GetHeadlinesAdDataResult> headlinesAdDataResult = campaignApiManager.getHeadlinesAdData(accountInfo.getAccountId(), adTokenManager.getAccessToken(accountInfo), startTime, endTime, pageNum, pageSize);
                    if (headlinesAdDataResult == null || !headlinesAdDataResult.isSuccess() || headlinesAdDataResult.getData() == null || headlinesAdDataResult.getData().getPageInfo() == null || CollectionUtils.isEmpty(headlinesAdDataResult.getData().getHeadlinesAdDataList())) {
                        log.info("HeadlinesAdMarketingManager refreshHeadlinesAdData campaignApiManager requestResult: {}", requestResult);
                        break;
                    }
                    headlinesAdDataResultList.addAll(headlinesAdDataResult.getData().getHeadlinesAdDataList());
                }
            }
            List<HeadlinesAdDataEntity> adDataEntityList = Lists.newArrayList();
            headlinesAdDataResultList.forEach(headlinesAdData -> {
                try {
                    HeadlinesAdDataEntity headlinesAdDataEntity = headlinesAdDataDAO.queryAdDataByDate(accountInfo.getEa(), accountInfo.getId(), headlinesAdData.getAdId(), DateUtil.parse(headlinesAdData.getStatDatetime()));
                    HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.queryAdByAdId(accountInfo.getEa(), accountInfo.getId(), headlinesAdData.getAdId());
                    int clueCount = 0;
                    if (headlinesAdEntity != null) {
                         clueCount = campaignDataManager.getLeadsCountByMarketingEvent(accountInfo.getEa(), Lists.newArrayList(headlinesAdEntity.getSubMarketingEventId()), null, null, null);
                    }
                    if (headlinesAdDataEntity == null) {
                        headlinesAdDataEntity = new HeadlinesAdDataEntity();
                        headlinesAdDataEntity.setId(UUIDUtil.getUUID());
                        headlinesAdDataEntity.setAdId(headlinesAdData.getAdId());
                        headlinesAdDataEntity.setAdAccountId(accountInfo.getId());
                        headlinesAdDataEntity.setCampaignId(headlinesAdData.getCampaignId());
                        headlinesAdDataEntity.setEa(accountInfo.getEa());
                        headlinesAdDataEntity.setStatDatetime(DateUtil.parse(headlinesAdData.getStatDatetime()));
                        headlinesAdDataDAO.addHeadlinesAdDataIgnore(headlinesAdDataEntity);
                    }
                    headlinesAdDataEntity.setClick(headlinesAdData.getClick());
                    headlinesAdDataEntity.setShow(headlinesAdData.getShow());
                    headlinesAdDataEntity.setCost(headlinesAdData.getCost());
                    headlinesAdDataEntity.setAvgClickCost(headlinesAdData.getAvgClickCost());
                    headlinesAdDataEntity.setLeads(clueCount);
                    headlinesAdDataDAO.updateHeadlinesAdDataRefreshData(headlinesAdDataEntity);
                    adDataEntityList.add(headlinesAdDataEntity);
                } catch (Exception e) {
                    log.error("HeadlinesAdMarketingManager refreshHeadlinesAdData campaignResultData: {}, ", headlinesAdData);
                }
            });
            buildAdvertisingDetailsObj(accountInfo.getEa(), accountInfo.getId(), adDataEntityList);
            return true;
        } catch (Exception e) {
            log.error("HeadlinesAdMarketingManager refreshHeadlinesAdData accountInfo: {}, exception: ", accountInfo, e);
        }
        return true;
    }

    private void buildAdvertisingDetailsObj(String ea, String accountId, List<HeadlinesAdDataEntity> adDataEntityList) {
        if (CollectionUtils.isEmpty(adDataEntityList)) {
            return;
        }
        List<Long> adIdList = adDataEntityList.stream().map(HeadlinesAdDataEntity::getAdId).collect(Collectors.toList());
        List<HeadlinesAdEntity> adEntityList = headlinesAdDAO.queryAdListByAdIds(ea, accountId, AdSourceEnum.SOURCE_JULIANG.getSource(), adIdList);
        Map<Long, String> adIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adEntityList)) {
            adEntityList.forEach(e -> adIdToMarketingEventIdMap.put(e.getAdId(), e.getSubMarketingEventId()));
        }
        List<CreateAdvertisingDetailObjArg> argList = Lists.newArrayList();
        for (HeadlinesAdDataEntity entity : adDataEntityList) {
            CreateAdvertisingDetailObjArg arg = new CreateAdvertisingDetailObjArg();
            arg.setEa(entity.getEa());
            arg.setLaunchDate(DateUtil.format2(entity.getStatDatetime()));
            arg.setMarketingEventId(adIdToMarketingEventIdMap.get(entity.getAdId()));
            arg.setAdAccountId(entity.getAdAccountId());
            arg.setCampaignOrAdGroupId(entity.getAdId());
            arg.setShow(entity.getShow());
            arg.setClick(entity.getClick());
            arg.setCost(entity.getCost());
            argList.add(arg);
        }
        advertisingDetailsObjManager.tryUpdateOrCreateObj(argList);
    }

    public void reindexAdvertisingDetailsObj(String ea) {
        int totalCount = headlinesAdDataDAO.countByEa(ea);
        int count = 0;
        String lastId = null;
        int pageSize = 2000;
        while (count < totalCount) {
            long t1 = System.currentTimeMillis();
            List<HeadlinesAdDataEntity> list = headlinesAdDataDAO.scanById(ea, lastId, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            List<HeadlinesAdDataEntity> finalList = list.stream().filter(e -> StringUtils.isNotBlank(e.getAdAccountId())).collect(Collectors.toList());
            count += list.size();
            lastId = list.get(list.size() - 1).getId();
            Map<String, List<HeadlinesAdDataEntity>> accountIdMap = finalList.stream().collect(Collectors.groupingBy(HeadlinesAdDataEntity::getAdAccountId));
            accountIdMap.forEach((accountId, result) -> buildAdvertisingDetailsObj(ea, accountId, result));
            log.info("toutiao reindexAdvertisingDetailsObj ea: {}, totalCount: {}, count:{} 耗时: {}ms", ea, totalCount, count, System.currentTimeMillis() - t1);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshPrototypeRoomAccountData(String ea, int cost) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_JULIANG.getSource());
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return;
        }
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            initCampaignAndAdGroup(adAccountEntity);
            int campaignTotalCount = headlinesCampaignDAO.countByAdAccountId(ea, adAccountEntity.getId());
            int pageSize = 1000;
            String lastId = null;
            int count = 0;
            while (count < campaignTotalCount) {
                List<HeadlinesCampaignEntity> campaignEntityList = headlinesCampaignDAO.scanByAdAccountId(ea, adAccountEntity.getId(), lastId, pageSize);
                if (CollectionUtils.isEmpty(campaignEntityList)) {
                    break;
                }
                int size = campaignEntityList.size();
                count += size;
                lastId = campaignEntityList.get(size - 1).getId();
                createCampaignDataAndAdGroupData(adAccountEntity, campaignEntityList, cost);
            }
        }
    }

    private void createCampaignDataAndAdGroupData(AdAccountEntity adAccountEntity, List<HeadlinesCampaignEntity> campaignEntityList, int totalCost) {
        String ea = adAccountEntity.getEa();
        Random random = new Random();
        List<HeadlinesCampaignDataEntity> campaignDataEntityList = Lists.newArrayList();
        Date now = new Date();
        Date actionDate = DateUtil.plusDay(now, -1);
        int campaignSize = campaignEntityList.size();
        int avgCost = totalCost / campaignSize;
        for (int i = 0; i < campaignSize; i++) {
            HeadlinesCampaignEntity campaignEntity = campaignEntityList.get(i);
            HeadlinesCampaignDataEntity dataEntity = new HeadlinesCampaignDataEntity();
            dataEntity.setId(UUIDUtil.getUUID());
            dataEntity.setEa(ea);
            dataEntity.setCampaignId(campaignEntity.getCampaignId());
            dataEntity.setActionDate(actionDate);
            // 每日消费数 1000~5000
            int cost;
            if (i == campaignSize - 1) {
                cost = totalCost;
            } else {
                cost = random.nextInt(avgCost);
                totalCost -= cost;
            }
            cost = Math.max(cost, 0);
            dataEntity.setCost((double) cost);
            // 每日展现数 = 每日消费数 * (1 ~ 10)
            long pv = (long) cost * (random.nextInt(11) + 1);
            dataEntity.setPv(pv);
            // 每日点击数 = 昨日展现数 * (0.1 ~ 10%)
            double click = pv * (random.nextInt(10) + 0.1) * 0.01;
            dataEntity.setClick((long) click);
            dataEntity.setLeads(0);
            dataEntity.setCreateTime(now);
            dataEntity.setUpdateTime(now);
            dataEntity.setAdAccountId(campaignEntity.getAdAccountId());
            dataEntity.setType(TypeEnum.HEADLINES_PROJECT.getCode());
            double avgClickCost = 0D;
            if (click > 0) {
                avgClickCost = BigDecimal.valueOf(cost).divide(BigDecimal.valueOf(click), 2, RoundingMode.HALF_UP).doubleValue();
            }
            dataEntity.setAvgClickCost(avgClickCost);
            campaignDataEntityList.add(dataEntity);
        }
        headlinesCampaignDataDAO.batchInsert(campaignDataEntityList);
        buildAdGroupData(adAccountEntity, campaignDataEntityList);
    }

    private void buildAdGroupData(AdAccountEntity adAccountEntity, List<HeadlinesCampaignDataEntity> campaignDataEntityList) {
        Map<Long, HeadlinesCampaignDataEntity> headlinesCampaignDataEntityMap = campaignDataEntityList.stream().collect(Collectors.toMap(HeadlinesCampaignDataEntity::getCampaignId, e -> e, (v1, v2) -> v1));
        String ea = adAccountEntity.getEa();
        List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryAdListByCampaignIdList(ea, Lists.newArrayList(headlinesCampaignDataEntityMap.keySet()));
        if (CollectionUtils.isEmpty(headlinesAdEntityList)) {
            return;
        }
        Map<Long, List<HeadlinesAdEntity>> campaignIdToAdGroupMap = headlinesAdEntityList.stream().collect(Collectors.groupingBy(HeadlinesAdEntity::getCampaignId));
        Random random = new Random();
        for (Map.Entry<Long, List<HeadlinesAdEntity>> entry : campaignIdToAdGroupMap.entrySet()) {
            long campaignId = entry.getKey();
            List<HeadlinesAdEntity> campaignAdGroupList = entry.getValue();
            HeadlinesCampaignDataEntity headlinesCampaignDataEntity = headlinesCampaignDataEntityMap.get(campaignId);
            long adGroupTotalPv = headlinesCampaignDataEntity.getPv();
            long adGroupTotalClick = headlinesCampaignDataEntity.getClick();
            double adGroupTotalCost = headlinesCampaignDataEntity.getCost();
            List<HeadlinesAdDataEntity> headlinesAdDataEntityList = Lists.newArrayList();
            for (int i = 0; i < campaignAdGroupList.size(); i++) {
                HeadlinesAdEntity headlinesAdEntity = campaignAdGroupList.get(i);
                HeadlinesAdDataEntity headlinesAdDataEntity = new HeadlinesAdDataEntity();
                headlinesAdDataEntity.setId(UUIDUtil.getUUID());
                headlinesAdDataEntity.setEa(ea);
                headlinesAdDataEntity.setAdId(headlinesAdEntity.getAdId());
                headlinesAdDataEntity.setCampaignId(campaignId);
                headlinesAdDataEntity.setStatDatetime(headlinesCampaignDataEntity.getActionDate());
                if (i == campaignAdGroupList.size() - 1) {
                    headlinesAdDataEntity.setShow(adGroupTotalPv);
                    headlinesAdDataEntity.setClick(adGroupTotalClick);
                    headlinesAdDataEntity.setCost(adGroupTotalCost);
                } else {
                    int adGroupPv = 0;
                    if (adGroupTotalPv > 0) {
                        adGroupPv = random.nextInt((int) adGroupTotalPv);
                    }
                    int adGroupClick = 0;
                    if (adGroupTotalClick > 0) {
                        adGroupClick = random.nextInt((int) adGroupTotalClick);
                    }
                    int adGroupCost = 0;
                    if (adGroupTotalCost > 0) {
                        adGroupCost = random.nextInt(Double.valueOf(adGroupTotalCost).intValue());
                    }
                    adGroupTotalPv -= adGroupPv;
                    adGroupTotalClick -= adGroupClick;
                    adGroupTotalCost -= adGroupCost;
                    headlinesAdDataEntity.setShow((long) adGroupPv);
                    headlinesAdDataEntity.setClick((long) adGroupClick);
                    headlinesAdDataEntity.setCost((double) adGroupCost);
                }
                double avgClickCost = 0D;
                if (headlinesAdDataEntity.getClick() > 0) {
                    avgClickCost = BigDecimal.valueOf(headlinesAdDataEntity.getCost()).divide(BigDecimal.valueOf(headlinesAdDataEntity.getClick()), 2, RoundingMode.HALF_UP).doubleValue();
                }
                headlinesAdDataEntity.setAvgClickCost(avgClickCost);
                headlinesAdDataEntity.setLeads(0);
                headlinesAdDataEntity.setAdAccountId(adAccountEntity.getId());
                headlinesAdDataEntity.setType(TypeEnum.HEADLINES_PROMOTION.getCode());
                headlinesAdDataEntityList.add(headlinesAdDataEntity);
            }
            headlinesAdDataDAO.batchInsert(headlinesAdDataEntityList);
            buildAdvertisingDetailsObj(ea, adAccountEntity.getId(), headlinesAdDataEntityList);
        }
    }

    private void initCampaignAndAdGroup(AdAccountEntity adAccountEntity) {
        String ea = adAccountEntity.getEa();
        List<HeadlinesCampaignEntity> campaignEntityList = Lists.newArrayList();
        List<HeadlinesAdEntity> adGroupEntityList = Lists.newArrayList();
        List<String> existCampaignNameList = headlinesCampaignDAO.getAllNameByAdAccountId(ea, adAccountEntity.getId());
        if (existCampaignNameList == null) {
            existCampaignNameList = Lists.newArrayList();
        }
        Set<String> existCampaignNameSet = Sets.newHashSet(existCampaignNameList);
//        String finalCampaignList = I18nUtil.getSuitedLangText(campaignList, campaignListEN);
        String finalCampaignList = null;
        String lang = marketingEventCommonSettingService.getLang(ea);
        int langType = org.apache.commons.lang3.StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
        if(langType == 1){
            finalCampaignList = campaignList;
        }else {
            finalCampaignList = campaignListEN;
        }
        JSONObject json = JSONObject.parseObject(finalCampaignList);
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            String campaignName = entry.getKey();
            if (existCampaignNameSet.contains(campaignName)) {
                continue;
            }
            List<String> adGroupNameList = (List<String>) entry.getValue();
            HeadlinesCampaignEntity headlinesCampaignEntity = new HeadlinesCampaignEntity();
            headlinesCampaignEntity.setId(UUIDUtil.getUUID());
            headlinesCampaignEntity.setEa(ea);
            headlinesCampaignEntity.setCampaignId(Long.parseLong(redisManager.getPrimaryId()));
            headlinesCampaignEntity.setCampaignName(campaignName);
            headlinesCampaignEntity.setBudget(null);
            headlinesCampaignEntity.setBudgetMode(BudgetModeEnum.BUDGET_MODE_INFINITE.getStatus());
            headlinesCampaignEntity.setLandingType(LandingTypeEnum.ARTICAL.getStatus());
            headlinesCampaignEntity.setStatus(HeadlinesCampaignStatusEnum.PROJECT_STATUS_ENABLE.getStatus());
            headlinesCampaignEntity.setMarketingPurpose(CampaignMarketingPurposeEnum.DEFAULT.getStatus());
            headlinesCampaignEntity.setDeliveryMode(DeliveryModeEnum.PROCEDURAL.getStatus());
            headlinesCampaignEntity.setRefreshTime(new Date());
            headlinesCampaignEntity.setSource(adAccountEntity.getSource());
            headlinesCampaignEntity.setAdAccountId(adAccountEntity.getId());
            headlinesCampaignEntity.setType(TypeEnum.HEADLINES_PROJECT.getCode());
            headlinesCampaignEntity.setAdType(HeadlinesAdTypeEnum.ALL.getDesc());
            campaignEntityList.add(headlinesCampaignEntity);

            for (String adGroupName : adGroupNameList) {
                HeadlinesAdEntity headlinesAdEntity = new HeadlinesAdEntity();
                headlinesAdEntity.setId(UUIDUtil.getUUID());
                headlinesAdEntity.setEa(ea);
                headlinesAdEntity.setAdId(Long.parseLong(redisManager.getPrimaryId()));
                headlinesAdEntity.setAdName(adGroupName);
                headlinesAdEntity.setCampaignId(headlinesCampaignEntity.getCampaignId());
                headlinesAdEntity.setStatus(HeadlinesAdStatusEnum.OK.getStatus());
                headlinesAdEntity.setOptStatus(HeadlinesOptStatusEnum.AD_STATUS_ENABLE.getStatus());
                headlinesAdEntity.setDeliveryRange(HeadlinesDeliveryRangeEnum.DEFAULT.getStatus());
                headlinesAdEntity.setInventoryCatalog(InventoryCataLogEnum.SCENE.getStatus());
                headlinesAdEntity.setInventoryType(Lists.newArrayList(""));
                headlinesAdEntity.setRefreshTime(new Date());
                headlinesAdEntity.setSource(adAccountEntity.getSource());
                headlinesAdEntity.setAdAccountId(adAccountEntity.getId());
                headlinesAdEntity.setType(TypeEnum.HEADLINES_PROMOTION.getCode());
                adGroupEntityList.add(headlinesAdEntity);
            }
        }
        if (CollectionUtils.isEmpty(campaignEntityList)) {
            return;
        }
        headlinesCampaignDAO.batchAddHeadlinesCampaign(campaignEntityList);
        List<AdCampaignEntity> adCampaignEntityList = Lists.newArrayList(campaignEntityList);
        refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, adCampaignEntityList, adAccountEntity.getSource());

        headlinesAdDAO.batchAddHeadlinesAd(adGroupEntityList);
        List<AdvertiserAdEntity> advertiserAdEntityList = Lists.newArrayList(adGroupEntityList);
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_JULIANG.getSource());
    }


    public void refreshLocalAccountInfo(AdAccountEntity adAccountEntity) {
        HeadlinesRequestResult<GetLocalAccountInfoResultData> result = campaignApiManager.getLocalAccountInfo(adAccountEntity, adTokenManager.getAccessToken(adAccountEntity));
        if (result == null || !result.isSuccess()) {
            log.warn("HeadlinesAdMarketingManager refreshLocalAccountInfo fail ea:{}, adAccount:{}, result:{}", adAccountEntity.getEa(), adAccountEntity, result);
            return;
        }
        GetLocalAccountInfoResultData resultData = result.getData();
        if (resultData == null || resultData.getResultData() == null || CollectionUtils.isEmpty(resultData.getResultData())) {
            log.info("HeadlinesAdMarketingManager refreshLocalAccountInfo empty ea:{}, adAccount:{}, resultData:{}", adAccountEntity.getEa(), adAccountEntity, resultData);
            return;
        }
        GetLocalAccountInfoResultData.LocalAccountInfoResultData localAccountInfoResultData = resultData.getResultData().get(0);
        Double balanceInCents = localAccountInfoResultData.getBalance(); // 接口获取的余额是以“分”为单位的
        Double balanceInYuan = balanceInCents == null ? 0 :  balanceInCents; // 数据库余额统一存”分“为单位
        adAccountEntity.setBalance(balanceInYuan);
        adAccountManager.updateAccount(adAccountEntity);
    }
}
