package com.facishare.marketing.provider.entity.cta;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CtaEntity implements Serializable {
    //主键ID
    private String id;
    private String ea;
    //名称
    private String name;
    //触发方式json
    private String triggerSettings;
    //引导组件json
    private String guideComponents;
    // 状态 0:未启用 1:已启用 4:已停用 5:已删除
    private Integer status;
    //创建人
    private Integer createBy;
    // 创建时间瓬
    private Date createTime;
    //最后修改人
    private Integer updateBy;
    // 更新时间
    private Date updateTime;
    //是否独立按钮
    private Boolean standaloneButton;
    //触发方式
    private String triggerTypes;
    //引导组件类型
    private String guideComponentTypes;
    //表单ID
    private String formId;
}
