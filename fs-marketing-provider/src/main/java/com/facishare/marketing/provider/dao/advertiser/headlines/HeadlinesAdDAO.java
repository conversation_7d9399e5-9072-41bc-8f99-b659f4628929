package com.facishare.marketing.provider.dao.advertiser.headlines;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdvertiserDataOverviewEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created by wang<PERSON><PERSON> on 2021/8/6 4:37 下午
 */
public interface HeadlinesAdDAO {

    @Insert("insert into headlines_ad (\"id\", \"ea\",\"ad_id\", \"ad_name\", \"campaign_id\", \"inventory_catalog\", \"inventory_type\", \"refresh_time\", \"create_time\", \"update_time\", \"source\", \"type\") values("
            + " #{obj.id},\n"
            + " #{obj.ea},\n"
            + " #{obj.adId},\n"
            + " #{obj.adName},\n"
            + " #{obj.campaignId},\n"
            + " #{obj.inventoryCatalog},\n"
            + " #{obj.inventoryType},\n"
            + " now(),\n"
            + " #{obj.createTime},\n"
            + " #{obj.updateTime},\n"
            + " #{obj.source},\n"
            + " #{obj.type}\n"
            + ") ON CONFLICT DO NOTHING;")
    void addHeadlinesAdIgnore(@Param("obj") HeadlinesAdEntity adEntity);

    @Insert("<script>"
            + "INSERT INTO headlines_ad(\"id\", \"ea\", \"ad_id\", \"ad_account_id\", \"ad_name\", " +
            "\"campaign_id\", \"status\", \"opt_status\", \"delivery_range\", \"inventory_catalog\", \"inventory_type\"," +
            " \"sub_marketing_event_id\", \"create_time\", \"update_time\", \"refresh_time\", \"source\", \"type\") VALUES"
            +  "  <foreach collection='adEntityList' item='item' separator=','>"
            +  "   (#{item.id}, #{item.ea}, #{item.adId}, #{item.adAccountId}, #{item.adName}, #{item.campaignId}, #{item.status}, #{item.optStatus}, #{item.deliveryRange}, #{item.inventoryCatalog}, #{item.inventoryType}, #{item.subMarketingEventId}, now(), #{item.updateTime}, now(), #{item.source}, #{item.type})"
            +  "  </foreach>"
            +  "ON CONFLICT DO NOTHING;"
            + "</script>")
    @FilterLog
    int batchAddHeadlinesAd(@Param("adEntityList") List<HeadlinesAdEntity> adEntityList);

    @Update("update headlines_ad set "
            + "\"ad_name\" = #{obj.adName} , "
            + "\"ad_account_id\" = #{obj.adAccountId} , "
            + "\"campaign_id\" = #{obj.campaignId} , "
            + "\"status\" = #{obj.status}, "
            + "\"opt_status\" = #{obj.optStatus}, "
            + "\"delivery_range\" = #{obj.deliveryRange}, "
            + "\"inventory_catalog\" = #{obj.inventoryCatalog}, "
            + "\"inventory_type\" = #{obj.inventoryType}, "
            + "\"sub_marketing_event_id\" = #{obj.subMarketingEventId}, "
            + "\"refresh_time\" = #{obj.refreshTime}, "
            + "\"update_time\" = #{obj.updateTime}, "
            + "\"create_time\" = #{obj.createTime}, "
            + "\"source\" = #{obj.source}"
            + "  where \"id\" = #{obj.id}")
    void updateAdForRefresh(@Param("obj") HeadlinesAdEntity adEntityList);

//    @Select(" <script>"
//            + "SELECT * FROM headlines_ad WHERE \"ea\"=#{ea} AND \"source\"=#{source}"
//            + "</script>")
//    List<HeadlinesAdEntity> listRelateMarketingEventAd(@Param("ea") String ea, @Param("source") String source);


    @Select(" <script>"
            + "SELECT * FROM headlines_ad WHERE \"ea\"=#{ea} AND ad_account_id=#{adAccountId} AND \"source\"=#{source}"
            + "</script>")
    List<HeadlinesAdEntity> listRelateMarketingEventAd(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Update("<script>UPDATE headlines_ad as c SET ad_name=tmp.adName, status=tmp.status, opt_status=tmp.optStatus,\n"
            + "delivery_range=tmp.deliveryRange, inventory_catalog=tmp.inventoryCatalog, inventory_type=tmp.inventoryType, refresh_time=now(), update_time=now() FROM (values"
            + "<foreach separator=',' collection='adEntityList' item='item'>"
            +   "(#{item.id}, #{item.adId}, #{item.adName}, #{item.status}, #{item.optStatus}, #{item.deliveryRange}, #{item.inventoryCatalog}, #{item.inventoryType} )"
            + "</foreach>"
            + ") as tmp(id, adId, adName, status, optStatus, deliveryRange, inventoryCatalog, inventoryType) WHERE c.ea = #{ea} AND c.ad_account_Id=#{adAccountId} AND c.id=tmp.id"
            + "</script>")
    @FilterLog
    void batchUpdateAd(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("adEntityList") List<HeadlinesAdEntity> adEntityList);

    @Select("<script>  " +
            "SELECT * FROM headlines_ad WHERE ea=#{ea} AND source=#{source}"
            + "</script>")
    List<HeadlinesCampaignEntity> queryCampaignByEa(@Param("ea") String ea, @Param("source")String source);

    @Select("SELECT * FROM headlines_ad WHERE id=#{id}")
    HeadlinesCampaignEntity queryCampaignById(@Param("id") String id);

    @Update("update headlines_ad set "
            + "\"sub_marketing_event_id\" = #{subMarketingEventId},"
            + "\"marketing_event_id\" = #{marketingEventId},"
            + "\"update_time\" = now()"
            + "  where \"id\" = #{id}")
    void relateAdSubMarketingEvent(@Param("id") String id, @Param("subMarketingEventId") String subMarketingEventId, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM headlines_ad WHERE ea=#{ea} and ad_account_id=#{adAccountId} and sub_marketing_event_id=#{subMarketingEventId} limit 1")
    HeadlinesAdEntity queryBySubMarketingEventId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND ad_id=#{adId} limit 1")
    HeadlinesAdEntity queryAdByAdId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adId")  Long adId);

    @Select("SELECT * FROM headlines_ad WHERE ea=#{ea} AND ad_id=#{adId} limit 1")
    HeadlinesAdEntity queryByAdId(@Param("ea") String ea, @Param("adId")  Long adId);

    @Select("SELECT * FROM headlines_ad WHERE id=#{id}")
    HeadlinesAdEntity queryAdById(@Param("id") String id);

    @Select("<script>  "
            + "SELECT * FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND ad_id = ANY(ARRAY"
            +   "<foreach collection = 'adIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    @FilterLog
    List<HeadlinesAdEntity> queryAdListByAdIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("adIds") List<Long> adIds);

    @Select("<script>  "
            + "SELECT * FROM headlines_ad WHERE ea=#{ea} AND campaign_id = ANY(ARRAY"
            +   "<foreach collection = 'campaignIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<HeadlinesAdEntity> queryAdListByCampaignIdList(@Param("ea") String ea, @Param("campaignIdList") List<Long> campaignIdList);


    @Select("<script>  "
            + "SELECT * \n"
            + "FROM headlines_ad \n"
            + "WHERE ea = #{ea} \n"
            +	"<if test=\"adAccountId != null\">"
            +       "AND ad_account_id = #{adAccountId} \n"
            +   "</if>"
            +	"<if test=\"campaignId != null\">"
            +       "AND campaign_id = #{campaignId} \n"
            +   "</if>"
            +	"<if test=\"status != null\">"
            +	    "AND status = #{status}\n"
            +   "</if>"
            +	"<if test=\"nameKey != null\">"
            +	    "AND ad_name like concat(concat('%',#{nameKey}),'%')\n"
            +   "</if>"
            +	"<if test=\"startTime != null\">"
            +	    "AND create_time &gt;= #{startTime}\n"
            +   "</if>"
            +	"<if test=\"endTime != null\">"
            +	    "AND create_time &lt;= #{endTime}\n"
            +   "</if>"
            +	"<if test=\"campaignIds != null\">"
            +       "AND campaign_id IN"
            +        "<foreach collection = 'campaignIds' index ='index' item = 'cam' open = '(' separator = ',' close = ')'>"
            +           "#{campaignIds[${index}]}"
            +       "</foreach>"
            +   "</if>"
            + "ORDER BY ad_id"
            + "</script>")
    List<HeadlinesAdEntity> pageAd(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("status") Integer status, @Param("campaignId") Long campaignId,
                                   @Param("nameKey") String nameKey, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("campaignIds") List<Long> campaignIdList, @Param("page") Page page);

    @Select("SELECT COUNT(*) FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    int getRefreshAdTotalCount(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Select("SELECT ad_id FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} ORDER BY ad_id")
    List<Long> pageRefreshAdIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source, @Param("page") Page page);

    @Select("SELECT COUNT(*) FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    int queryAdTotalCount(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Select("<script>"
            + "SELECT * FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}"
            + "</script>")
    @FilterLog
    List<HeadlinesAdEntity> pageAdEntityData(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source, @Param("page") Page page);

    @Update("<script>UPDATE headlines_ad as c SET sub_marketing_event_id=tmp.subMarketingEventId, update_time=now() FROM (values"
            + "<foreach separator=',' collection='adEntityList' item='item'>"
            +   "(#{item.id}, #{item.subMarketingEventId})"
            + "</foreach>"
            + ") as tmp(id, subMarketingEventId) WHERE c.ea = #{ea} AND c.ad_account_id=#{adAccountId} AND c.id=tmp.id"
            + "</script>")
    void batchUpdateSubMarketingEventById(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adEntityList") List<AdvertiserAdEntity> adEntityList);

    @Select("SELECT sub_marketing_event_id FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    List<String> getAdLinkSubMarketingEventIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Select("SELECT sub_marketing_event_id FROM headlines_ad WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND ad_id=#{adId}")
    String querySubMarketingEventIdByAdId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source, @Param("adId") Long adId);


    @Select("SELECT * FROM headlines_ad WHERE ea=#{ea} and sub_marketing_event_id=#{marketingEventId} limit 1")
    HeadlinesAdEntity getBySubMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

//    @Select("SELECT * FROM ad_leads_mapping_data WHERE ea=#{ea} ")
//    List<AdLeadsMappingDataEntity> queryAdLeadsMappingDataByEa(@Param("ea") String ea);

//    @Insert("INSERT INTO ad_leads_mapping_data(\"id\", \"ea\", \"ad_account_id\",\"create_by\", \"crm_record_type\", \"create_time\", \"source\") " +
//            "VALUES(#{id}, #{ea}, #{adAccountId}, #{createBy}, #{crmRecordType}, #{updateTime}, #{source})")
//    void addAdLeadsMappingData(@Param("id") String id, @Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("createBy") Integer createBy, @Param("crmRecordType") String crmRecordType, @Param("updateTime") Date updateTime, @Param("source") String source);
//
//    @Update("update ad_leads_mapping_data set "
//            + "\"ea\" = #{obj.ea} , "
//            + "\"create_by\" = #{obj.createBy} , "
//            + "\"crm_pool_id\" = #{obj.crmPoolId}, "
//            + "\"crm_record_type\" = #{obj.crmRecordType}, "
//            + "\"custom_func_api_name\" = #{obj.customFuncApiName}, "
//            + "\"create_time\" = now(), "
//            + "\"update_time\" = now(), "
//            + "\"source\" = #{obj.source}"
//            + "  where \"ea\" = #{obj.ea} and \"source\" = #{obj.source}")
//    int updateLeadsMappingData(@Param("obj") AdLeadsMappingDataEntity leadsMappingDataEntity);

    @Select("<script>  "
            + "select sum(ad_data.show) as show, sum(ad_data.click) as click, sum(ad_data.cost) as cost, sum(ad_data.avg_click_cost) as avgClickCost "
            + "from headlines_ad_data ad_data join headlines_ad ad \n"
            + "on ad_data.ad_id = ad.ad_id and ad_data.ea = ad.ea and ad_data.ad_account_id = ad.ad_account_id\n"
            + "where ad_data.ea = #{ea} and ad_data.ad_account_id = #{adAccountId} \n"
            +	"<if test=\"adName != null\">"
            +	    "AND ad.ad_name like concat(concat('%',#{adName}),'%')\n"
            +   "</if>"
            + "    <if test=\"startTime != null\">\n"
            + "         AND ad_data.stat_datetime  &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND ad_data.stat_datetime  &lt;= #{endTime}\n"
            + "    </if>\n"
            +	"<if test=\"campaignIds != null\">"
            +       "AND ad.campaign_id =ANY(ARRAY"
            +        "<foreach collection = 'campaignIds' index ='index' item = 'cam' open = '[' separator = ',' close = ']'>"
            +           "#{campaignIds[${index}]}"
            +       "</foreach> )"
            +   "</if>"
            + "</script>")
    HeadlinesAdvertiserDataOverviewEntity queryAdDataOverview(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime, @Param("adName") String adName, @Param("campaignIds") List<Long> campaignIdList);

    @Select("SELECT sub_marketing_event_id FROM headlines_ad WHERE ea=#{ea}")
    List<String> getAllMarketingEventId(@Param("ea") String ea);

    @Select("<script>SELECT * "
            + "FROM headlines_ad WHERE ea=#{ea} AND ad_name = ANY( ARRAY "
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    @FilterLog
    List<AdvertiserAdEntity> queryByNames(@Param("ea") String ea, @Param("nameList") List<String> nameList);

    @Select("<script>SELECT * "
            + "FROM headlines_ad WHERE ea=#{ea} AND sub_marketing_event_id = ANY( ARRAY "
            +   "<foreach collection = 'subMarketingIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<HeadlinesAdEntity> queryBySubMarketingIdList(@Param("ea") String ea, @Param("subMarketingIdList") List<String> subMarketingIdList);

    @Select("<script>"
            + "select * from headlines_ad  where  ea = #{ea} and ad_account_id  =ANY(ARRAY\n"
            +   "<foreach collection = 'adAccountIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>"
    )
    List<HeadlinesAdEntity> queryAllByAdAccountIdList(@Param("ea") String ea, @Param("adAccountIdList") List<String> adAccountIdList);

    @Select("<script>SELECT * "
            + "FROM headlines_ad WHERE ea=#{ea} and ad_account_id = #{adAccountId} AND ad_name = ANY( ARRAY "
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<HeadlinesAdEntity> queryByNameAndAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("nameList") List<String> nameList);

    @Select("SELECT * FROM headlines_ad WHERE ea=#{ea}")
    List<HeadlinesAdEntity> queryByEa(@Param("ea") String ea);

    @Update("update headlines_ad set sub_marketing_event_id = #{subMarketingEventId} WHERE ea=#{ea} and id = #{id}")
    int updateSubMarketingEventIdById(@Param("ea") String ea, @Param("subMarketingEventId") String subMarketingEventId, @Param("id") String id);

    @Select("<script>"
            + "select sub_marketing_event_id from headlines_ad WHERE ea=#{ea} AND ad_account_id = ANY(ARRAY \n"
            +   "<foreach collection = 'accountIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<String> queryMarketingEventIdByEaAndAccountIdList(@Param("ea") String ea, @Param("accountIdList") List<String> accountIdList);
}
