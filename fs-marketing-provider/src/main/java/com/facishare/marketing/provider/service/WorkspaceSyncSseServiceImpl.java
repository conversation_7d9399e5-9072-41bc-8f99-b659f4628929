package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.WorkspaceSyncSseService;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 工作空间同步状态的 SSE 服务。
 * 负责管理所有订阅工作空间同步状态的 SSE 连接，并向其推送实时更新。
 */
@Service
@Slf4j
public class WorkspaceSyncSseServiceImpl implements WorkspaceSyncSseService {

    // 使用 ConcurrentHashMap 存储每个 workspaceId 对应的 SseEmitter 列表。
    // CopyOnWriteArrayList 适用于读多写少的场景，且在迭代时修改不会抛出 ConcurrentModificationException。
    // 这样可以确保在多线程环境下对连接列表的操作是线程安全的。
    private final Map<String, CopyOnWriteArrayList<SseEmitter>> emitters = new ConcurrentHashMap<>();

    /**
     * 添加一个新的 SseEmitter 到指定的工作空间ID的连接列表。
     * 当连接完成、超时或发生错误时，会自动从列表中移除。
     *
     * @param workspaceId 工作空间ID，用于标识连接属于哪个工作空间。
     * @param emitter     SseEmitter 实例，代表一个客户端的 SSE 连接。
     */
    public void addEmitter(String workspaceId, SseEmitter emitter) {
        // 获取或创建该 workspaceId 对应的 SseEmitter 列表。
        // computeIfAbsent 方法确保如果 key 不存在，则原子性地创建一个新的 CopyOnWriteArrayList。
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = emitters.computeIfAbsent(workspaceId, k -> new CopyOnWriteArrayList<>());
        workspaceEmitters.add(emitter);
        log.info("为工作空间 {} 添加新的 SSE 连接，当前连接数: {}", workspaceId, workspaceEmitters.size());

        // 注册完成回调：当客户端连接正常关闭时触发。
        emitter.onCompletion(() -> {
            workspaceEmitters.remove(emitter); // 从列表中移除该 emitter。
            log.info("工作空间 {} 的 SSE 连接完成，剩余连接数: {}", workspaceId, workspaceEmitters.size());
            // 如果该工作空间没有活跃的连接了，可以考虑清理 ConcurrentHashMap 中的 entry，释放内存。
            if (workspaceEmitters.isEmpty()) {
                emitters.remove(workspaceId);
            }
        });

        // 注册超时回调：当客户端连接在指定时间内没有收到事件时触发。
        emitter.onTimeout(() -> {
            emitter.complete(); // 显式完成连接，这也会触发 onCompletion 回调。
            workspaceEmitters.remove(emitter); // 从列表中移除该 emitter。
            log.warn("工作空间 {} 的 SSE 连接超时，剩余连接数: {}", workspaceId, workspaceEmitters.size());
            if (workspaceEmitters.isEmpty()) {
                emitters.remove(workspaceId);
            }
        });
        
    }

    /**
     * 向指定工作空间ID的所有活跃 SSE 连接推送同步状态更新。
     * 如果没有活跃连接，则不进行任何操作。
     *
     * @param workspaceId 工作空间ID，用于确定向哪些客户端推送。
     * @param status      同步状态字符串 (例如: INITIAL, SYNCING, COMPLETED, FAILED)。
     */
    public void sendSyncStatus(String workspaceId, String status) {
        // 获取指定工作空间ID对应的 SseEmitter 列表。
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = emitters.get(workspaceId);
        // 如果列表为空或不存在，说明没有客户端订阅该工作空间的状态，直接返回。
        if (workspaceEmitters == null || workspaceEmitters.isEmpty()) {
            log.debug("工作空间 {} 没有活跃的 SSE 连接，无需推送状态: {}", workspaceId, status);
            return;
        }

        log.info("向工作空间 {} 的 {} 个 SSE 连接推送状态: {}", workspaceId, workspaceEmitters.size(), status);
        // 遍历所有连接并发送事件。
        // 使用 for-each 循环遍历 CopyOnWriteArrayList 是安全的。
        for (SseEmitter emitter : workspaceEmitters) {
            try {
                // 发送一个名为 "sync-status" 的事件，数据为状态字符串。
                // 客户端的 EventSource 将通过 addEventListener('sync-status', ...) 来监听这个事件。
                emitter.send(SseEmitter.event().name("sync-status").data(status));
            } catch (IOException e) {
                // 如果发送失败，通常意味着客户端已断开连接。
                // 记录错误，但不需要手动移除 emitter，因为 onCompletion/onTimeout 回调会处理。
                log.error("向工作空间 {} 的 SSE 连接发送状态失败，连接可能已断开: {}", workspaceId, e.getMessage());
                // 在旧版本Spring中，如果需要更明确地标记连接为错误并关闭，可以在这里调用 emitter.completeWithError(e);
                // 但通常情况下，IOException 已经表明连接不可用，Spring 会自动处理后续的清理。
            } catch (Exception e) {
                // 捕获其他可能的异常。
                log.error("向工作空间 {} 的 SSE 连接发送状态时发生未知异常: {}", workspaceId, e.getMessage(), e);
                // 同样，如果需要，可以在这里调用 emitter.completeWithError(e);
            }
        }
    }
}
