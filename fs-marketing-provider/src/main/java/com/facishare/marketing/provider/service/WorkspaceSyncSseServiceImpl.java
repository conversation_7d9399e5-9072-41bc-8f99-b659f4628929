package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.WorkspaceSyncSseService;
import com.facishare.marketing.common.sse.RedisMQ;
import com.facishare.marketing.common.sse.RedisMQHandler;
import com.alibaba.fastjson.JSONObject;
import com.github.jedis.support.MergeJedisCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作空间同步状态的 SSE 服务 V2版本
 * 基于现有RedisMQ机制实现多节点支持，参考AI聊天的简洁实现
 */
@Service("workspaceSyncSseService")
@Slf4j
public class WorkspaceSyncSseServiceImpl implements WorkspaceSyncSseService {

    @Autowired
    private MergeJedisCmd jedisCmd;

    // Redis消息队列前缀
    private static final String WORKSPACE_SYNC_QUEUE_PREFIX = "WORKSPACE_SYNC_MSG:";

    /**
     * 创建基于Redis队列的SSE连接（参考AI聊天实现）
     */
    public SseEmitter createSseEmitter(String workspaceId) {
        return RedisMQHandler.onMessage(jedisCmd, workspaceId);
    }

    /**
     * 发送同步状态到Redis队列
     */
    @Override
    public void sendSyncStatus(String workspaceId, String status) {
        try {
            JSONObject message = new JSONObject();
            message.put("workspaceId", workspaceId);
            message.put("status", status);
            message.put("timestamp", System.currentTimeMillis());
            message.put("finish", true); // 标记消息完成

            // 使用现有的RedisMQ发布消息
            RedisMQ.publish(jedisCmd, workspaceId, message.toJSONString());

            log.debug("发送同步状态到Redis队列: workspaceId={}, status={}", workspaceId, status);

        } catch (Exception e) {
            log.error("发送同步状态失败: workspaceId={}, status={}", workspaceId, status, e);
        }
    }

    /**
     * 兼容原有接口，但不实际使用
     */
    @Override
    public void addEmitter(String workspaceId, SseEmitter emitter) {
        // 空实现，V2版本不需要预先添加连接
        log.debug("V2版本不需要预先添加连接: {}", workspaceId);
    }
}
