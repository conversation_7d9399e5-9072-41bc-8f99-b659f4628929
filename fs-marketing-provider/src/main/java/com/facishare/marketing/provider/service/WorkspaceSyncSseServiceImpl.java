package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.WorkspaceSyncSseService;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.provider.manager.RedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.JedisPubSub;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 工作空间同步状态的 SSE 服务。
 * 负责管理所有订阅工作空间同步状态的 SSE 连接，并向其推送实时更新。
 *
 * 多节点部署优化：
 * 1. 本地维护SSE连接列表
 * 2. 通过Redis发布订阅实现跨节点消息推送
 * 3. 支持K8s多Pod部署和负载均衡
 */
@Service
@Slf4j
public class WorkspaceSyncSseServiceImpl implements WorkspaceSyncSseService {

    @Autowired
    private RedisManager redisManager;

    // 使用 ConcurrentHashMap 存储每个 workspaceId 对应的 SseEmitter 列表。
    // CopyOnWriteArrayList 适用于读多写少的场景，且在迭代时修改不会抛出 ConcurrentModificationException。
    // 这样可以确保在多线程环境下对连接列表的操作是线程安全的。
    private final Map<String, CopyOnWriteArrayList<SseEmitter>> emitters = new ConcurrentHashMap<>();

    // Redis发布订阅相关
    private static final String WORKSPACE_SYNC_CHANNEL_PREFIX = "workspace_sync_status:";
    private ExecutorService subscriberExecutor;
    private volatile boolean isSubscriberRunning = false;

    // 当前节点ID，用于日志追踪
    private String nodeId;

    /**
     * 初始化Redis订阅器
     */
    @PostConstruct
    public void init() {
        nodeId = getNodeId();
        subscriberExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "workspace-sync-subscriber-" + nodeId);
            t.setDaemon(true);
            return t;
        });
        startRedisSubscriber();
        log.info("WorkspaceSyncSseService 初始化完成，节点ID: {}", nodeId);
    }

    /**
     * 销毁资源
     */
    @PreDestroy
    public void destroy() {
        isSubscriberRunning = false;
        if (subscriberExecutor != null) {
            subscriberExecutor.shutdown();
        }
        log.info("WorkspaceSyncSseService 资源清理完成，节点ID: {}", nodeId);
    }

    /**
     * 获取当前节点ID
     */
    private String getNodeId() {
        String nodeId = System.getenv("NODE_ID");
        if (nodeId == null) {
            nodeId = System.getProperty("node.id", "unknown-" + System.currentTimeMillis());
        }
        return nodeId;
    }

    /**
     * 添加一个新的 SseEmitter 到指定的工作空间ID的连接列表。
     * 当连接完成、超时或发生错误时，会自动从列表中移除。
     *
     * @param workspaceId 工作空间ID，用于标识连接属于哪个工作空间。
     * @param emitter     SseEmitter 实例，代表一个客户端的 SSE 连接。
     */
    public void addEmitter(String workspaceId, SseEmitter emitter) {
        // 获取或创建该 workspaceId 对应的 SseEmitter 列表。
        // computeIfAbsent 方法确保如果 key 不存在，则原子性地创建一个新的 CopyOnWriteArrayList。
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = emitters.computeIfAbsent(workspaceId, k -> new CopyOnWriteArrayList<>());
        workspaceEmitters.add(emitter);
        log.info("为工作空间 {} 添加新的 SSE 连接，当前连接数: {}，节点: {}",
                workspaceId, workspaceEmitters.size(), nodeId);

        // 注册完成回调：当客户端连接正常关闭时触发。
        emitter.onCompletion(() -> {
            workspaceEmitters.remove(emitter); // 从列表中移除该 emitter。
            log.info("工作空间 {} 的 SSE 连接完成，剩余连接数: {}，节点: {}",
                    workspaceId, workspaceEmitters.size(), nodeId);
            // 如果该工作空间没有活跃的连接了，可以考虑清理 ConcurrentHashMap 中的 entry，释放内存。
            if (workspaceEmitters.isEmpty()) {
                emitters.remove(workspaceId);
            }
        });

        // 注册超时回调：当客户端连接在指定时间内没有收到事件时触发。
        emitter.onTimeout(() -> {
            emitter.complete(); // 显式完成连接，这也会触发 onCompletion 回调。
            workspaceEmitters.remove(emitter); // 从列表中移除该 emitter。
            log.warn("工作空间 {} 的 SSE 连接超时，剩余连接数: {}，节点: {}",
                    workspaceId, workspaceEmitters.size(), nodeId);
            if (workspaceEmitters.isEmpty()) {
                emitters.remove(workspaceId);
            }
        });
        
    }

    /**
     * 向指定工作空间ID的所有活跃 SSE 连接推送同步状态更新。
     * 多节点部署优化：同时发布Redis消息，确保所有节点都能收到状态更新。
     *
     * @param workspaceId 工作空间ID，用于确定向哪些客户端推送。
     * @param status      同步状态字符串 (例如: INITIAL, SYNCING, COMPLETED, FAILED)。
     */
    public void sendSyncStatus(String workspaceId, String status) {
        // 1. 先发布Redis消息，确保所有节点都能收到
        publishToRedis(workspaceId, status);

        // 2. 本地推送（如果当前节点有连接的话）
        sendSyncStatusToLocalEmitters(workspaceId, status);
    }

    /**
     * 发布消息到Redis，供所有节点订阅
     */
    private void publishToRedis(String workspaceId, String status) {
        try {
            String channel = WORKSPACE_SYNC_CHANNEL_PREFIX + workspaceId;
            JSONObject message = new JSONObject();
            message.put("workspaceId", workspaceId);
            message.put("status", status);
            message.put("timestamp", System.currentTimeMillis());
            message.put("nodeId", nodeId);

            redisManager.publish(channel, message.toJSONString());
            log.debug("发布Redis消息到频道 {}，状态: {}，节点: {}", channel, status, nodeId);
        } catch (Exception e) {
            log.error("发布Redis消息失败，workspaceId: {}，status: {}，节点: {}",
                    workspaceId, status, nodeId, e);
        }
    }

    /**
     * 向本地SSE连接推送状态更新
     */
    private void sendSyncStatusToLocalEmitters(String workspaceId, String status) {
        // 获取指定工作空间ID对应的 SseEmitter 列表。
        CopyOnWriteArrayList<SseEmitter> workspaceEmitters = emitters.get(workspaceId);
        // 如果列表为空或不存在，说明当前节点没有客户端订阅该工作空间的状态。
        if (workspaceEmitters == null || workspaceEmitters.isEmpty()) {
            log.debug("工作空间 {} 在当前节点 {} 没有活跃的 SSE 连接", workspaceId, nodeId);
            return;
        }

        log.info("向工作空间 {} 的 {} 个 SSE 连接推送状态: {}，节点: {}",
                workspaceId, workspaceEmitters.size(), status, nodeId);
        // 遍历所有连接并发送事件。
        // 使用 for-each 循环遍历 CopyOnWriteArrayList 是安全的。
        for (SseEmitter emitter : workspaceEmitters) {
            try {
                // 发送一个名为 "sync-status" 的事件，数据为状态字符串。
                // 客户端的 EventSource 将通过 addEventListener('sync-status', ...) 来监听这个事件。
                emitter.send(SseEmitter.event().name("sync-status").data(status));
            } catch (IOException e) {
                // 如果发送失败，通常意味着客户端已断开连接。
                // 记录错误，但不需要手动移除 emitter，因为 onCompletion/onTimeout 回调会处理。
                log.error("向工作空间 {} 的 SSE 连接发送状态失败，连接可能已断开，节点: {}: {}",
                        workspaceId, nodeId, e.getMessage());
            } catch (Exception e) {
                // 捕获其他可能的异常。
                log.error("向工作空间 {} 的 SSE 连接发送状态时发生未知异常，节点: {}: {}",
                        workspaceId, nodeId, e.getMessage(), e);
            }
        }
    }

    /**
     * 启动Redis订阅器，监听所有工作空间的状态更新
     */
    private void startRedisSubscriber() {
        isSubscriberRunning = true;
        subscriberExecutor.submit(() -> {
            while (isSubscriberRunning) {
                try {
                    log.info("启动Redis订阅器，节点: {}", nodeId);
                    // 订阅所有工作空间的状态更新频道
                    String pattern = WORKSPACE_SYNC_CHANNEL_PREFIX + "*";
                    redisManager.psubscribe(new WorkspaceSyncPubSub(), pattern);
                } catch (Exception e) {
                    log.error("Redis订阅器异常，节点: {}，将在5秒后重试", nodeId, e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            log.info("Redis订阅器已停止，节点: {}", nodeId);
        });
    }

    /**
     * Redis发布订阅处理器
     */
    private class WorkspaceSyncPubSub extends JedisPubSub {
        @Override
        public void onPMessage(String pattern, String channel, String message) {
            try {
                // 解析消息
                JSONObject msgObj = JSONObject.parseObject(message);
                String workspaceId = msgObj.getString("workspaceId");
                String status = msgObj.getString("status");
                String sourceNodeId = msgObj.getString("nodeId");

                // 避免处理自己发送的消息（防止重复推送）
                if (nodeId.equals(sourceNodeId)) {
                    log.debug("忽略来自当前节点的消息，workspaceId: {}，节点: {}", workspaceId, nodeId);
                    return;
                }

                log.debug("收到Redis消息，频道: {}，workspaceId: {}，状态: {}，来源节点: {}，当前节点: {}",
                        channel, workspaceId, status, sourceNodeId, nodeId);

                // 向本地连接推送状态更新
                sendSyncStatusToLocalEmitters(workspaceId, status);

            } catch (Exception e) {
                log.error("处理Redis订阅消息失败，频道: {}，消息: {}，节点: {}",
                        channel, message, nodeId, e);
            }
        }

        @Override
        public void onPSubscribe(String pattern, int subscribedChannels) {
            log.info("成功订阅Redis频道模式: {}，当前订阅数: {}，节点: {}",
                    pattern, subscribedChannels, nodeId);
        }

        @Override
        public void onPUnsubscribe(String pattern, int subscribedChannels) {
            log.info("取消订阅Redis频道模式: {}，剩余订阅数: {}，节点: {}",
                    pattern, subscribedChannels, nodeId);
        }
    }
}
