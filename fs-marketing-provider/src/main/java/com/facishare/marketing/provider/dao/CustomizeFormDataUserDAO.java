package com.facishare.marketing.provider.dao;

import com.facishare.marketing.api.result.EmployeeRankingDataResult;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormBindOtherCrmObject;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.provider.dto.*;
import com.facishare.marketing.provider.dto.conference.ConferenceSubmitContentDTO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.github.mybatis.pagination.Page;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2019/05/21
 **/
public interface CustomizeFormDataUserDAO {

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE "
            + "((object_id = #{objectId} AND object_type = #{objectType}) "
            + " OR (parent_object_id = #{objectId} AND parent_object_type = #{objectType})) "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObject(@Param("objectId") String objectId, @Param("objectType") Integer objectType, @Param("form_usage") Integer form_usage, @Param("page") Page page);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE object_id IN"
            +   "<foreach collection = 'objectIds' index = 'index' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{objectIds[${index}]}"
            +   "</foreach>"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObjects(@Param("objectIds") List<String> objectIds, @Param("form_usage") Integer form_usage, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE ((object_id = #{objectId}\n"
            + " AND object_type = #{objectType}) OR (parent_object_id = #{objectId}\n"
            + " AND parent_object_type = #{objectType}))"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObjectWithOutPage(@Param("objectId") String objectId, @Param("objectType") Integer objectType, @Param("form_usage") Integer form_usage);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObjectsWithOutPage(@Param("objectIds") List<String> objectIds, @Param("form_usage") Integer form_usage);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_activity_id = #{marketingActivityId} "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByMarketingActivityId(@Param("marketingActivityId") String marketingActivityId, @Param("form_usage") Integer form_usage, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} "
            + "AND ((object_id = #{objectId} AND object_type = #{objectType}) OR (parent_object_id = #{objectId} AND parent_object_type = #{objectType})) "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdAndObject(@Param("objectId") String objectId, @Param("objectType") Integer objectType,
                                                                                 @Param("marketingEventId") String marketingEventId, @Param("form_usage") Integer form_usage, @Param("page") Page page);

    @Select("<script>" +
            "select\n" +
            "\tA.*\n" +
            "from\n" +
            "\tcustomize_form_data as B\n" +
            "left join customize_form_data_user as A on A.form_id = B.id\n" +
            "where\n" +
            "\tB.ea = #{ea}\n" +
            "\tand A.marketing_activity_id = #{marketingActivityd}\n" +
            "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByActivityd(@Param("ea") String ea, @Param("marketingActivityd") String marketingActivityd, @Param("page") Page page);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE marketing_event_id = #{marketingEventId}\n"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND A.form_usage=2 "
            + " </if>"
            + "<if test =\"keyword != null\"> AND (A.submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))</if>\n"
            + "<if test=\"status == 0\">\n"
            + " AND A.submit_content->>'phone' NOT IN\n"
            +   "<foreach collection = 'livePhones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{livePhones[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "<if test=\"status == 0\">\n"
            + " AND A.submit_content->>'phone' IN\n"
            +   "<foreach collection = 'livePhones' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{livePhones[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + " ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getLiveViewCustomizeFormDataUserByEventId(@Param("marketingEventId") String marketingEventId, @Param("status")Integer status,
                                                                                @Param("livePhones")List<String> livePhones,
                                                                                @Param("keyword")String keyword, @Param("form_usage") Integer form_usage,  @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE marketing_event_id = #{marketingEventId}\n"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND A.form_usage=2 "
            + " </if>"
            + "<if test =\"keyword != null\"> AND (A.submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))</if>\n"
            + " ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventId(@Param("marketingEventId") String marketingEventId,
                                                                        @Param("keyword")String keyword, @Param("form_usage") Integer form_usage,  @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND marketing_event_id = #{marketingEventId} "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdAndFormId(@Param("formId") String formId, @Param("marketingEventId") String marketingEventId, @Param("form_usage") Integer form_usage, @Param("page") Page page);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE marketing_event_id = #{marketingEventId} "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND A.form_usage=2 "
            + " </if>"
            + "ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdWithOutPage(@Param("marketingEventId") String marketingEventId, @Param("form_usage") Integer form_usage);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND marketing_event_id = #{marketingEventId} "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdAndFormIdWithOutPage(@Param("formId") String formId, @Param("marketingEventId") String marketingEventId, @Param("form_usage") Integer form_usage);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} AND ((object_id = #{objectId} AND object_type = #{objectType}) OR (parent_object_id = #{objectId} AND parent_object_type = #{objectType}))"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdAndObjectWithOutPage(@Param("objectId") String objectId, @Param("objectType") Integer objectType,
                                                                                            @Param("marketingEventId") String marketingEventId, @Param("form_usage") Integer form_usage);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_activity_id = #{marketingActivityId}"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByMarketingActivityIdWithOutPage(@Param("marketingActivityId") String marketingActivityId, @Param("form_usage") Integer form_usage);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId}"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " <if test='sourceType!=null'>"
            + "  AND source_type = #{sourceType}"
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByFormId(@Param("formId") String formId, @Param("form_usage") Integer form_usage, @Param("sourceType") Integer sourceType, @Param("page") Page page);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId}"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " <if test='sourceType!=null'>"
            + "  AND source_type = #{sourceType}"
            + " </if>"
            + " order by create_time desc"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByFormIdWithOutPage(@Param("formId") String formId, @Param("form_usage") Integer form_usage, @Param("sourceType") Integer sourceType);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE ea=#{ea} AND form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')' index = 'num'>"
            + "#{formIds[${num}]}"
            + "</foreach>"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " <if test='sourceType!=null'>"
            + "  AND source_type = #{sourceType}"
            + " </if>"
            + " <if test =\"keyword != null\"> "
            + "  AND (submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))"
            + " </if>"
            + " <if test='marketingSourceType!=null'>"
            + "  AND (submit_content->>'marketingSourceType' LIKE CONCAT('%',#{marketingSourceType},'%'))"
            + " </if>"
            + " <if test='marketingSourceName!=null'>"
            + "  AND  submit_content->>'marketingSourceName' IN "
            + "       <foreach collection = 'marketingSourceName' item = 'item' open = '(' separator = ',' close = ')' index = 'num'>"
            + "                 #{marketingSourceName[${num}]}"
            + "       </foreach>"
            + " </if>"
            + " <if test='saveCrmStatus!=null'>"
            + "  AND save_crm_status IN "
            + " <foreach collection = 'saveCrmStatus' item = 'item' open = '(' separator = ',' close = ')' index = 'num'>"
            + "   #{saveCrmStatus[${num}]}"
            + " </foreach>"
            + " </if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByFormIdsAndSource(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("form_usage") Integer form_usage, @Param("sourceType") Integer sourceType,
                                                                                 @Param("page") Page page, @Param("keyword") String keyword, @Param("saveCrmStatus") List<Integer> saveCrmStatus,
                                                                                 @Param("marketingSourceType") String marketingSourceType, @Param("marketingSourceName") List<String> marketingSourceName);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE form_id IN "
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='form_usage!=null and form_usage==2'>"
        + "  AND form_usage=2 "
        + " </if>"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByFormIdsAndSourceWithOutPage(@Param("formIds") List<String> formIds, @Param("form_usage") Integer form_usage, @Param("sourceType") Integer sourceType);


    @Select("<script>"
            + "SELECT count(1) FROM customize_form_data_user WHERE object_id = #{objectId} AND object_type = #{objectType}"
            + "</script>")
    Integer getCustomizeFormDataUserCountByObject(@Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + " SELECT * FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND A.id IS NOT NULL \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + " ) AS T"
            + "</script>")
    Integer countClueNumByEa(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
            + " SELECT count(*) FROM customize_form_data_user "
            + " WHERE ea = #{ea}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND create_time &gt;= #{startDate}\n"
            + " AND create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + "</script>")
    Integer countClueByEaWithMarketingActivity(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT T.lead_id FROM (\n"
            + " SELECT * FROM customize_form_data AS B JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + " ) AS T"
            + "</script>")
    List<String> getClueIdByEaWithMarketingActivity(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT * FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid = #{fsUserId} \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T"
            + "</script>")
    Integer countClueNumByFsUserId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
            + "SELECT fs_uid,count(1) FROM\n"
            + "(\n"
            + "SELECT spread_fs_uid AS fs_uid FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid IN \n"
            + "<foreach collection = 'fsUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T\n"
            + "GROUP BY fs_uid "
            + "ORDER BY count DESC"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumByFsUserIds(@Param("ea") String ea, @Param("fsUserIds") List<Integer> fsUserIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT marketing_activity_id, count(1) FROM"
            + "(\n"
            + "SELECT marketing_activity_id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid = #{fsUserId} AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T\n"
            + "GROUP BY marketing_activity_id \n"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumByFsUserIdAndMarketingActivity(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId,
                                                                                  @Param("marketingActivityIds") List<String> marketingActivityIds,
                                                                                  @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT marketing_activity_id, count(1) FROM"
            + "(\n"
            + "SELECT marketing_activity_id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{upstreamEa} AND A.out_uid = #{fsUserId} AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + ") AS T\n"
            + "GROUP BY marketing_activity_id \n"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumForPartner(@Param("upstreamEa") String upstreamEa, @Param("fsUserId") String fsUserId,
                                                                                  @Param("marketingActivityIds") List<String> marketingActivityIds);

    @Select("<script>"
            + "SELECT lead_id,save_crm_status FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea}  AND A.marketing_activity_id = #{marketingActivityId} AND A.lead_id IS NOT NULL AND A.lead_id != ''\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + "ORDER BY A.create_time DESC \n"
            + "</script>")
    List<CustomizeFormClueNumDTO> countClueInfoByMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String MarketingActivityId, @Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
            + "SELECT lead_id,save_crm_status FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.marketing_activity_id = #{marketingActivityId} AND A.spread_fs_uid = #{fsUserId} AND A.lead_id IS NOT NULL AND A.lead_id != ''\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + " ORDER BY A.create_time DESC\n"
            + "</script>")
    List<CustomizeFormClueNumDTO> countClueInfoByMarketingActivityIdAndFsUserId(@Param("ea") String ea, @Param("marketingActivityId") String MarketingActivityId, @Param("fsUserId") Integer fsUserId,
                                                                                @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT * FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.marketing_activity_id = #{marketingActivityId}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T"
            + "</script>")
    Integer countClueNumByMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String MarketingActivityId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT * FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{upstreamEa} AND A.marketing_activity_id = #{marketingActivityId}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T"
            + "</script>")
    Integer countClueNumByMarketingActivityIdForPartner(@Param("upstreamEa") String upstreamEa, @Param("marketingActivityId") String MarketingActivityId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);



    @Select("<script>"
            + "SELECT marketing_activity_id,count(1) FROM\n"
            + "(\n"
            + "SELECT marketing_activity_id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T\n"
            + "GROUP BY marketing_activity_id "
            + "ORDER BY count DESC"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumByMarketingActivityIds(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);



    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT * FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid = #{fsUserId} AND A.marketing_activity_id = #{marketingActivityId} AND A.lead_id IS NOT NULL AND A.lead_id != '' \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T"
            + "</script>")
    Integer countClueNumByFsUserIdAndMarketingActivityId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("marketingActivityId") String marketingActivityId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT * FROM (\n"
            + "SELECT A.id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid = #{fsUserId} AND A.marketing_activity_id = #{marketingActivityId} \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + "ORDER BY A.create_time desc) AS T"
            + "</script>")
    List<String> getFormDataUserIdByFsUserIdAndMarketingActivityId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("marketingActivityId") String marketingActivityId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
            + "SELECT fs_uid,count(1) FROM\n"
            + "(\n"
            + "SELECT spread_fs_uid AS fs_uid FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.marketing_activity_id = #{marketingActivityId} AND A.spread_fs_uid IN \n"
            + "<foreach collection = 'fsUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T\n"
            + "GROUP BY fs_uid "
            + "ORDER BY count DESC"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumByFsUserIdsAndMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String MarketingActivityId, @Param("fsUserIds") List<Integer> fsUserIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
            + "SELECT fs_uid,count(1) FROM\n"
            + "(\n"
            + "SELECT spread_fs_uid AS fs_uid FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} AND A.marketing_activity_id = #{marketingActivityId}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + ") AS T\n"
            + "GROUP BY fs_uid "
            + "ORDER BY count DESC"
            + "</script>")
    List<CustomizeFormClueNumDTO> batchCountClueNumByMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String MarketingActivityId, @Param("fsUserIds") List<Integer> fsUserIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
            + "SELECT * FROM (\n"
            + "SELECT T1.userId,T1.outUserId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount ,T1.lookUpCount,T1.forwardCount FROM\n"
            + "(SELECT fs_user_id AS userId,null as outUserId, spread_count as spreadCount,look_up_count as lookUpCount,forward_count as forwardCount FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}\n"
            + "UNION all\n"
            + "SELECT user_id AS userId,out_user_id as outUserId, -1 as spreadCount ,-1 as lookUpCount,-1 as forwardCount FROM spread_task WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} AND user_id NOT IN\n"
            + "(SELECT fs_user_id FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}))AS T1\n"
            + "LEFT JOIN(SELECT A.spread_fs_uid AS userId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY userId )AS T2 ON T1.userId = T2.userId\n"
            + "ORDER BY clueCount DESC,lookUpCount DESC, spreadCount DESC ,forwardCount DESC) AS T4\n"
            + "<if test='employeeRangeStr != null'>"
            + "where "
            + "string_to_array(T4.userId::text, ',') <![CDATA[ && ]]> string_to_array(#{employeeRangeStr}, ',')\n"
            + "</if>"
            + "</script>")
    List<MarketingActivityEmployeedsByClueDTO> queryPageMarketingActivityEmployeedsByClueCount(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId, @Param("page") Page page,
                                                                                               @Param("dingTalkVersion") Boolean dingTalkVersion,
                                                                                               @Param("employeeRangeStr") String employeeRangeStr
    );

    @Select("<script>"
            + "select * from (\n"
            + "select T1.*,coalesce(clueCount, 0) clueCount from \n"
                + "("
                + "select task.user_id userId,task.out_user_id  outUserId,coalesce(statistic.spread_count, 0) spreadCount,coalesce(statistic.look_up_count, 0) lookUpCount,coalesce(statistic.forward_count, 0) forwardCount\n"
                + "  from spread_task task left join marketing_activity_employee_statistic statistic \n"
                + "on task.ea = statistic.ea  and task.user_id = statistic.fs_user_id and statistic.marketing_activity_id = task.marketing_activity_id\n"
                + "where task.ea = #{ea} AND task.marketing_activity_id = #{marketingActivityId} \n"
                + ") as T1 \n"
            + "left join \n"
                + "(\n"
                + "select spread_fs_uid userId, count(*) clueCount from customize_form_data_user  \n"
                + "where ea = #{ea} and marketing_activity_id =#{marketingActivityId} \n"
                + "group by userId \n"
                + ") AS T2 ON T1.userId = T2.userId \n"
            + ") T3 where 1 = 1 \n"
            + "<if test='employeeRangeStr != null'>"
            + " and string_to_array(T3.userId::text, ',') <![CDATA[ && ]]> string_to_array(#{employeeRangeStr}, ',')\n"
            + "</if>"
            + "<if test='promoteStatus != null and promoteStatus == 1'>"
            + " and <![CDATA[ spreadCount <= 0 ]]>\n"
            + "</if>"
            + "<if test='promoteStatus != null and promoteStatus == 2'>"
            + " and <![CDATA[ spreadCount > 0 ]]>\n"
            + "</if>"
            + " order by clueCount desc, lookUpCount desc, spreadCount desc, forwardCount desc"
            + "</script>")
    List<MarketingActivityEmployeedsByClueDTO> queryPageMarketingActivityEmployeesByClueCountV2(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId, @Param("page") Page page,
                                                                                               @Param("promoteStatus") Integer promoteStatus, @Param("employeeRangeStr") String employeeRangeStr
    );


    @Select("<script>"
            + "SELECT * FROM (\n"
            + "SELECT T1.outerTenantId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount FROM\n"
            + "(SELECT ea AS outerTenantId, sum(spread_count) as spreadCount FROM marketing_activity_employee_statistic WHERE up_stream_ea=#{upstreamEa} AND marketing_activity_id=#{marketingActivityId} group by ea\n"
            + "UNION\n"
            + "SELECT distinct(ea) AS outerTenantId, -1 as spreadCount FROM spread_task WHERE up_stream_ea=#{upstreamEa} AND marketing_activity_id=#{marketingActivityId} AND ea NOT IN\n"
            + "(SELECT ea FROM marketing_activity_employee_statistic WHERE up_stream_ea=#{upstreamEa} AND marketing_activity_id=#{marketingActivityId}))AS T1\n"
            + "LEFT JOIN(SELECT A.out_tenant_id  AS outerTenantId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY outerTenantId )AS T2 ON T1.outerTenantId = T2.outerTenantId\n"
            + "ORDER BY clueCount DESC, spreadCount DESC) AS T4 WHERE 1=1\n"
            + "</script>")
    List<MarketingActivityCompanyByClueDTO> queryPageMarketingActivityCompanyClueCount(@Param("ea")String ea,@Param("upstreamEa")String upstreamEa, @Param("marketingActivityId")String marketingActivityId, @Param("page")Page page);

    //已推广的用户union未推广的用户得到推广数 left join 报名数据表得到线索数量
    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT T1.userId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount FROM\n"
            + "(SELECT fs_user_id AS userId, spread_count as spreadCount FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}\n"
            + "UNION all\n"
            + "SELECT user_id AS userId, -1 as spreadCount FROM spread_task WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} AND user_id NOT IN\n"
            + "(SELECT fs_user_id FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}))AS T1\n"
            + "LEFT JOIN(SELECT A.spread_fs_uid AS userId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY userId )AS T2 ON T1.userId = T2.userId\n"
            + "ORDER BY clueCount DESC, spreadCount DESC) AS T4\n"
            + "<if test='employeeRangeStr != null'>"
            + "where "
            + "string_to_array(T4.userId::text, ',') <![CDATA[ && ]]> string_to_array(#{employeeRangeStr}, ',')\n"
            + "</if>"
            + "</script>")
    int queryCountMarketingActivityEmployeedsByClueCount(@Param("ea")String ea, @Param("marketingActivityId")String marketingActivityId, @Param("employeeRangeStr") String employeeRangeStr);

    //已推广的用户union未推广的用户得到推广数 left join 报名数据表得到线索数量
    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + "SELECT T1.userId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount FROM\n"
            + "(SELECT fs_user_id AS userId, spread_count as spreadCount FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}\n"
            + "UNION\n"
            + "SELECT user_id AS userId, -1 as spreadCount FROM spread_task WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} AND user_id NOT IN\n"
            + "(SELECT fs_user_id FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}))AS T1\n"
            + "LEFT JOIN(SELECT A.out_ AS userId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY userId )AS T2 ON T1.userId = T2.userId\n"
            + "ORDER BY clueCount DESC, spreadCount DESC) AS T4 WHERE 1=1\n"
            + "</script>")
    int queryCountMarketingActivityCompanyByClueCount(@Param("ea")String ea, @Param("marketingActivityId")String marketingActivityId);

    @Select("<script>"
            + "        SELECT marketing_activity_id,count(1) FROM\n"
            + "        (\n"
            + "        SELECT marketing_activity_id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "        WHERE B.ea = #{ea} AND A.marketing_activity_id IS NOT NULL AND A.marketing_activity_id != ''\n"
            + "        <if test=\"needFilter == true\">\n"
            + "        AND A.create_time &gt;= #{startDate}\n"
            + "        AND A.create_time &lt;= #{endDate}\n"
            + "        </if>\n"
            + "        ) AS T\n"
            + "        GROUP BY marketing_activity_id\n"
            + "        ORDER BY count DESC"
            + "        LIMIT #{limit}"
            + "        </script>")
    List<CustomizeFormClueNumDTO> countMarketingActivityClueNumByEa(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter, @Param("limit") Integer limit);


    @Select("      <script>"
            + "        SELECT fs_uid,count(1) FROM\n"
            + "        (\n"
            + "        SELECT spread_fs_uid AS fs_uid FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "        WHERE B.ea = #{ea} AND A.spread_fs_uid IS NOT NULL AND A.marketing_activity_id IS NOT NULL AND A.marketing_activity_id != ''\n"
            + "        <if test=\"needFilter == true\">\n"
            + "        AND A.create_time &gt;= #{startDate}\n"
            + "        AND A.create_time &lt;= #{endDate}\n"
            + "        </if>\n"
            + "        ) AS T\n"
            + "        GROUP BY fs_uid\n"
            + "        ORDER BY count DESC"
            + "        LIMIT #{limit}"
            + "        </script>")
    List<CustomizeFormClueNumDTO> countFsUserIdClueNumByEa(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter, @Param("limit") Integer limit);


    @Select("<script>"
            + "SELECT A.marketing_activity_id, count(1) as count FROM customize_form_data_user AS A \n"
            + "JOIN marketing_activity_external_config AS B ON A.marketing_activity_id = B.marketing_activity_id\n"
            + "WHERE B.ea = #{ea} \n"
            + "   <if test=\"needFilter == true\">\n"
            + "        AND A.create_time &gt;= #{startDate}\n"
            + "        AND A.create_time &lt;= #{endDate}\n"
            + "   </if>\n"
            + "   <if test=\"marketingActivityType != null\">\n"
            + "        AND B.marketing_activity_type = #{marketingActivityType}\n"
            + "   </if>\n"
            +     "<if test=\"ids != null and ids.size > 0\">"
            + "    AND B.marketing_activity_id IN\n"
            + "    <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "    #{ids[${index}]} "
            + "    </foreach>"
            +     "</if>"
            + "GROUP BY A.marketing_activity_id\n"
            + "ORDER BY count desc\n"
            + "LIMIT #{limit}"
            + "OFFSET #{offset}"
            + "</script>")
    List<CustomizeFormClueNumDTO> countMarketingActivityClueNumByEaAndType(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter, @Param("marketingActivityType") Integer marketingActivityType, @Param("limit") int limit, @Param("offset") int offset,@Param("ids") List<String> ids );

    @Select("<script>"
            + "SELECT A.spread_fs_uid as fsUid, count(1) as count FROM customize_form_data_user AS A \n"
            + "JOIN marketing_activity_external_config AS B ON A.marketing_activity_id = B.marketing_activity_id\n"
            + "WHERE B.ea = #{ea} AND A.spread_fs_uid IS NOT NULL\n"
            + "   <if test=\"needFilter == true\">\n"
            + "        AND A.create_time &gt;= #{startDate}\n"
            + "        AND A.create_time &lt;= #{endDate}\n"
            + "   </if>\n"
            + "   <if test=\"marketingActivityType != null\">\n"
            + "        AND B.marketing_activity_type = #{marketingActivityType}\n"
            + "   </if>\n"
            +     "<if test=\"ids != null and ids.size > 0\">"
            + "    AND B.marketing_activity_id IN\n"
            + "    <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "    #{ids[${index}]} "
            + "    </foreach>"
            +     "</if>"
            +     "<if test=\"employeeIds != null and employeeIds.size > 0\">"
            + "    AND A.spread_fs_uid IN\n"
            + "    <foreach collection=\"employeeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "    #{employeeIds[${index}]} "
            + "    </foreach>"
            +     "</if>"
            + "GROUP BY A.spread_fs_uid\n"
            + "ORDER BY count desc\n"
            + "LIMIT #{limit}"
            + "OFFSET #{offset}"
            + "</script>")
    List<CustomizeFormClueNumDTO> countFsUidClueNumByEaAndType(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter, @Param("marketingActivityType") Integer marketingActivityType, @Param("limit") int limit, @Param("offset") int offset, @Param("ids") List<String> ids  ,@Param("employeeIds") List<Integer> employeeIds);

    @Select("SELECT * FROM customize_form_data_user WHERE open_id = #{openId} AND wx_app_id = #{wxAppId} AND form_id = #{formId} AND object_id = #{objectId} AND object_type = #{objectType} ORDER BY create_time DESC")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByOpenId(@Param("marketingEventId") String marketingEventId, @Param("openId") String openId, @Param("wxAppId") String wxAppId, @Param("formId") String formId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM customize_form_data_user WHERE open_id = #{openId} AND wx_app_id = #{wxAppId} AND form_id = #{formId} AND parent_object_id = #{parentObjectId} AND parent_object_type = #{parentObjectType} ORDER BY create_time DESC")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByOpenIdAndParentObjectId(@Param("openId") String openId, @Param("wxAppId") String wxAppId, @Param("formId") String formId, @Param("parentObjectId") String parentObjectId, @Param("parentObjectType") Integer parentObjectType);

    @Select("SELECT * FROM customize_form_data_user WHERE uid = #{uid} AND form_id = #{formId} AND parent_object_id = #{parentObjectId} AND parent_object_type = #{parentObjectType} ORDER BY create_time DESC")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByUidAndParentObjectId(@Param("uid") String uid, @Param("formId") String formId, @Param("parentObjectId") String parentObjectId, @Param("parentObjectType") Integer parentObjectType);

    @Select("<script>" +
            "SELECT * " +
            "FROM customize_form_data_user " +
            "WHERE enroll_user_ea = #{ea} AND enroll_user_fs_uid = #{fsUserId} AND form_id = #{formId} AND object_id = #{objectId} AND object_type = #{objectType}" +
            "<if test=\"marketingEventId != null and marketingEventId != ''\">" +
            "  AND marketing_event_id = #{marketingEventId}" +
            "</if>" +
            "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByEaAndFsUserId(@Param("marketingEventId") String marketingEventId, @Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("formId") String formId,
                                                                                @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>" +
            "SELECT * " +
            "FROM customize_form_data_user " +
            "WHERE enroll_user_ea = #{ea} AND enroll_user_fs_uid = #{fsUserId} AND form_id = #{formId} AND parent_object_id = #{parentObjectId} AND parent_object_type = #{parentObjectType}" +
            "<if test=\"marketingEventId != null and marketingEventId != ''\">" +
            "  AND marketing_event_id = #{marketingEventId}" +
            "</if>" +
            "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByFsUserIdAndParentObjectId(@Param("marketingEventId") String marketingEventId, @Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("formId") String formId,
                                                                                @Param("parentObjectId") String parentObjectId, @Param("parentObjectType") Integer parentObjectType);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE open_id = #{openId} AND wx_app_id = #{wxAppId} AND form_id = #{formId} AND object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<if test=\"phone != null\">"
            + "  AND submit_content ->> 'phone' = #{phone}"
            + "</if>\n"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + "  AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByOpenIdAndObjectIds(@Param("marketingEventId") String marketingEventId, @Param("openId") String openId, @Param("wxAppId") String wxAppId, @Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} "
            + "<if test=\"objectIds != null and objectIds.size()>0\">"
            + "<foreach collection = 'objectIds' item = 'item' open = 'AND object_id IN (' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</if>"
            + "<if test=\"phone != null\">"
            + "  AND submit_content ->> 'phone' = #{phone}"
            + "</if>"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + "  AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByObjectIds(@Param("marketingEventId") String marketingEventId, @Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);


    @Select("<script>"
        + " SELECT * FROM customize_form_data_user WHERE open_id = #{openId} AND wx_app_id = #{wxAppId} AND marketing_event_id = #{marketingEventId}"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByOpenIdAndEventId(@Param("openId") String openId, @Param("wxAppId") String wxAppId, @Param("marketingEventId") String marketingEventId);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND finger_print = #{fingerPrint} AND object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<if test=\"phone != null\">"
            + "  AND submit_content ->> 'phone' = #{phone}"
            + "</if>\n"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + "  AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByFingerPrintAndObjectIds(@Param("marketingEventId") String marketingEventId, @Param("fingerPrint") String fingerPrint, @Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND finger_print = #{fingerPrint} AND parent_object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<if test=\"phone != null\">"
            + "  AND submit_content ->> 'phone' = #{phone}"
            + "</if>\n"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + "  AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByFingerPrintAndParentObjectIds(@Param("marketingEventId") String marketingEventId, @Param("fingerPrint") String fingerPrint, @Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);


    @Select("<script>"
        + " SELECT * FROM customize_form_data_user WHERE finger_print = #{fingerPrint} AND marketing_event_id = #{marketingEventId}"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByFingerPrintAndEventId(@Param("fingerPrint") String fingerPrint, @Param("marketingEventId") String marketingEventId);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND enroll_user_fs_uid = #{enrollUserFsUid} AND enroll_user_ea = #{enrollUserEa} "
            + "<if test=\"objectIds != null and objectIds.size() > 0\">"
            + " AND object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</if>\n"
            + "<if test=\"phone != null\">"
            + "  AND submit_content ->> 'phone' = #{phone}"
            + "</if>\n"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + "  AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByFsUserInfoAndObjectIds(@Param("marketingEventId") String marketingEventId, @Param("enrollUserEa") String enrollUserEa, @Param("enrollUserFsUid") Integer enrollUserFsUid,
                                                                                          @Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);

    @Select("<script>"
        + " SELECT * FROM customize_form_data_user WHERE enroll_user_fs_uid = #{enrollUserFsUid} AND enroll_user_ea = #{enrollUserEa} AND marketing_event_id = #{marketingEventId}"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByFsUserInfoAndEventId(@Param("enrollUserEa") String enrollUserEa, @Param("enrollUserFsUid") Integer enrollUserFsUid, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND submit_content ->> 'phone' = #{phone} AND object_id IN"
        + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "ORDER BY create_time DESC"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByObjectIdsAndPhone(@Param("formId") String formId, @Param("objectIds") List<String> objectIds, @Param("phone") String phone);


    @Insert("INSERT INTO customize_form_data_user (\n"
            + "        \"id\",\n"
            + "        \"uid\",\n"
            + "        \"form_id\",\n"
            + "        \"object_id\",\n"
            + "        \"object_type\",\n"
            + "        \"parent_object_id\",\n"
            + "        \"parent_object_type\",\n"
            + "        \"submit_content\",\n"
            + "        \"marketing_activity_id\",\n"
            + "        \"save_crm_status\",\n"
            + "        \"save_crm_error_message\",\n"
            + "        \"create_time\",\n"
            + "        \"spread_fs_uid\",\n"
            + "        \"lead_id\",\n"
            + "        \"open_id\",\n"
            + "        \"wx_app_id\",\n"
            + "        \"source_type\",\n"
            + "        \"finger_print\",\n"
            + "        \"enroll_user_fs_uid\",\n"
            + "        \"enroll_user_ea\",\n"
            + "        \"marketing_event_id\",\n"
            + "        \"extra_data_id\",\n"
            + "        \"out_tenant_id\",\n"
            + "        \"ip_addr\",\n"
            + "        \"user_agent\",\n"
            + "        \"channel_value\",\n"
            + "        \"out_uid\",\n"
            + "        \"spread_user_identify_id\",\n"
            + "        \"from_user_marketing_id\",\n"
            + "        \"ea\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.uid},\n"
            + "        #{obj.formId},\n"
            + "        #{obj.objectId},\n"
            + "        #{obj.objectType},\n"
            + "        #{obj.parentObjectId},\n"
            + "        #{obj.parentObjectType},\n"
            + "        #{obj.submitContent, typeHandler=com.facishare.marketing.common.typehandlers.CustomizeFormDataEnrollTypeHandler},\n"
            + "        #{obj.marketingActivityId},\n"
            + "        #{obj.saveCrmStatus},\n"
            + "        #{obj.saveCrmErrorMessage},\n"
            + "        now(),\n"
            + "        #{obj.spreadFsUid},\n"
            + "        #{obj.leadId},\n"
            + "        #{obj.openId},\n"
            + "        #{obj.wxAppId},\n"
            + "        #{obj.sourceType},\n"
            + "        #{obj.fingerPrint},\n"
            + "        #{obj.enrollUserFsUid},\n"
            + "        #{obj.enrollUserEa},\n"
            + "        #{obj.marketingEventId},\n"
            + "        #{obj.extraDataId},\n"
            + "        #{obj.outTenantId},\n"
            + "        #{obj.ipAddr},\n"
            + "        #{obj.userAgent},\n"
            + "        #{obj.channelValue},\n"
            + "        #{obj.outUid},\n"
            + "        #{obj.spreadUserIdentifyId},\n"
            + "        #{obj.fromUserMarketingId},\n"
            + "        #{obj.ea}\n"
            + "        )")
    int insertCustomizeFormDataUser(@Param("obj") CustomizeFormDataUserEntity customizeFormDataUserEntity);

    @Update("UPDATE customize_form_data_user SET create_time=#{createTime} WHERE id=#{id}")
    int updateCustomizeFormDataUser(@Param("id")String id, @Param("createTime")Date createTime);

    @Update(" UPDATE customize_form_data_user SET submit_content = #{obj.submitContent, typeHandler=com.facishare.marketing.common.typehandlers.CustomizeFormDataEnrollTypeHandler} WHERE id = #{obj.id}")
    int updateCustomizeFormDataEnrollDataById(@Param("obj") CustomizeFormDataUserEntity customizeFormDataUserEntity);

    @Update(" UPDATE customize_form_data_user SET pay_order_id = #{orderId}, form_usage = 2 WHERE id = #{id}")
    int updateCustomizeFormDataUserOrderIdById(@Param("orderId") String orderId, @Param("id") String id);

    @Update("  UPDATE customize_form_data_user\n"
        + "    SET extra_data_id = #{extraDataId}\n"
        + "    WHERE id = #{id}")
    int setCustomizeFormDataUserExtraDataId(@Param("id") String id, @Param("extraDataId") String extraDataId);


    @Update(" UPDATE customize_form_data_user\n"
            + "    SET save_crm_status = #{saveCrmStatus},save_crm_error_message =#{saveCrmMessage},lead_id = #{leadId}\n"
            + "    WHERE id = #{id} and other_crm_object_bind is null")
    int updateCustomizeFormDataUserCrmMessage(@Param("id") String id, @Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmMessage") String saveCrmMessage, @Param("leadId") String leadId);

    @Update("<script>"
            + " UPDATE customize_form_data_user "
            + "    SET save_crm_status = #{saveCrmStatus},save_crm_error_message =#{saveCrmMessage},lead_id = #{leadId}"
            + "    WHERE id in "
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            + " #{item}"
            + "</foreach>"
            + " and other_crm_object_bind is null"
            + "</script>")
    int updateCustomizeFormDataUserCrmMessageWithIdList(@Param("idList") List<String> idList, @Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmMessage") String saveCrmMessage, @Param("leadId") String leadId);


    @Update(" UPDATE customize_form_data_user SET save_crm_status = #{saveCrmStatus}, other_crm_object_bind = #{otherCrmObjectBind, typeHandler=com.facishare.marketing.common.typehandlers.CustomizeFormBindOtherCrmObjectTypeHandler} WHERE id = #{id}")
    int updateCustomizeFormDataUserCrmObjectBind(@Param("saveCrmStatus") Integer saveCrmStatus, @Param("id") String id, @Param("otherCrmObjectBind") CustomizeFormBindOtherCrmObject otherCrmObjectBind);

    @Select("SELECT A.* FROM customize_form_data AS A LEFT JOIN customize_form_data_user AS B\n"
            + "ON A.id = B.form_id WHERE A.ea = #{ea} AND B.marketing_activity_id = #{marketingActivityId}\n"
            + "ORDER BY B.create_time DESC LIMIT 1")
    CustomizeFormDataEntity getLatestEnrollDataFormByMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId);


    @Select("SELECT A.* FROM customize_form_data AS A LEFT JOIN customize_form_data_user AS B\n"
            + "ON A.id = B.form_id WHERE A.ea = #{ea} AND B.object_id = #{objectId} AND B.object_type = #{objectType} AND B.marketing_event_id = #{marketingEventId}\n"
            + "ORDER BY B.create_time DESC LIMIT 1")
    CustomizeFormDataEntity getLatestEnrollDataFormByEventIdAndObject(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT A.* from customize_form_data AS A LEFT JOIN customize_form_data_user AS B\n"
            + "ON A.id = B.form_id WHERE A.ea = #{ea} AND B.object_id = #{objectId} AND object_type = #{objectType}\n"
            + "ORDER BY B.create_time DESC LIMIT 1")
    CustomizeFormDataEntity getLatestEnrollDataFormByObject(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);


    @Select("SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND object_id = #{objectId} AND object_type = #{objectType} AND finger_print = #{fingerPrint} ORDER BY create_time DESC")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByFingerPrint(@Param("fingerPrint") String fingerPrint, @Param("formId") String formId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>" +
            "SELECT * FROM customize_form_data_user " +
            "WHERE form_id = #{formId} AND object_id = #{objectId} AND object_type = #{objectType} AND enroll_user_fs_uid = #{enrollUserFsUid} " +
            "<if test=\"marketingEventId != null and marketingEventId != ''\">" +
            "  AND marketing_event_id = #{marketingEventId}" +
            "</if>" +
            "AND enroll_user_ea = #{enrollUserEa} ORDER BY create_time DESC" +
            "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByFsUserInfo(@Param("marketingEventId")String marketingEventId, @Param("enrollUserEa") String enrollUserEa, @Param("enrollUserFsUid") Integer enrollUserFsUid, @Param("formId") String formId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND submit_content ->> 'phone' = #{phone} AND object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByPhone(@Param("phone") String phone, @Param("formId") String formId, @Param("objectIds") List<String> objectIds);

    @Select("<script>" +
            "SELECT source_type, COALESCE(COUNT(*), 0) as count FROM customize_form_data_user " +
            "WHERE marketing_event_id = #{marketingEventId} " +
            "GROUP BY source_type" +
            "</script>")
    List<MarketingActivityLeadSourceCountDTO> countLeadSourceByMarketingEventId(@Param("marketingEventId")String marketingEventId);

    @Select("<script>" +
            "SELECT COALESCE(COUNT(*), 0) as count FROM customize_form_data_user " +
            "WHERE marketing_event_id = #{marketingEventId}  " +
            "</script>")
    int countLeadByMarketingEventId(@Param("marketingEventId") String marketingEventId);

    @Select("<script>" +
            "SELECT COALESCE(COUNT(*), 0) as count FROM customize_form_data_user " +
            "WHERE marketing_event_id = #{marketingEventId} AND (object_id=#{objectId} OR parent_object_id=#{objectId})" +
            "</script>")
    int countLeadByMarketingEventIdAndObjectId(@Param("marketingEventId") String marketingEventId, @Param("objectId") String objectId);

    @Select("<script>"
            + "SELECT COALESCE(COUNT(*), 0) as count, object_id AS objectId FROM customize_form_data_user\n"
            + "WHERE marketing_event_id = #{marketingEventId} AND object_id in\n"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
                + "#{item}"
            + "</foreach>"
            + " group by objectId\n"
            + "</script>")
    List<CustomizeFormUserDataCountByObjectIdDTO> batchCountByMarketingEventIdAndObjectIds(@Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);

    @Select("<script>"
            + "SELECT COALESCE(COUNT(*), 0) as count, object_id AS objectId FROM customize_form_data_user\n"
            + "WHERE marketing_event_id = #{marketingEventId} AND parent_object_id in\n"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " group by objectId\n"
            + "</script>")
    List<CustomizeFormUserDataCountByObjectIdDTO> batchCountByMarketingEventIdAndParentObjectIds(@Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);



    @Select("<script>" +
            "SELECT COALESCE(COUNT(*), 0) as count FROM customize_form_data_user " +
            "WHERE marketing_event_id = #{marketingEventId} AND parent_object_id=#{parentObjectId}" +
            "</script>")
    int countLeadByMarketingEventIdAndParentObjectId(@Param("marketingEventId") String marketingEventId, @Param("parentObjectId") String parentObjectId);

    @Select("<script>"
            + "SELECT form_id,count(1) FROM customize_form_data_user WHERE form_id IN\n"
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach> "
            + "GROUP BY form_id"
            + "</script>")
    List<CustomizeFormDataUserStatisticDTO> queryCustomizeFormDataUserStatistic(@Param("ids") List<String> formIds);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE id IN"
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserEntityByIds(@Param("ids")List<String> ids);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user\n"
        + "WHERE ((save_crm_status != 0 and save_crm_status != 3) OR save_crm_status is null) AND id IN "
        + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<CustomizeFormDataUserEntity> querySaveFailedCrmEntityByIds(@Param("ids") List<String> ids);


    @Select("SELECT * from customize_form_data_user where id = #{id}")
    CustomizeFormDataUserEntity getCustomizeFormDataUserById(@Param("id") String id);


    @Select("<script>"
            + "SELECT C.* FROM activity_enroll_data A JOIN campaign_merge_data B ON A.form_data_user_id = B.id JOIN customize_form_data_user C ON C.campaign_id = B.id\n"
            +  "WHERE A.id IN"
            +   "<foreach collection = 'conferenceEnrollIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "   <if test=\"reviewStatus != null\">\n"
            + "        AND A.review_status = #{reviewStatus}\n"
            + "   </if>\n"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryByConferenceEnrollIds(@Param("conferenceEnrollIds") List<String> conferenceEnrollIds, @Param("reviewStatus") Integer reviewStatus);

    @Select("SELECT count(1) FROM customize_form_data_user WHERE form_id = #{formId} ")
    long countCustomizeFormDataUserByFormId(@Param("formId") String formId);

    @Select("<script>"
            + "SELECT id AS customizeFormDataUserId, submit_content AS submitContent FROM customize_form_data_user WHERE id IN\n"
            +  "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +  "</foreach>"
            + "</script>")
    List<ConferenceSubmitContentDTO> queryFormSubmitContent(@Param("ids")List<String> ids);



    @Select("<script>"
            + "SELECT  A.hexagon_site_id AS objectId, count(1) AS count FROM hexagon_page AS A JOIN customize_form_data_user AS B ON A.id = B.object_id WHERE B.form_id IN \n"
            +  "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +  "</foreach>"
            + " GROUP BY A.hexagon_site_id"
            + "</script>")
    List<CustomizeFormClueNumDTO> queryHexagonSiteClueCount(@Param("ids") List<String> formIds);


    @Select("<script>"
            + "SELECT  A.hexagon_site_id AS objectId, count(1) AS count FROM hexagon_page AS A JOIN customize_form_data_user AS B ON A.form_id = B.form_id WHERE B.form_id IN \n"
            +  "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +  "</foreach>"
            + " GROUP BY A.hexagon_site_id"
            + "</script>")
    List<CustomizeFormClueNumDTO> queryHexagonSiteClueCountV2(@Param("ids") List<String> formIds);

    @Select("<script>"
            + "SELECT A.id AS formId, A.form_head_setting ->> 'name' AS formName, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id WHERE B.form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " GROUP BY A.id, A.form_head_setting ->> 'name'"
            + "</script>")
    List<CustomizeFormClueNumDTO> getFormInfoAndClueNameByForms(@Param("formIds") List<String> formIds);

    @Select("<script>"
            + "SELECT A.id AS formId, A.form_head_setting ->> 'name' AS formName, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id WHERE B.object_id = #{objectId} AND B.object_type = #{objectType} AND B.form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " GROUP BY A.id, A.form_head_setting ->> 'name'"
            + "</script>")
    List<CustomizeFormClueNumDTO> getFormInfoAndClueNameByFormsAndObject(@Param("formIds") List<String> formIds, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("<script>"
            + "SELECT A.id AS formId, A.form_head_setting ->> 'name' AS formName, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id WHERE B.object_id = #{objectId} AND B.object_type = #{objectType} AND B.form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test='startDate != null and endDate != null'>"
            + "  AND B.create_time &gt;= #{startDate} AND B.create_time  &lt;= #{endDate} "
            + " </if>"
            + " GROUP BY A.id, A.form_head_setting ->> 'name'"
            + "</script>")
    List<CustomizeFormClueNumDTO> getFormInfoAndClueNameByFormsAndObjectAndTime(@Param("formIds") List<String> formIds, @Param("objectId") String objectId, @Param("objectType") Integer objectType,
                                                                                @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>"
        + " SELECT form_id AS formId, count(id) AS count FROM customize_form_data_user WHERE ea=#{ea} AND form_id=ANY(ARRAY"
            + "<foreach collection = 'formIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +      "#{item}"
            + "</foreach> )"
        + " AND source_type = #{sourceType} "
        + " GROUP BY form_id"
        + "</script>")
    List<CustomizeFormClueNumDTO> getFormClueCountBySourceType(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType);

    @Select("<script>"
            + " SELECT form_id AS formId, count(id) AS count FROM customize_form_data_user WHERE ea=#{ea} "
            + "AND form_id=ANY(ARRAY"
            + "<foreach collection = 'formIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +      "#{item}"
            + "</foreach> )"
            + " AND source_type = #{sourceType} "
            + " <if test='startDate != null and endDate != null'>"
            + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
            + " </if>"
            + " GROUP BY form_id"
            + "</script>")
    List<CustomizeFormClueNumDTO> getFormClueCountBySourceTypeAndTime(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType,
                                                               @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Delete("DELETE FROM customize_form_data_user WHERE id=#{id}")
    int deleteById(@Param("id")String id);

    @Delete("DELETE FROM customize_form_data_user WHERE marketing_activity_id=#{marketingActivityId}")
    int deleteByMarketingActivityId(@Param("marketingActivityId")String marketingActivityId);

    @Select("SELECT COUNT(*) FROM customize_form_data_user WHERE marketing_event_id=#{marketingEventId} AND create_time <= #{datePoint}")
    int countLeadByMarketingEventIdAndTimePoint(@Param("marketingEventId")String marketingEventId, @Param("datePoint")Date datePoint);

    @Select("<script>"
            + "SELECT id AS phone FROM customize_form_data_user WHERE marketing_event_id=#{marketingEventId} AND submit_content->>'phone' IN"
            + "<foreach collection = 'phones' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            + "</script>")
    List<String> getIdsByContentPhone(@Param("marketingEventId")String marketingEventId, @Param("phones")List<String> phones);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_event_id=#{marketingEventId} AND submit_content->>'phone' IN\n"
            + "<foreach collection = 'phones' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserEntityByContentPhone(@Param("marketingEventId")String marketingEventId, @Param("phones")List<String> phones);

    @Select("SELECT * FROM customize_form_data_user WHERE marketing_event_id=#{marketingEventId} AND submit_content->>'phone'=#{phone}")
    List<CustomizeFormDataUserEntity> getByPhone(@Param("marketingEventId")String marketingEventId, @Param("phone")String phone);

    @Select("SELECT * FROM customize_form_data_user WHERE extra_data_id=#{extraDataId}")
    CustomizeFormDataUserEntity getByExtraDataId(@Param("extraDataId")String extraDataId);

    @Update("UPDATE customize_form_data_user SET pay_order_id=#{payOrderId} WHERE id = #{id}")
    int setPayOrderId(@Param("id") String id, @Param("payOrderId") String payOrderId);

    @Select("SELECT form_head_setting ->> 'name' AS name FROM customize_form_data WHERE id IN (SELECT form_id FROM customize_form_data_user WHERE pay_order_id = #{payOrderId})")
    String getFormNameByPayOrderId(@Param("payOrderId") String payOrderId);

    @Select("SELECT * FROM customize_form_data_user WHERE pay_order_id = #{payOrderId}")
    CustomizeFormDataUserEntity getByPayOrderId(@Param("payOrderId") String payOrderId);

    @Update("<script>"
        + "UPDATE customize_form_data_user SET finger_print = #{fingerPrint}"
        + " WHERE id IN "
        + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>"
    )
    int updateCustomizeFormDataUserFingerPrintByIds(@Param("fingerPrint") String fingerPrint, @Param("ids") List<String> ids);

    @Select("<script>"
        + " SELECT submit_content ->> 'marketingSourceName' AS countName, count(1) AS count FROM customize_form_data_user "
        + " WHERE ea=#{ea} AND form_id=ANY(ARRAY"
        + "<foreach collection = 'formIds' item = 'item' open = '[' separator = ',' close = ']'>"
        +      "#{item}"
        + "</foreach> )"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " GROUP BY countName "
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataUserSourceNameStatistics(@Param("ea") String ea , @Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage,
        @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select("<script>"
        + " SELECT submit_content ->> 'utmTerm' AS countName, count(1) AS count FROM customize_form_data_user "
        + " WHERE submit_content ->> 'utmTerm' is not null AND submit_content ->> 'utmTerm' != ''AND form_id IN "
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " GROUP BY countName "
        + " ORDER BY count desc"
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataUserUtmTermStatistics(@Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage,
        @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>"
        + " SELECT to_char(create_time, 'YYYY-MM-DD') AS simpleTime, submit_content ->> 'marketingSourceName' AS countName, count(1) AS count FROM customize_form_data_user "
        + " WHERE form_id IN "
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " GROUP BY simpleTime, countName ORDER BY simpleTime asc, countName"
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataUserSourceNameDayStatistics(@Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage,
        @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>"
        + " SELECT to_char(create_time, 'YYYY-MM-DD') AS simpleTime, count(1) AS count FROM customize_form_data_user "
        + " WHERE form_id IN "
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " GROUP BY simpleTime ORDER BY simpleTime asc"
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataCountDayStatistics(@Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage,
        @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select("<script>"
        + " SELECT submit_content ->> 'utmCampaig' AS countName, COUNT(1) AS count FROM customize_form_data_user "
        + " WHERE ea=#{ea} "
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + "AND form_id=ANY(ARRAY "
        + "<foreach collection = 'formIds' item = 'item' index='num' open = '[' separator = ',' close = ']'>"
        +     " #{formIds[${num}]}"
        + "</foreach> )"
        + " AND submit_content ->> 'utmCampaig' is not null and submit_content ->> 'utmCampaig' != '' "
        + " GROUP BY submit_content ->> 'utmCampaig'"
        + " ORDER BY count DESC"
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataUtmCampaig(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage, @Param("page")Page page,
                                                                 @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>"
        + "SELECT submit_content ->> 'marketingSourceType' AS countName, count(1) AS count FROM customize_form_data_user"
        + " WHERE ea=#{ea} AND form_id IN "
        + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='sourceType!=null'>"
        + "  AND source_type = #{sourceType}"
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + " GROUP BY countName"
        + "</script>")
    List<CustomizeFormClueNumDTO> getCustomizeFormDataUserSourceTypeStatistics(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage,
        @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>"
        + " SELECT COUNT(id) FROM customize_form_data_user WHERE source_type = #{sourceType} AND marketing_event_id = #{marketingEventId}"
        + " <if test='startDate != null and endDate != null'>"
        + "  AND create_time &gt;= #{startDate} AND create_time  &lt;= #{endDate} "
        + " </if>"
        + "</script>")
    Integer getCustomizeFormDataUserCountByMarketingEventAndSourceType(@Param("marketingEventId") String marketingEventId, @Param("sourceType") Integer sourceType, @Param("form_usage") Integer form_usage, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select("<script>"
        + " SELECT COUNT(id) FROM customize_form_data_user where marketing_activity_id = #{marketingActivityId} AND object_id = #{objectId}"
        + "</script>")
    Integer getCountByMarketingActivityIdAndObjectId(@Param("marketingActivityId") String marketingActivityId, @Param("objectId") String objectId, @Param("form_usage") Integer form_usage);

    @Select("<script>"
            + " select count(*) from customize_form_data_user where marketing_event_id = #{marketingEventId} and save_crm_status in <foreach collection = 'saveCrmStatus' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach>"
            + "</script>")
    Integer getSaveCrmFailLeadsCountByMarketingEventId(@Param("marketingEventId") String marketingEventId, @Param("saveCrmStatus") List<Integer> saveCrmStatus);

    @Select("<script>"
            + " select count(*) from customize_form_data_user where form_id = #{formId} and save_crm_status in <foreach collection = 'saveCrmStatus' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> and marketing_event_id is null"
            + "</script>")
    Integer getSaveCrmFailLeadsCountByFormId(@Param("formId") String formId, @Param("saveCrmStatus") List<Integer> saveCrmStatus);

    @Select("<script>"
            + " select count(*) from customize_form_data_user where marketing_activity_id = #{marketingActivityId} and save_crm_status = #{saveCrmStatus} "
            + "</script>")
    Integer getSaveCrmFailLeadsCountByMarketingActivityId(@Param("marketingActivityId") String marketingActivityId, @Param("saveCrmStatus") Integer saveCrmStatus);


    @Select("<script>"
        + "SELECT count(1) FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
        + " WHERE B.ea = #{ea} AND A.marketing_activity_id IN \n"
        + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    int countByMarketingActivityIds(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds);

    @Select("<script>"
            + "SELECT count(1) FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    int countByMarketingActivityIdsAndDateRange(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);

    @Select("<script>"
            + "SELECT count(1) FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND A.spread_fs_uid IN"
            + "<foreach collection = 'userIds' item = 'userId' open = '(' separator = ',' close = ')'>"
            + "#{userId}"
            + "</foreach>"
            + "</script>")
    int countByMarketingActivityIdsAndUserIdRange(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds,
                                                  @Param("userIds") List<Integer> userIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);


    @Select("<script>"
            + "SELECT count(1) FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{upstreamEa} AND A.out_tenant_id = #{outTenantId} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND A.spread_fs_uid IN"
            + "<foreach collection = 'userIds' item = 'userId' open = '(' separator = ',' close = ')'>"
            + "#{userId}"
            + "</foreach>"
            + "</script>")
    int countByMarketingActivityIdsAndUserIdAndOutTenantIdRange(@Param("upstreamEa") String upstreamEa,@Param("outTenantId") String outTenantId, @Param("marketingActivityIds") List<String> marketingActivityIds,
                                                  @Param("userIds") List<Integer> userIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);


    @Select("<script>"
            + "SELECT A.object_id as id, count(*) as count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND A.object_id IN \n"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " GROUP BY A.object_id"
            + "</script>")
    List<DataCount> groupCountByMarketingObjectIds(@Param("ea") String ea, @Param("objectIds") Collection<String> objectIds);

    @Select("<script>"
            + "SELECT A.parent_object_id as id, count(*) as count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} "
            + " AND A.parent_object_id IN  "
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " GROUP BY A.parent_object_id"
            + "</script>")
    List<DataCount> groupCountByMarketingParentObjectIds(@Param("ea") String ea, @Param("objectIds") Collection<String> objectIds);

    @Select("<script>"
            + "SELECT A.create_time::date as \"date\", count(*) as count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND  (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>" +
            " GROUP BY A.create_time::date"
            + "</script>")
    List<DateCount> dateCountByMarketingActivityIdsAndDateRange(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);

    @Select("<script>"
            + "SELECT A.create_time::date as \"date\", count(*) as count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND (object_id=#{objectId} OR parent_object_id=#{objectId})" +
            " GROUP BY A.create_time::date"
            + "</script>")
    List<DateCount> dateCountByMarketingObjectIdsAndDateRange(@Param("ea") String ea, @Param("objectId") String objectId, @Param("startDate") Date startDate, @Param("endDate")Date endDate);

    @Select("<script>"
            + "SELECT A.spread_fs_uid as employee_id, count(*) as form_data_enroll_count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>" +
            " GROUP BY A.spread_fs_uid ORDER BY form_data_enroll_count DESC"
            + "</script>")
    List<EmployeeRankingDataResult> groupCountByMarketingActivityIdsAndDateRange(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);

    @Select("<script>"
            + "SELECT A.spread_fs_uid as employee_id, count(*) as form_data_enroll_count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND A.spread_fs_uid IN"
            + "<foreach collection = 'userIds' item = 'userId' open = '(' separator = ',' close = ')'>"
            + "#{userId}"
            + "</foreach>"
            + " GROUP BY A.spread_fs_uid ORDER BY form_data_enroll_count DESC"
            + "</script>")
    List<EmployeeRankingDataResult> groupCountByMarketingActivityIdsAndDateRangeAndUserIds(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate, @Param("userIds")List<Integer>userIds);

    @Select("<script>"
            + "SELECT relation.outer_tenant_id as outTenantId, count(*) as form_data_enroll_count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " left join user_relation relation on A.ea = relation.ea and a.spread_fs_uid = relation.fs_user_id "
            + " WHERE relation.status = 'NORMAL' and B.ea = #{upstreamEa} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND relation.outer_tenant_id IN"
            + "<foreach collection = 'outTenantIds' item = 'outEa' open = '(' separator = ',' close = ')'>"
            + "#{outEa}"
            + "</foreach>"
            + " GROUP BY relation.outer_tenant_id ORDER BY form_data_enroll_count DESC"
            + "</script>")
    List<EmployeeRankingDataResult> groupCountByMarketingActivityIdsAndDateRangeAndOutTenantIds(@Param("upstreamEa") String upstreamEa, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate, @Param("outTenantIds") List<Long> outTenantIds);


    @Select("<script>"
            + "SELECT A.spread_fs_uid as employee_id, count(*) as form_data_enroll_count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = ' AND A.marketing_activity_id IN (' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<foreach collection = 'userIds' item = 'userId' open = ' AND A.spread_fs_uid IN (' separator = ',' close = ')'>"
            + "#{userId}"
            + "</foreach>"
            + " GROUP BY A.spread_fs_uid ORDER BY form_data_enroll_count DESC"
            + "</script>")
    List<EmployeeRankingDataResult> groupCountByMarketingActivityIdsAndUserId(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("userIds") List<Integer> userIds,
                                                                              @Param("startDate") Date startDate, @Param("endDate")Date endDate);


    @Select("<script>"
            + "SELECT count(*) as form_data_enroll_count FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea} AND (A.create_time BETWEEN #{startDate} AND #{endDate}) AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND spread_fs_uid IN"
            + "<foreach collection = 'userIds' item = 'userId' open = '(' separator = ',' close = ')'>"
            + "#{userId}"
            + "</foreach>" +
            " GROUP BY A.spread_fs_uid ORDER BY form_data_enroll_count DESC"
            + "</script>")
    int  CountByMarketingActivityIdsAndDateRangeUserIds(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("userIds")List<Integer>userIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate);

    @Select("<script>"
            + "SELECT marketing_event_id as id, count(*) as count FROM customize_form_data_user WHERE marketing_event_id IN \n"
            + "<foreach collection = 'marketingEventIds' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> "
            + "GROUP BY marketing_event_id"
            + "</script>")
    List<DataCount> groupCountByMarketingEventIds(@Param("marketingEventIds")Collection<String> marketingEventIds);

    @Select("<script>"
           +  "SELECT c.id, c.submit_content ->> 'marketingSourceName' AS marketingSourceName, c.submit_content ->> 'utmCampaig' AS campaigName FROM customize_form_data_user c  INNER JOIN customize_form_data d ON c.form_id = d.id\n"
           +  "WHERE d.ea = #{ea} AND c.submit_content ->> 'marketingSourceName' = #{marketingSourceName} AND  c.submit_content ->> 'utmCampaig' IN \n\n"
           + "<foreach collection = 'utmCampaigNames' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> \n"
           + "</script>")
    List<CustomizeFormClueCampaigNameDTO> queryAdCampaigCluesByCampaigName(@Param("ea")String ea, @Param("utmCampaigNames")List<String> utmCampaigNames, @Param("marketingSourceName")String marketingSourceName);

    @Select("<script>"
           + "SELECT c.* FROM customize_form_data_user c INNER JOIN customize_form_data d ON c.form_id = d.id\n"
           + "WHERE d.ea = #{ea} AND c.submit_content ->> 'marketingSourceName' = #{marketingSourceName} AND c.submit_content ->> 'utmCampaig'=#{campaignName}"
           + "</script>")
    List<CustomizeFormDataUserEntity> queryAdCampaigDetailsByCampaigName(@Param("ea")String ea, @Param("campaignName")String campaignName, @Param("marketingSourceName")String marketingSourceName, @Param("page")Page page);


    @Select("<script>"
            + "SELECT c.* FROM customize_form_data_user c INNER JOIN customize_form_data d ON c.form_id = d.id\n"
            + "WHERE d.ea = #{ea} AND c.submit_content ->> 'marketingSourceName' = #{marketingSourceName} AND c.submit_content ->> 'utmCampaig'=#{campaignName}"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryAdCampaigDetailsByCampaigNameWithoutPage(@Param("ea")String ea, @Param("campaignName")String campaignName, @Param("marketingSourceName")String marketingSourceName);


    @Update(" UPDATE customize_form_data_user SET campaign_id = #{campaignId} WHERE id = #{id} ")
    void bindCustomizeFormDataUserAndCampaign(@Param("id") String id, @Param("campaignId") String campaignId);

    @Select(" SELECT * FROM customize_form_data_user WHERE campaign_id = #{campaignId} ORDER BY create_time DESC")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByCampaignId(@Param("campaignId") String campaignId);


    @Select("<script>" +
            "SELECT count(distinct a.id) " +
            "FROM campaign_merge_data a left join customize_form_data_user b on a.id = b.campaign_id and a.marketing_event_id = b.marketing_event_id " +
            "WHERE a.marketing_event_id = #{marketingEventId} and " +
            "<if test='saveCrmStatus.size() != 1'>" +
            "(b.save_crm_status is null or b.save_crm_status in " +
            "<foreach collection = 'saveCrmStatus' item = 'item' open = '(' separator = ',' close = ')'>" +
            "#{item}" +
            "</foreach>" +
            ")" +
            "</if>" +
            "<if test='saveCrmStatus.size() == 1'>" +
            "a.bind_crm_object_id is not null and b.save_crm_status in " +
            "<foreach collection = 'saveCrmStatus' item = 'item' open = '(' separator = ',' close = ')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    Integer getCustomizeFormDataUserByMarketingEventId(@Param("marketingEventId") String marketingEventId, @Param("saveCrmStatus") List<Integer> saveCrmStatus);

    @Select(" SELECT * FROM customize_form_data_user WHERE campaign_id = #{campaignId} ORDER BY create_time LIMIT 1")
    CustomizeFormDataUserEntity getCustomizeFormDataUserByCampaignIdOrderByCreateTime (@Param("campaignId") String campaignId);

    @Select(" SELECT * FROM customize_form_data_user WHERE campaign_id = #{campaignId} AND object_id = #{objectId} AND object_type = #{objectType}")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByCampaignIdAndObject(@Param("campaignId") String campaignId, @Param("objectId") String objectId,
        @Param("objectType") Integer objectType);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE campaign_id IN\n"
        + "<foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByCampaignIds(@Param("campaignIds") List<String> campaignIds);

    @Select("<script> " +
            "  select customize_form_data_user.* from customize_form_data_user , " +
            "  (select campaign_id, MAX(create_time) latest from customize_form_data_user where campaign_id in " +
            "  <foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> " +
            "  AND open_id IS Not null AND wx_app_id = #{wxAppId} GROUP BY campaign_id) tmp_table " +
            "  where customize_form_data_user.campaign_id = tmp_table.campaign_id and customize_form_data_user.create_time = tmp_table.latest " +
            "</script>")
    List<CustomizeFormDataUserEntity> getLatestCustomizeFormDataUserByCampaignIdsAndWxAppId(@Param("wxAppId") String wxAppId, @Param("campaignIds") Collection<String> campaignIds);

    @Select("<script> " +
            " select * from customize_form_data_user where campaign_id in " +
            " <foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> " +
            " <if test = 'wxAppId != null'> and wx_app_id = #{wxAppId} </if> " +
            "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByCampaignIdsAndWxAppId(@Param("wxAppId") String wxAppId, @Param("campaignIds") Collection<String> campaignIds);


    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE (save_crm_status != 0 OR save_crm_status is null) AND campaign_id IN\n"
        + "<foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByCampaignIdsAndSaveFailed(@Param("campaignIds") List<String> campaignIds);

    @Select(" SELECT * FROM customize_form_data_user WHERE submit_content ->> 'phone' = #{phone} AND marketing_event_id = #{marketingEventId}")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByEventIdAndPhone(@Param("marketingEventId") String marketingEventId, @Param("phone") String phone);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE object_id IN"
        + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " <if test='campaignId != null'>"
        + "  AND campaign_id = #{campaignId} "
        + " </if>"
        + "ORDER BY create_time DESC"
        + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObjectsWithOutUseAge(@Param("objectIds") List<String> objectIds, @Param("campaignId") String campaignId);


    @Select("<script>"
        + "SELECT * FROM\n"
        + "(\n"
        + "SELECT *, row_number() over(PARTITION BY t1.campaign_id ORDER BY create_time DESC) AS ROW FROM\n"
        + "(\n"
        + "SELECT * FROM customize_form_data_user WHERE object_id IN \n"
        + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " AND campaign_id IN "
        + "<foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " ORDER BY create_time DESC\n"
        + ") AS t1\n"
        + ") AS t2 where t2.ROW = 1"
        + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserByObjectsAndCampaignId(@Param("objectIds") List<String> objectIds, @Param("campaignIds") List<String> campaignIds);

    @Select(" SELECT * FROM customize_form_data_user WHERE campaign_id = #{campaignId} ORDER BY create_time DESC LIMIT 1")
    CustomizeFormDataUserEntity getLatestCustomizeFormDataUserByCampaignId(@Param("campaignId") String campaignId);

    @Select("<script>"
            + " SELECT * FROM "
            + " ("
            + "   SELECT *, ROW_NUMBER() OVER (PARTITION BY campaign_id ORDER BY create_time DESC) AS row_num "
            + "   FROM customize_form_data_user "
            + "   WHERE campaign_id IN "
            + "   <foreach collection='campaignIds' item='campaignId' open='(' separator=',' close=')'>"
            + "     #{campaignId}"
            + "   </foreach>"
            + " ) AS sub_query "
            + " WHERE sub_query.row_num = 1"
            + "</script>")
    List<CustomizeFormDataUserEntity> getLatestCustomizeFormDataUserByCampaignIds(@Param("campaignIds") List<String> campaignIds);


    @Update("UPDATE customize_form_data_user SET campaign_id = null WHERE campaign_id = #{campaignId}")
    int unBindCampaignData(@Param("campaignId") String campaignId);


    @Select("<script>"
            + "SELECT B.* FROM campaign_merge_data AS A JOIN customize_form_data_user AS B ON A.id = B.campaign_id where campaign_members_obj_id is null and save_crm_status is null "
            + "   <if test=\"ea != null\">\n"
            + "        AND A.ea = #{ea}\n"
            + "   </if>\n"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserNoBindMembersObj(@Param("ea") String ea);


    @Select("<script>"
        + "SELECT A.* from customize_form_data_user AS A JOIN marketing_live AS B ON A.marketing_event_id = B.marketing_event_id WHERE A.campaign_id is null "
        + "<if test=\"corpId != null\"> "
        + "     AND B.corp_id = #{corpId} \n"
        + " </if> "
        + "</script>")
    List<CustomizeFormDataUserEntity> queryLiveNotBindCampaignEnrollDataBydCorpId(@Param("corpId") Integer corpId);

    @Select("<script>"
            + "SELECT marketing_event_id AS marketingEventId, object_type AS objectType, object_id AS objectId, create_time AS submitTime FROM customize_form_data_user WHERE submit_content->>'phone'=#{memberPhone} AND  marketing_event_id IN\n"
            + "<foreach collection = 'marketingEventIds' item = 'item' open = '(' separator = ',' close = ')'>"
                + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserObjectDTO> getByMarketingEventIds(@Param("marketingEventIds")List<String> marketingEventIds, @Param("memberPhone")String memberPhone);

    @Select("<script>"
        + " SELECT count(1) FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id\n"
        + " WHERE A.ea = #{ea} AND B.uid is NOT NULL AND B.uid != '' "
        + " <if test=\"needFilter == true\">\n"
        + " AND B.create_time &gt;= #{startDate}\n"
        + " AND B.create_time &lt;= #{endDate}\n"
        + " </if>\n"
        + "</script>")
    int countMinAppEnrollCountByEa(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);


    @Select("<script>"
        + " SELECT spread_fs_uid, count(1) AS count from customize_form_data_user\n"
        + " WHERE ea = #{ea} AND uid IS NOT NULL AND uid != '' AND spread_fs_uid is not null AND spread_fs_uid != 0\n"
        + " <if test=\"needFilter == true\">\n"
        + " AND create_time &gt;= #{startDate}\n"
        + " AND create_time &lt;= #{endDate}\n"
        + " </if>\n"
        + " GROUP BY spread_fs_uid\n"
        + " ORDER BY count(1) DESC"
        + "</script>")
    List<CustomizeFormClueNumDTO> pageStaffMinAppEnrollLead(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter, @Param("page") Page page);

    @Select("<script>"
        + " SELECT COUNT(1) FROM ("
        + " SELECT B.spread_fs_uid, count(1) AS count from customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id\n"
        + " WHERE A.ea = #{ea} AND B.uid IS NOT NULL AND B.uid != '' AND B.spread_fs_uid is not null AND B.spread_fs_uid != 0\n"
        + " <if test=\"needFilter == true\">\n"
        + " AND B.create_time &gt;= #{startDate}\n"
        + " AND B.create_time &lt;= #{endDate}\n"
        + " </if>\n"
        + " GROUP BY B.spread_fs_uid\n"
        + " ORDER BY count(1) DESC) AS T"
        + "</script>")
    int countStaffMinAppEnrollLead(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);

    @Select("<script>"
        + "SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} AND submit_content ->> <![CDATA['${field}']]> = #{enrollContent} LIMIT 1"
        + "</script>")
    CustomizeFormDataUserEntity queryDataByEnrollFieldAndEventId(@Param("field") String enrollField, @Param("enrollContent") String  enrollContent, @Param("marketingEventId") String MarketingEventId);

    @Select("<script>"
        + " SELECT A.* FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id\n"
        + " LEFT JOIN activity AS C ON A.marketing_event_id = C.marketing_event_id\n"
        + " LEFT JOIN marketing_live AS D ON A.marketing_event_id = D.marketing_event_id\n"
        + " WHERE A.marketing_event_id IS NOT NULL AND A.campaign_id IS NULL AND A.marketing_event_id != '' AND C.id IS NULL \n"
        + " AND D.id IS NULL"
        + "   <if test=\"ea != null\">\n"
        + "     AND B.ea = #{ea}\n"
        + "   </if>\n"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryEnrollNotActivityAndLive(@Param("ea") String ea);


    @Select("<script>"
        + " SELECT B.* FROM customize_form_data AS A join customize_form_data_user AS B ON A.id = B.form_id\n"
        + " WHERE B.spread_fs_uid = 0 AND B.lead_id IS NOT NULL \n"
        + "   <if test=\"ea != null\">\n"
        + "     AND A.ea = #{ea}\n"
        + "   </if>\n"
        + " ORDER BY create_time DESC"
        + "</script>")
    List<CustomizeFormDataUserEntity> queryEnrollspreadfsUidIsZero(@Param("ea") String ea);

    @Select("SELECT u.* FROM customize_form_data_user u LEFT JOIN customize_form_data d ON u.form_id = d.id WHERE d.ea=#{ea}\n"
            + "AND u.lead_id IS NOT NULL AND u.submit_content ->>'utmCampaig' IS NOT NULL AND u.create_time >=#{createTime}")
    List<CustomizeFormDataUserEntity> queryUtmLeadsByCreateTime(@Param("ea") String ea, @Param("createTime")Date createTime);

    @Select("SELECT distinct(d.ea) AS ea FROM customize_form_data_user u LEFT JOIN customize_form_data d ON u.form_id = d.id\n"
            + "AND u.lead_id IS NOT NULL AND u.submit_content ->>'utmCampaig' IS NOT NULL")
    List<String> getUtmLeadsAllEas();

    @Select(" SELECT channel_value, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id WHERE A.ea = #{ea} AND B.marketing_event_id = #{marketingEventId} AND B.channel_value IS NOT NULL AND B.channel_value != '' GROUP BY channel_value")
    List<CustomizeFormClueNumDTO> queryChannelStatisticsByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
        + " SELECT object_id AS objectId, count(1) AS count FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} AND object_id IN "
        + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + " GROUP BY object_id"
        + "</script>")
    List<CustomizeFormClueNumDTO> queryMarketingEventAndObjsClueNum(@Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);

    @Select("<script>"
            + " SELECT parent_object_id AS objectId, count(1) AS count FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} AND parent_object_id IN "
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " GROUP BY parent_object_id"
            + "</script>")
    List<CustomizeFormClueNumDTO> queryMarketingEventAndParentObjsClueNum(@Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);



    @Select("SELECT spread_fs_uid AS spreadFsUid, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id\n"
        + "WHERE A.ea = #{ea} AND B.marketing_event_id = #{marketingEventId} AND spread_fs_uid IS NOT NULL AND spread_fs_uid != 0 GROUP BY spread_fs_uid ORDER BY count(1) DESC")
    List<CustomizeFormClueNumDTO> queryStaffClueByMarketingEvent(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("page") Page page);

    @Select("SELECT COUNT(1) FROM  "
        + "(SELECT spread_fs_uid AS spreadFsUid, count(1) AS count FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id\n"
        + "WHERE A.ea = #{ea} AND B.marketing_event_id = #{marketingEventId} AND spread_fs_uid IS NOT NULL AND spread_fs_uid != 0 GROUP BY spread_fs_uid ORDER BY count(1) DESC) AS T")
    int queryStaffClueByMarketingEventCount(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);


    @Update(" UPDATE customize_form_data_user\n"
            + "    SET save_crm_status = #{saveCrmStatus},save_crm_error_message =#{saveCrmMessage},lead_id = #{leadId}\n"
            + "    WHERE id = #{id}")
    int updateRelatedCustomizeFormDataUserCrmMessage(@Param("id") String id, @Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmMessage") String saveCrmMessage, @Param("leadId") String leadId);

    @Select(" SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} AND lead_id = #{leadId} ORDER BY create_time LIMIT 1")
    CustomizeFormDataUserEntity getCustomizeFormDataUserByLeadIdOrderByCreateTime (@Param("marketingEventId") String marketingEventId,@Param("leadId") String leadId);

    @Select("<script>"
            + " SELECT * FROM customize_form_data_user where form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " and lead_id is not null and (source_type != 5 and source_type != 9) "
            + "</script>")
    List<CustomizeFormDataUserEntity> getUpdateLeadCreatorFormData(@Param("formIds") List<String> formIds);

    @Select("<script>"
            + "SELECT count(*) FROM customize_form_data_user WHERE ea=#{ea} AND form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')' index = 'num'>"
            + "#{formIds[${num}]}"
            + "</foreach>"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " <if test='sourceType!=null'>"
            + "  AND source_type = #{sourceType}"
            + " </if>"
            + " <if test='saveCrmStatus!=null and saveCrmStatus==99'>"
            + "  AND (save_crm_status = 1 OR save_crm_status = 2)"
            + " </if>"
            + "</script>")
    int countUnSaveFormData(@Param("ea")String ea, @Param("formIds") List<String> formIds, @Param("form_usage") Integer form_usage, @Param("sourceType") Integer sourceType, @Param("saveCrmStatus") Integer saveCrmStatus);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId}"
            + "<if test=\"objectIds != null and objectIds.size() > 0\">"
            + "<foreach collection = 'objectIds' item = 'item' open = ' AND object_id IN (' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</if>"
            + " AND lead_id IN"
            + "<foreach collection = 'crmLeadIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "<if test=\"marketingEventId != null and marketingEventId != ''\">"
            + " AND marketing_event_id = #{marketingEventId}"
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUsersByCrmLeadIds(@Param("crmLeadIds") List<String> crmLeadIds, @Param("marketingEventId") String marketingEventId,
                                                                              @Param("formId") String formId, @Param("objectIds") List<String> objectIds);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId}"
            + " AND lead_id IN"
            + "<foreach collection = 'crmLeadIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserEntity> checkInMarketingEvent(@Param("marketingEventId") String marketingEventId, @Param("crmLeadIds") List<String> crmLeadIds);

    @Select("select user_marketing_id from user_marketing_miniapp_account_relation where ea = #{ea} and uid = #{uid}")
    String getMarketingUserAccountByEaAndUid(@Param("ea") String ea, @Param("uid") String uid);

    @Select("<script>" +
            "select crm_lead_id from user_marketing_crm_lead_account_relation where ea = #{ea} and user_marketing_id IN\n" +
            "        <foreach collection=\"userMarketingIds\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">\n" +
            "            #{userMarketingIds[${idx}]}\n" +
            "        </foreach>" +
            "</script>")
    List<String> listByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds") ArrayList<String> userMarketingIds);

    @Select("<script>" +
            "SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} and other_crm_object_bind ->> 'apiName' = #{bindApiName} and other_crm_object_bind ->> 'objectId' = #{objectId} limit 1" +
            "</script>")
    CustomizeFormDataUserEntity getContactOrCustomerByTypeAndId(@Param("bindApiName") String bindApiName, @Param("objectId") String objectId, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
            + "SELECT * FROM (\n"
            + "SELECT T1.userId,T1.outUserId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount ,T1.lookUpCount,T1.forwardCount FROM\n"
            + "(SELECT fs_user_id AS userId,null as outUserId, spread_count as spreadCount,look_up_count as lookUpCount,forward_count as forwardCount FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} <![CDATA[ AND spread_count<=0 ]]>\n"
            + "UNION all\n"
            + "SELECT user_id AS userId,out_user_id as outUserId, -1 as spreadCount ,-1 as lookUpCount,-1 as forwardCount FROM spread_task WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} AND user_id NOT IN\n"
            + "(SELECT fs_user_id FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}))AS T1\n"
            + "LEFT JOIN(SELECT A.spread_fs_uid AS userId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY userId )AS T2 ON T1.userId = T2.userId\n"
            + "ORDER BY clueCount DESC,lookUpCount DESC, spreadCount DESC ,forwardCount DESC) AS T4\n"
            + "<if test='employeeRangeStr != null'>"
            + "where "
            + "string_to_array(T4.userId::text, ',') <![CDATA[ && ]]> string_to_array(#{employeeRangeStr}, ',')\n"
            + "</if>"
            + "</script>")
    List<MarketingActivityEmployeedsByClueDTO> queryPageMarketingActivityUncompleteTaskEmployeedsByClueCount(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId, @Param("page") Page page,
                                                                                                             @Param("dingTalkVersion") Boolean dingTalkVersion,
                                                                                                             @Param("employeeRangeStr") String employeeRangeStr
    );

    @Select("<script>"
            + "SELECT * FROM (\n"
            + "SELECT T1.userId,T1.outUserId, T1.spreadCount, COALESCE(T2.clueCount, 0) AS clueCount ,T1.lookUpCount,T1.forwardCount FROM\n"
            + "(SELECT fs_user_id AS userId,null as outUserId, spread_count as spreadCount,look_up_count as lookUpCount,forward_count as forwardCount FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} <![CDATA[ AND spread_count>0 ]]>\n)AS T1\n"
            + "LEFT JOIN(SELECT A.spread_fs_uid AS userId, count(*) AS clueCount FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea =#{ea} AND A.marketing_activity_id =#{marketingActivityId} GROUP BY userId )AS T2 ON T1.userId = T2.userId\n"
            + "ORDER BY clueCount DESC,lookUpCount DESC, spreadCount DESC ,forwardCount DESC) AS T4\n"
            + "<if test='employeeRangeStr != null'>"
            + "where "
            + "string_to_array(T4.userId::text, ',') <![CDATA[ && ]]> string_to_array(#{employeeRangeStr}, ',')\n"
            + "</if>"
            + "</script>")
    List<MarketingActivityEmployeedsByClueDTO> queryPageMarketingActivityCompleteTaskEmployeedsByClueCount(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId, @Param("page") Page page,
                                                                                                           @Param("dingTalkVersion") Boolean dingTalkVersion,
                                                                                                           @Param("employeeRangeStr") String employeeRangeStr
    );

    @Select("<script>"
            + "SELECT sum(A) FROM\n"
            + "(SELECT count(*) AS A  FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} <![CDATA[ AND spread_count<=0 ]]>\n"
            + "UNION all\n"
            + "SELECT count(*) AS A FROM spread_task WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId} AND user_id NOT IN\n"
            + "(SELECT fs_user_id FROM marketing_activity_employee_statistic WHERE ea=#{ea} AND marketing_activity_id=#{marketingActivityId}))C\n"
            + "</script>")
    int countMarketingActivityUncompleteTaskEmployeeds(@Param("ea")String ea, @Param("marketingActivityId")String marketingActivityId);

    @Update("UPDATE customize_form_data_user SET spread_fs_uid=#{newUserId} WHERE id IN(\n" +
            "SELECT c1.id FROM customize_form_data_user c1 LEFT JOIN customize_form_data c2 on c1.form_id=c2.id WHERE c2.ea=#{ea} AND c1.spread_fs_uid=#{oldUserId})")
    int updateFsUserIdByVirtrualUserId(@Param("ea")String ea, @Param("oldUserId")Integer oldUserId, @Param("newUserId")Integer newUserId);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE marketing_activity_id = #{marketingActivityId}\n"
            + "AND A.spread_fs_uid = #{fsUid} \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + "AND ((A.object_id = #{objectId}\n"
            + " AND A.object_type = #{objectType}) OR (A.parent_object_id = #{objectId}\n"
            + " AND A.parent_object_type = #{objectType}))\n"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND A.form_usage=2 "
            + " </if>"
            + "<if test =\"keyword != null\"> AND (A.submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))</if>\n"
            + " ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getEmployeesCustomizeFormDataUser(@Param("marketingActivityId") String marketingActivityId,
                                                                                @Param("objectId") String objectId, @Param("objectType") Integer objectType,@Param("fsUid") Integer fsUid,
                                                                                @Param("keyword")String keyword, @Param("form_usage") Integer form_usage, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter);
    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE A.id in \n"
            +   "<foreach collection = 'ids' index = 'index' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{ids[${index}]}"
            +   "</foreach>"
            + "<if test =\"keyword != null\"> AND (A.submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))</if>\n"
            + " ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> getCustomizeFormIds(@Param("ids") List<String> ids, @Param("keyword")String keyword,@Param("page") Page page);

    @Select("<script>"
            + "select A.objectId as objectId,  sum(A.count) as count from"
            + " ("
            + "SELECT  object_id as objectId, count(1) AS count FROM customize_form_data_user WHERE object_id IN \n"
            +  "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +  "</foreach>"
            + " AND object_type = #{objectType} "
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " GROUP BY object_id union "
            + " (SELECT  parent_object_id as objectId, count(1) AS count FROM customize_form_data_user WHERE parent_object_id IN \n"
            +  "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +  "</foreach>"
            + " AND parent_object_type = #{objectType}"
            + " <if test='form_usage!=null and form_usage==2'>"
            + "  AND form_usage=2 "
            + " </if>"
            + " GROUP BY parent_object_id) "
            + " ) as A "
            + "GROUP BY objectId"
            + "</script>")
    List<CustomizeFormUserDataCountByObjectIdDTO> queryObjectClueCount(@Param("objectIds") List<String> objectIds,@Param("objectType")Integer objectType, @Param("form_usage") Integer form_usage);

    @Select("<script>"
            + "SELECT marketing_activity_id,count(1) FROM\n"
            + "(\n"
            + "SELECT marketing_activity_id FROM customize_form_data AS B LEFT JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + "WHERE B.ea = #{ea} and from_user_marketing_id = #{fromUserMarketingId} AND A.marketing_activity_id IN \n"
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + ") AS T\n"
            + "GROUP BY marketing_activity_id "
            + "</script>")
    List<CustomizeFormClueNumDTO> countByMarketingActivityIdsAndFromUserMarketingId(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("fromUserMarketingId") String fromUserMarketingId);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user AS A JOIN customize_form_data AS B ON A.form_id = B.id WHERE marketing_activity_id = #{marketingActivityId}\n"
            + "AND A.spread_fs_uid = #{fsUid} AND A.from_user_marketing_id = #{fromUserMarketingId} \n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            + "<if test=\"(fingerPrintList != null and fingerPrintList.size() > 0) or (uidList != null and uidList.size() > 0) or (wxOpenIdList != null and wxOpenIdList.size() > 0)\">"
            + "<trim prefix=\"AND (\" prefixOverrides=\"AND|OR\" suffix=\")\">\n"
            + "<if test=\"fingerPrintList != null and fingerPrintList.size() > 0\">"
            + "OR A.finger_print IN \n"
            +   "<foreach collection = 'fingerPrintList' index = 'index' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{fingerPrintList[${index}]}"
            +   "</foreach>"
            + "</if>"
            + "<if test=\"uidList != null and uidList.size() > 0\">"
            + "OR A.uid IN \n"
            +   "<foreach collection = 'uidList' index = 'index' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{uidList[${index}]}"
            +   "</foreach>"
            + "</if>"
            + "<if test=\"wxOpenIdList != null and wxOpenIdList.size() > 0\">"
            + "OR A.open_id IN \n"
            +   "<foreach collection = 'wxOpenIdList' index = 'index' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{wxOpenIdList[${index}]}"
            +   "</foreach>"
            + "</if>"
            + "</trim>\n"
            + "<if test =\"keyword != null\"> AND (A.submit_content->>'phone' LIKE CONCAT('%',#{keyword},'%') OR submit_content->>'name' LIKE CONCAT('%',#{keyword},'%'))</if>\n"
            + "</if>"
            + " ORDER BY B.create_time DESC,A.create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> pageFormUserDataForMarketingActivityIdAndSpreadFsUid(@Param("marketingActivityId") String marketingActivityId,@Param("fsUid") Integer fsUid,@Param("fromUserMarketingId") String fromUserMarketingId, @Param("keyword")String keyword,
                                                                                           @Param("fingerPrintList") List<String> fingerPrintList,@Param("uidList") List<String> uidList,@Param("wxOpenIdList") List<String> wxOpenIdList,
                                                                                           @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter,@Param("page") Page page);

    @Select("<script>"
            + "SELECT count(1) FROM (\n"
            + " SELECT * FROM customize_form_data AS B JOIN customize_form_data_user AS A ON A.form_id = B.id\n"
            + " WHERE B.ea = #{ea}\n"
            + " <if test=\"needFilter == true\">\n"
            + " AND A.create_time &gt;= #{startDate}\n"
            + " AND A.create_time &lt;= #{endDate}\n"
            + " </if>\n"
            +     "<if test='ids != null and ids.size > 0'>"
            +           "AND A.lead_id IN\n"
            +           "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            +               "#{item}\n"
            +           "</foreach>"
            +     "</if>"
            + " ) AS T"
            + "</script>")
    Integer countClueByEaAndMarketingActivityId(@Param("ea") String ea, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("needFilter") boolean needFilter,@Param("ids") List<String> ids);

    @Select("<script>"
            + "SELECT * FROM  customize_form_data_user "
            + " WHERE ea = #{ea}  <![CDATA[ AND create_time >= #{startDate} AND create_time <= #{endDate} ]]> "
            + "<if test=\"marketingActivityIds != null and marketingActivityIds.size()>0 \">"
            + "  AND marketing_activity_id IN "
            +   "<foreach collection = 'marketingActivityIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingActivityIds[${num}]}"
            +   "</foreach>"
            + "</if>"
            + "<if test=\"userIds != null and userIds.size()>0 \">"
            + "  AND spread_fs_uid IN "
            +   "<foreach collection = 'userIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{userIds[${num}]}"
            +   "</foreach>"
            + "</if>"
            + " ORDER BY create_time DESC"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryPageByMarketingActivityIdsAndUserIdRange(@Param("ea") String ea, @Param("marketingActivityIds") List<String> marketingActivityIds,
                                                  @Param("userIds") List<Integer> userIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate,@Param("page") Page page);

    @Update(" UPDATE customize_form_data_user\n"
            + "    SET save_crm_status = #{saveCrmStatus},save_crm_error_message =#{saveCrmMessage} \n"
            + "    WHERE id = #{id}")
    int updateCustomizeFormDataUserSaveCrmMessage(@Param("id") String id, @Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmMessage") String saveCrmMessage);

    @Update(" UPDATE customize_form_data_user\n"
            + "    SET save_crm_status = #{saveCrmStatus},save_crm_error_message =#{saveCrmMessage},extra_data_id = #{extraDataId} \n"
            + "    WHERE id = #{id}")
    int updateCustomizeFormDataUserSaveCrm(@Param("id") String id, @Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmMessage") String saveCrmMessage, @Param("extraDataId") String extraDataId);


    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE form_id = #{formId} AND submit_content ->> 'phone' = #{phone} "
            + " <if test='marketingEventId != null'>"
            + "  AND marketing_event_id = #{marketingEventId} "
            + " </if>"
            + "<if test=\"objectIds != null and objectIds.size() > 0\">"
            + " AND object_id IN"
            + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " </if>"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByPhoneAndMarketingEventId(@Param("marketingEventId")String marketingEventId,@Param("phone") String phone, @Param("formId") String formId, @Param("objectIds") List<String> objectIds);

    @Select("<script>"
            + " SELECT * FROM customize_form_data_user where ea = #{ea} "
            + " <if test='marketingEventId != null'>"
            + "  AND marketing_event_id = #{marketingEventId} "
            + " </if>"
            + " <if test='fingerPrint != null'>"
            + "  AND finger_print in (select browser_user_id from user_marketing_browser_user_relation where user_marketing_id = (select user_marketing_id from user_marketing_browser_user_relation where browser_user_id = #{fingerPrint} and ea = #{ea})) "
            + " </if>"
            + " <if test='uid != null'>"
            + "  AND uid = #{uid} "
            + " </if>"
            + " <if test='wxAppId != null'>"
            + "  AND wx_app_id = #{wxAppId} "
            + " </if>"
            + " <if test='wxOpenId != null'>"
            + "  AND open_id = #{wxOpenId} "
            + " </if>"
            + " AND form_id IN "
            + "<foreach collection = 'formIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(@Param("ea")String ea,@Param("marketingEventId")String marketingEventId,@Param("fingerPrint")String fingerPrint, @Param("uid") String uid,@Param("wxAppId")String wxAppId,@Param("wxOpenId")String wxOpenId, @Param("formIds") List<String> formIds);

    @Select("<script>"
            + "SELECT * FROM customize_form_data_user WHERE ea = #{ea} "
            + " <if test='marketingEventId != null'>"
            + "  AND marketing_event_id = #{marketingEventId} "
            + " </if>"
            + " <if test='fingerPrint != null'>"
            + "  AND finger_print in (select browser_user_id from user_marketing_browser_user_relation where user_marketing_id = (select user_marketing_id from user_marketing_browser_user_relation where browser_user_id = #{fingerPrint} and ea = #{ea})) "
            + " </if>"
            + " <if test='uid != null'>"
            + "  AND uid = #{uid} "
            + " </if>"
            + " <if test='wxAppId != null'>"
            + "  AND wx_app_id = #{wxAppId} "
            + " </if>"
            + " <if test='wxOpenId != null'>"
            + "  AND open_id = #{wxOpenId} "
            + " </if>"
            + " AND object_id = #{objectId}"
            + " AND object_type = #{objectType}"
            + "</script>")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByObjectIdAndUserInfo(@Param("ea")String ea,@Param("fingerPrint") String fingerPrint, @Param("uid") String uid,@Param("wxAppId")String wxAppId,@Param("wxOpenId")String wxOpenId, @Param("objectId") String objectId, @Param("objectType") Integer objectType,@Param("marketingEventId")String marketingEventId);

    @Select("<script> " +
            " select * from customize_form_data_user where ea = #{ea} and marketing_event_id = #{marketingEventId}  and campaign_id in " +
            " <foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'> "
            + "#{item} "
            + "</foreach> "
            + "</script>")
    List<CustomizeFormDataUserEntity> getByMarketingEventIdAndCampaignIdList(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("campaignIds") Collection<String> campaignIds);

    @Select(" SELECT * FROM customize_form_data_user WHERE marketing_event_id = #{marketingEventId} ORDER BY create_time")
    List<CustomizeFormDataUserEntity> getCustomizeFormDataUserListByMarketingEventId(@Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM customize_form_data_user WHERE ea = #{ea} and marketing_event_id = #{marketingEventId} and submit_content ->> 'email' = #{email}")
    List<CustomizeFormDataUserEntity> queryCustomizeFormDataUserByEmail(@Param("ea") String ea, @Param("email") String email, @Param("marketingEventId") String marketingEventId);

    @Select("select * from customize_form_data_user where ea = #{ea} and marketing_event_id = #{marketingEventId} and campaign_id is null order by create_time desc ")
    List<CustomizeFormDataUserEntity> queryFailedCampaign(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
            + "select * from customize_form_data_user where ea = #{ea}  and marketing_event_id = #{marketingEventId} "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<CustomizeFormDataUserEntity> scanByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("lastId") String lastId, @Param("limit") int limit);

    @Select("select count(*) from customize_form_data_user where ea = #{ea} and marketing_event_id = #{marketingEventId} and object_id = #{objectId}")
    int countByMarketingEventIdAndObjectId(@Param("marketingEventId")  String marketingEventId,@Param("objectId") String objectId,@Param("ea")  String ea);
}
