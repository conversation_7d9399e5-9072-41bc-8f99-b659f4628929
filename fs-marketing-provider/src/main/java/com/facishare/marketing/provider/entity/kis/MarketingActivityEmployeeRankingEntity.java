package com.facishare.marketing.provider.entity.kis;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * @author: dongzhb
 * @date: 2019/3/12
 * @Description:
 */
@Data
@ToString
public class MarketingActivityEmployeeRankingEntity implements Serializable {
    private static final long serialVersionUID = -2256395442785910240L;
    @ApiModelProperty(value = "员工ID")
    private Integer fsUserId;
    @ApiModelProperty("推广次数")
    private Integer  spreadCount;
    @ApiModelProperty("访问次数")
    private Integer lookUpCount;
    @ApiModelProperty("转发次数")
    private Integer forwardCount;
    @ApiModelProperty("转发人数")
    private Integer forwardUserCount;
    @ApiModelProperty("获取线索数")
    private Integer leadIncrementCount;
    @ApiModelProperty("访问人数")
    private Integer  lookUpUserCount;
}
