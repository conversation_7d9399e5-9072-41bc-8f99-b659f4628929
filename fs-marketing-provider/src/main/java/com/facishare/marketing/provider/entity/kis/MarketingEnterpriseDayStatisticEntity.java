package com.facishare.marketing.provider.entity.kis;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/2/25.
 */
@Data
@Entity
public class MarketingEnterpriseDayStatisticEntity implements Serializable {
    private String id;
    private String ea;   // 公司帐号
    private Date date;
    private String marketingActivityId;
    private Integer spreadCount;   // 员工推广次数
    private Integer spreadUserCount; //员工推广人数
    private Integer forwardCount; // 转发次数，不含员工
    private Integer lookUpCount; // 访问次数，不含员工
    private Integer forwardUserCount; // 转发人数，不含员工
    private Integer lookUpUserCount; // 访问人数，不含员工
    private Integer leadIncrementCount; // 线索增量
    private Integer customerIncrementCount; // 客户增量
    private Date createTime;
    private Date updateTime;
}
