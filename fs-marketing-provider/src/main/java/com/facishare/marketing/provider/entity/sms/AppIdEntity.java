package com.facishare.marketing.provider.entity.sms;

import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

/**
 * Created by zhengh on 2018/12/20.
 */
@Data
@Entity
public class AppIdEntity {
    private String id;                     //appId  腾讯控制台申请
    private String secret;                 //secret 腾讯控制台申请
    private Integer signatureLeftCount;    //该appid已经分配的签名数量
    private Integer templateLeftCount;     //该appid已经分配的模板数量
    private Integer signatureTotalCount;   //该appid分配的签名数量
    private Integer templateTotalCount;    //该appid分配的模板数量
    private Date createTime;               //创建appid时间
    private Date updateTime;               //更新appid时间，默认和createTime相同
}
