package com.facishare.marketing.provider.dao.marketingplugin;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.Query;
import com.facishare.marketing.provider.dao.param.coupon.QueryCouponParam;
import com.facishare.marketing.provider.entity.marketingplugin.CouponTemplateCount;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

public interface WeChatCouponDAO {

    @Insert("INSERT INTO wechat_coupon ("
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"wechat_coupon_id\",\n"
            + "        \"exchange_price\",\n"
            + "        \"expired_tip\",\n"
            + "        \"available_day_time\",\n"
            + "        \"use_method\",\n"
            + "        \"coupon_code_mode\",\n"
            + "        \"discount_amount\",\n"
            + "        \"merchant_logo_url\",\n"
            + "        \"coupon_image_url\",\n"
            + "        \"channel\",\n"
            + "        \"hide_link\",\n"
            + "        \"description\",\n"
            + "        \"expired_days\",\n"
            + "        \"out_request_no\",\n"
            + "        \"max_coupons\",\n"
            + "        \"goods_name\",\n"
            + "        \"wait_days_after_receive\",\n"
            + "        \"stock_name\",\n"
            + "        \"is_member\",\n"
            + "        \"available_end_time\",\n"
            + "        \"transaction_minimum\",\n"
            + "        \"merchant_name\",\n"
            + "        \"belong_merchant\",\n"
            + "        \"stock_id\",\n"
            + "        \"tags\",\n"
            + "        \"stock_type\",\n"
            + "        \"marketing_event_id\",\n"
            + "        \"background_color\",\n"
            + "        \"available_day_after_receive\",\n"
            + "        \"max_coupons_per_user\",\n"
            + "        \"week_day\",\n"
            + "        \"template_id\",\n"
            + "        \"comment\",\n"
            + "        \"discount_percent\",\n"
            + "        \"available_begin_time\",\n"
            + "        \"status\",\n"
            + "        \"applicable_channel\",\n"
            + "        \"send_scope\",\n"
            + "        \"coupon_id\",\n"
            + "        \"operator\",\n"
            + "        \"dealer_count\",\n"
            + "        \"is_participate\",\n"
            + "        \"scene\",\n"
            + "        \"create_coupon_type\",\n"
            + "        \"send_down_status\",\n"
            + "        \"coupon_no\",\n"
            + "        \"account_scope\",\n"
            + "        \"receive_scope\",\n"
            + "        \"store_scope\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.wechatCouponId},\n"
            + "        #{obj.exchangePrice},\n"
            + "        #{obj.expiredTip},\n"
            + "        #{obj.availableDayTime},\n"
            + "        #{obj.useMethod},\n"
            + "        #{obj.couponCodeMode},\n"
            + "        #{obj.discountAmount},\n"
            + "        #{obj.merchantLogoUrl},\n"
            + "        #{obj.couponImageUrl},\n"
            + "        #{obj.channel},\n"
            + "        #{obj.hideLink},\n"
            + "        #{obj.description},\n"
            + "        #{obj.expiredDays},\n"
            + "        #{obj.outRequestNo},\n"
            + "        #{obj.maxCoupons},\n"
            + "        #{obj.goodsName},\n"
            + "        #{obj.waitDaysAfterReceive},\n"
            + "        #{obj.stockName},\n"
            + "        #{obj.isMember},\n"
            + "        #{obj.availableEndTime},\n"
            + "        #{obj.transactionMinimum},\n"
            + "        #{obj.merchantName},\n"
            + "        #{obj.belongMerchant},\n"
            + "        #{obj.stockId},\n"
            + "        #{obj.tags},\n"
            + "        #{obj.stockType},\n"
            + "        #{obj.marketingEventId},\n"
            + "        #{obj.backgroundColor},\n"
            + "        #{obj.availableDayAfterReceive},\n"
            + "        #{obj.maxCouponsPerUser},\n"
            + "        #{obj.weekDay},\n"
            + "        #{obj.templateId},\n"
            + "        #{obj.comment},\n"
            + "        #{obj.discountPercent},\n"
            + "        #{obj.availableBeginTime},\n"
            + "        #{obj.status},\n"
            + "        #{obj.applicableChannel},\n"
            + "        #{obj.sendScope},\n"
            + "        #{obj.couponId},\n"
            + "        #{obj.operator},\n"
            + "        #{obj.dealerCount},\n"
            + "        #{obj.isParticipate},\n"
            + "        #{obj.scene},\n"
            + "        #{obj.createCouponType},\n"
            + "        #{obj.sendDownStatus},\n"
            + "        #{obj.couponNo},\n"
            + "        #{obj.accountScope},\n"
            + "        #{obj.receiveScope},\n"
            + "        #{obj.storeScope},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int saveWeChatCoupon(@Param("obj") WechatCouponEntity wechatCouponEntity);

    @Select("<script> select * from wechat_coupon where wechat_coupon_id = #{wechatCouponId} </script>")
    WechatCouponEntity queryWeChatCoupon(@Param("wechatCouponId") String wechatCouponId);

    @Select("<script> select * from wechat_coupon where stock_id = #{stockId} </script>")
    WechatCouponEntity queryWeChatCouponByStockId(@Param("stockId") String stockId);

    @Select("<script> select * from wechat_coupon where id = #{id} </script>")
    WechatCouponEntity getById(@Param("id") String id);

    @Select("<script> select * from wechat_coupon where marketing_event_id = #{marketingEventId} and ea = #{ea} and status != 2 "
            + " <if test=\"scenes !=null\">\n" + " <foreach collection=\"scenes\"  index='idx' open=\" and scene in(\" close=\")\"\n" + "   item=\"item\" separator=\",\">\n"
            + "          #{scenes[${idx}]}\n" + "        </foreach>\n" + "      </if>"
            + " order by create_time desc  </script>")
    List<WechatCouponEntity> queryList(@Param("marketingEventId") String marketingEventId,@Param("ea") String ea,@Param("scenes") List<Integer> scenes,@Param("page") Page page);

    @Select("<script> select count(distinct (marketing_event_id)) as relateMarketingCount , count(id) as stockCount from wechat_coupon where template_id = #{templateId} </script>")
    CouponTemplateCount queryStaticCountByTemplate(@Param("templateId") String templateId);

    @Select("<script> SELECT * from wechat_coupon " +
            " where template_id = #{templateId} " +
            "ORDER BY create_time DESC </script>")
    List<WechatCouponEntity> queryStockListByTemplateId(@Param("templateId") String templateId, @Param("page") Page page);

    @Select("<script> select * from wechat_coupon order by create_time desc </script>")
    List<WechatCouponEntity> queryAll();

    @Select("<script> update wechat_coupon set status = #{status} where id = #{objectId} </script>")
    void updateCouponStatus(@Param("objectId") String objectId, @Param("status") Integer status);

    @Update("<script>  " +
            "UPDATE wechat_coupon "
            + "SET "
            + "    <if test=\"obj.maxCoupons != null\">\n"
            + "         \"max_coupons\" = #{obj.maxCoupons},\n"
            + "    </if>\n"
            + "    <if test=\"obj.channel != null\">\n"
            + "         \"channel\" = #{obj.channel},\n"
            + "    </if>\n"
            + "    <if test=\"obj.applicableChannel != null\">\n"
            + "         \"applicable_channel\" = #{obj.applicableChannel},\n"
            + "    </if>\n"
            + "    <if test=\"obj.sendScope != null\">\n"
            + "         \"send_scope\" = #{obj.sendScope},\n"
            + "    </if>\n"
            + "    <if test=\"obj.storeScope != null\">\n"
            + "         \"store_scope\" = #{obj.storeScope},\n"
            + "    </if>\n"
            + "    <if test=\"obj.comment != null\">\n"
            + "         \"comment\" = #{obj.comment},\n"
            + "    </if>\n"
            + "    <if test=\"obj.goodsName != null\">\n"
            + "         \"goods_name\" = #{obj.goodsName},\n"
            + "    </if>\n"
            + "    <if test=\"obj.description != null\">\n"
            + "         \"description\" = #{obj.description},\n"
            + "    </if>\n"
            + "    <if test=\"obj.backgroundColor != null\">\n"
            + "         \"background_color\" = #{obj.backgroundColor},\n"
            + "    </if>\n"
            + "    <if test=\"obj.couponImageUrl != null\">\n"
            + "         \"coupon_image_url\" = #{obj.couponImageUrl},\n"
            + "    </if>\n"
            + "    <if test=\"obj.useMethod != null\">\n"
            + "         \"use_method\" = #{obj.useMethod},\n"
            + "    </if>\n"
            + "    <if test=\"obj.isMember != null\">\n"
            + "         \"is_member\" = #{obj.isMember},\n"
            + "    </if>\n"
            + "    <if test=\"obj.tags != null\">\n"
            + "         \"tags\" = #{obj.tags},\n"
            + "    </if>\n"
            + "    <if test=\"obj.hideLink != null\">\n"
            + "         \"hide_link\" = #{obj.hideLink},\n"
            + "    </if>\n"
            + "    <if test=\"obj.expiredTip != null\">\n"
            + "         \"expired_tip\" = #{obj.expiredTip},\n"
            + "    </if>\n"
            + "    <if test=\"obj.expiredDays != null\">\n"
            + "         \"expired_days\" = #{obj.expiredDays},\n"
            + "    </if>\n"
            + "    <if test=\"obj.sendScopeImportResult != null\">\n"
            + "         \"send_scope_import_result\" = #{obj.sendScopeImportResult},\n"
            + "    </if>\n"
            + "    <if test=\"obj.storeScopeImportResult != null\">\n"
            + "         \"store_scope_import_result\" = #{obj.storeScopeImportResult},\n"
            + "    </if>\n"
            + "    <if test=\"obj.accountScope != null\">\n"
            + "         \"account_scope\" = #{obj.accountScope},\n"
            + "    </if>\n"
            + "    <if test=\"obj.receiveScope != null\">\n"
            + "         \"receive_scope\" = #{obj.receiveScope},\n"
            + "    </if>\n"
            + "    <if test=\"obj.status != null\">\n"
            + "         \"status\" = #{obj.status},\n"
            + "    </if>\n"
            + "update_time = now() "
            + "WHERE id = #{obj.id}"
            + "</script>")
    void updateCouponInfo(@Param("obj") WechatCouponEntity wechatCouponEntity);

    @Select("<script> select * from wechat_coupon where  ea = #{ea} order by create_time desc </script>")
    List<WechatCouponEntity> queryByEa(@Param("ea") String ea);

    @Update("<script> update wechat_coupon set send_down_status = #{sendStatus} where  id = #{objectId}  </script>")
    void updateSendStatus(@Param("objectId") String objectId, @Param("sendStatus") int sendStatus);

    @Select("<script> select * from wechat_coupon where  ea = #{ea} and scene = #{scene} order by create_time desc </script>")
    List<WechatCouponEntity> queryByEaAndScene(@Param("ea")String erUpstreamEa, @Param("scene") int type,@Param("page") Page page);

    @Select("<script> select * from wechat_coupon where  ea = #{param.ea} and status != 2 and create_coupon_type = 1 "
            + " <if test=\"param.stockName != null and param.stockName !='' \">\n"
            + "  AND stock_name LIKE CONCAT('%',#{param.stockName},'%')\n"
            + " </if>\n"
            + " order by create_time desc  </script>")
    List<WechatCouponEntity> queryFxCouponList(@Param("param") QueryCouponParam param, @Param("page") Page page);

    @Select("<script>" +
            "select * from  wechat_coupon " +
            "where id in " +
            "<foreach collection='ids' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            "</script>")
    List<WechatCouponEntity> getByIds(@Param("ids") List<String> ids);

    @Select("<script> select * from wechat_coupon where ea = #{ea} and stock_id = #{couponId} </script>")
    WechatCouponEntity queryDetailByCouponIdAndEa(@Param("ea") String ea, @Param("couponId") String couponId);


    @Select("<script> select * from wechat_coupon where  ea = #{ea} and scene = #{scene} and status = 0 order by create_time desc </script>")
    List<WechatCouponEntity> queryListByEaAndScene(@Param("ea")String erUpstreamEa, @Param("scene") int type);

    @Select("<script>" +
            "select * from  wechat_coupon " +
            "where ea = #{ea} and stock_id in " +
            "<foreach collection='stockIds' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            " order by create_time desc" +
            "</script>")
    List<WechatCouponEntity> queryByEaAndStockIds(@Param("ea") String ea, @Param("stockIds") List<String> couponIds);

    @Select("<script> select * from wechat_coupon where ea = #{ea} and coupon_id = #{couponId} </script>")
    WechatCouponEntity queryCouponDetailByObjectId(@Param("ea") String ea,@Param("couponId") String couponId);

    @Select("<script> select * from wechat_coupon where coupon_id = #{couponId} </script>")
    WechatCouponEntity queryCouponByObjectId(@Param("couponId") String couponId);

    @FilterLog
    @Select("<script>" +
            "select * from  wechat_coupon " +
            "where status != 2  and coupon_id in " +
            "<foreach collection='couponIds' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            " order by create_time desc" +
            "</script>")
    List<WechatCouponEntity> queryCouponIds(@Param("couponIds") List<String> couponIds);


    @Select("<script>" +
            "select * from  wechat_coupon " +
            "where stock_id in " +
            "<foreach collection='stockIds' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            " order by create_time desc" +
            "</script>")
    List<WechatCouponEntity> queryStockIds(@Param("stockIds") List<String> stockIds);


    @Update("<script> update wechat_coupon set store_scope = #{storeScope} where  id = #{objectId}  </script>")
    int updateStoreScope(@Param("objectId") String objectId, @Param("storeScope") String storeScope);

    @FilterLog
    @Select("<script> select * from wechat_coupon where  ea = #{ea} and status = 0 order by create_time desc </script>")
    List<WechatCouponEntity> queryAllByEa(@Param("ea")String ea);

    @FilterLog
    @Select("<script>" +
            "select id,receive_scope,store_scope from  wechat_coupon " +
            "where status = 0  and coupon_id in " +
            "<foreach collection='couponIds' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            " order by create_time desc" +
            "</script>")
    List<WechatCouponEntity> queryByCouponObjectIds(@Param("couponIds") List<String> couponIds);
}
