package com.facishare.marketing.provider.entity.qr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class QRPosterEntity implements Serializable {

    /**
     * UUID自增主键
     */
    private String id;

    /**
     * 市场活动ID
     */
    private String marketingEventId;

    /**
     * 跳转类型
     * {@link com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum}
     */
    private Integer forwardType;

    /**
     * 二维码ID
     * {@link com.facishare.marketing.provider.entity.qr.QRCodeEntity}
     */
    private Integer qrCodeId;

    /**
     * 外部二维码URL
     */
    private String externalQrUrl;

    /**
     * 跳转素材ID(营销素材为素材ID，公众号渠道二维码为公众号APPID+二维码ID，网页为空)
     */
    private String targetId;

    /**
     * 状态
     * {@link com.facishare.marketing.common.enums.qr.QRPosterStatusEnum}
     */
    private Integer status;

    /**
     * 企业账号
     */
    private String ea;

    /**
     * 海报标题
     */
    private String title;

    /**
     * 海报背景图apath
     */
    private String bgApath;

    /**
     * 海报背景图缩略图apath
     */
    private String bgThumbnailApath;

    /**
     * 最终效果图apath
     */
    private String apath;

    /**
     * 最终效果图缩略图apath
     */
    private String thumbnailApath;

    /**
     * 二维码样式
     */
    private String qrStyle;

    /**
     * 创建人userId
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 海报类型
     * {@link com.facishare.marketing.common.enums.qr.QRPosterTypeEnum}
     */
    private Integer type;

    /**
     * advance_poster_relation表id，标识该海报为外部海报
     */
    private String advanceRelationId;

    private String posterStyle;

    private Boolean isMobileDisplay = false;

    private Integer userAddSettings;

    private Integer failedOperation;
}
