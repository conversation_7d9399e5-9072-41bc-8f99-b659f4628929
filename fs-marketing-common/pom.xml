<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-marketing</artifactId>
    <groupId>com.facishare.marketing</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-marketing-common</artifactId>
  <version>1.0.1-SNAPSHOT</version>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
<!--    <dependency>-->
<!--      <groupId>com.fxiaoke</groupId>-->
<!--      <artifactId>fs-enterpriserelation-rest-api2</artifactId>-->
<!--      <version>2.0.0-SNAPSHOT</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>com.fxiaoke.msg</groupId>
      <artifactId>fs-message-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.zhxing</groupId>
      <artifactId>retrofit-spring</artifactId>
      <version>1.3.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mybatis-spring-support</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.3.5</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>

    <!--spring-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ning</groupId>
      <artifactId>async-http-client</artifactId>
      <version>1.9.40</version>
    </dependency>
    <dependency>
      <groupId>com.esotericsoftware</groupId>
      <artifactId>reflectasm</artifactId>
      <version>1.11.3</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
    </dependency>
      <!--
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>3.9</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-excelant</artifactId>
      <version>3.9</version>
    </dependency>
    -->

      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-excelant</artifactId>
          <version>3.17</version>
      </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.8.0</version>
      <scope>compile</scope>
    </dependency>
      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-paas-rule-rest-api</artifactId>
          <version>1.0.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <artifactId>retrofit-spring</artifactId>
            <groupId>com.github.zhxing</groupId>
          </exclusion>
        </exclusions>
      </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>1.55</version>
    </dependency>
      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-common-mq</artifactId>
      </dependency>
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
      </dependency>
      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>jedis-spring-support</artifactId>
          <scope>compile</scope>
      </dependency>

      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-websocket</artifactId>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-messaging</artifactId>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>i18n-client</artifactId>
      </dependency>
      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-fcp-biz-server</artifactId>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.facishare.mankeep</groupId>
          <artifactId>fs-mankeep-common</artifactId>
          <version>1.0.1-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <groupId>org.apache.poi</groupId>
                  <artifactId>poi</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>org.apache.poi</groupId>
                  <artifactId>poi-excelant</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>poi-ooxml</artifactId>
                  <groupId>org.apache.poi</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>poi-ooxml-schemas</artifactId>
                  <groupId>org.apache.poi</groupId>
              </exclusion>
          </exclusions>
      </dependency>

  </dependencies>
</project>