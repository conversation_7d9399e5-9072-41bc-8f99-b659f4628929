/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.web.controller.usermarketingaccounttag;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.*;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.ListTagModelResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.ListTagsByTagGroupResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.TagModelResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.TagModelTemplateResult;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 25/03/2019
 */
@RestController
@RequestMapping("userMarketingTag")
@Slf4j
@Api(description = "营销用户标签")
public class UserMarketingTagController {
    @Autowired
    private UserMarketingTagService userMarketingTagService;

    @CheckIdentityTrigger
    @RequestMapping(value = "listTagModelTemplates", method = RequestMethod.POST)
    @ApiOperation(value = "拉取标签模型模板列表", tags="5.1")
    public Result<List<TagModelTemplateResult>> listTagModelTemplates(){
        return userMarketingTagService.listTagModelTemplates();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listTagModel", method = RequestMethod.POST)
    @ApiOperation(value = "拉取标签模型列表及内部标签", tags="5.1")
    public Result<ListTagModelResult> listTagModel(@RequestBody ListTagModelArg arg){
        return userMarketingTagService.listTagModel(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getTagModel", method = RequestMethod.POST)
    @ApiOperation(value = "获取标签模型及内部标签, 当拖拽失败时，调用该方法获取最新的模型列表", tags="5.1")
    public Result<TagModelResult> getTagModel(@RequestBody JSONObject arg){
        String tagModelId = arg.getString("tagModelId");
        return userMarketingTagService.getTagModel(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), tagModelId);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "addModel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "添加模型", tags = "5.1")
    public Result<String> addModel(@RequestBody AddTagModelArg arg) {
        return userMarketingTagService.addModel(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateModel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改模型", tags = "5.1")
    public Result<String> updateModel(@RequestBody UpdateTagModelArg arg) {
        return userMarketingTagService.updateModel(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateModelState", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改模型状态", tags = "5.1")
    public Result<String> updateModelState(@RequestBody UpdateTagModelStateArg arg) {
        return userMarketingTagService.updateModelState(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId(), arg.getState());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "deleteModel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除标签", notes = "删除标签", tags = "5.1")
    public Result<Boolean> deleteModel(@RequestBody IdArg arg) {
        return userMarketingTagService.deleteModel(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "setOrCancelExclusive", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置或取消标签互斥", notes = "设置或取消标签互斥", tags = "5.1")
    public Result<Boolean> setOrCancelExclusive(@RequestBody SetOrCancelTagExclusiveArg arg) {
        return userMarketingTagService.setOrCancelExclusive(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "addFirstTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建一级标签", tags = {"5.1"})
    public Result<NestedId> addFirstTag(@RequestBody AddFirstTagAndSubTagsArg arg) {
        return userMarketingTagService.addFirstTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateFirstTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改一级标签", tags = {"5.1"})
    public Result<NestedId> updateFirstTag(@RequestBody UpdateFirstTagAndSubTagsArg arg) {
        return userMarketingTagService.updateFirstTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "addSecondTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "增加二级标签", notes = "增加标签", tags = {"1.9.2", "5.1"})
    public Result<String> addSecondTag(@RequestBody AddTagArg arg) {
        arg.verifyAndFixName();
        return userMarketingTagService.addSecondTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTagModelId(), arg.getParentTagId(), arg.getName(), arg.getSourceType());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateSecondTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改二级标签", tags = {"1.9.2", "5.1"})
    public Result<String> updateSecondTag(@RequestBody UpdateTagArg arg) {
        arg.verifyAndFixName();
        return userMarketingTagService.updateSecondTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTagModelId(), arg.getTagId(), arg.getName(), arg.getSourceType());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "deleteTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除标签", notes = "删除标签", tags = {"1.9.2", "5.1"})
    public Result<Boolean> deleteTag(@RequestBody DeleteTagArg arg) {
        return userMarketingTagService.deleteTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTagModelId(), arg.getTagId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getLatestDisplayOrder", method = RequestMethod.POST)
    @ApiOperation(value = "获取最新的展示顺序,拖动排序的时候要调用该方法", tags="5.1")
    public Result<GetLatestDisplayOrderResult> getLatestDisplayOrder(@ApiParam("列表的key,如果为模型列表则为:ALL_TAG_MODEL 标签列表为:TAG_IN_MODEL_${模型id}") @Param("displayKey") String displayKey){
        Preconditions.checkArgument(DisplayOrderConstants.checkDisplayKey(displayKey), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGCONTROLLER_139));
        return userMarketingTagService.getLatestDisplayOrder(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), displayKey);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateDisplayOrder", method = RequestMethod.POST)
    @ApiOperation(value = "更新展示顺序", tags="5.1")
    public Result<GetLatestDisplayOrderResult> updateDisplayOrder(@RequestBody UpdateDisplayOrderArg arg){
        Preconditions.checkArgument(DisplayOrderConstants.checkDisplayKey(arg.getDisplayKey()), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGCONTROLLER_139));
        return userMarketingTagService.updateDisplayOrder(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listTagsByTagGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "标签组查找标签列表", notes = "通过标签组查找标签列表", tags = {"1.9.2", "5.1"})
    public Result<PageResult<ListTagsByTagGroupResult>> listTagsByTagGroup(@RequestBody ListTagsByTagGroupArg arg) {
        return userMarketingTagService.listTagsByTagGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "setOrCancelAllowAddTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置或取消允许添加自定义标签", notes = "设置或取消允许添加自定义标签", tags = "5.1")
    public Result<Boolean> setOrCancelAllowAddTag(@RequestBody SetOrCancelAllowAddTagArg arg) {
        return userMarketingTagService.setOrCancelAllowAddTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "isAllowAddTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "是否允许添加自定义标签", notes = "是否允许添加自定义标签", tags = "5.1")
    public Result<Boolean> isAllowAddTag() {
        return userMarketingTagService.isAllowAddTag(UserInfoKeeper.getEa());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "setMaterialTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置内容标签", notes = "设置内容标签", tags = "5.1")
    public Result<Void> setMaterialTag(@RequestBody SetMaterialTagArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        Result checkResult = arg.checkParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        return userMarketingTagService.setMaterialTag(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "removeMaterialTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "移除内容标签", notes = "移除内容标签", tags = "5.1")
    public Result<Void> removeMaterialTag(@RequestBody RemoveMaterialTagArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        Result checkResult = arg.checkParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        return userMarketingTagService.removeMaterialTag(arg);
    }
}