package com.facishare.marketing.web.controller;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.ListFormDataArg;
import com.facishare.marketing.api.arg.QueryCountFormDataArg;
import com.facishare.marketing.api.arg.QueryObjectDescriptionDetailArg;
import com.facishare.marketing.api.result.FormDataListResult;
import com.facishare.marketing.api.result.QueryFormDataDetailResult;
import com.facishare.marketing.api.result.QueryObjectDescriptionDetailResult;
import com.facishare.marketing.api.service.FormDataService;
import com.facishare.marketing.api.service.ObjectDescriptionService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.ReflectionUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.ListFormDataResultArg;
import com.facishare.marketing.web.arg.QueryFormDataDetailArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 表单数据（参加产品报名人数）
 */
@RestController
@RequestMapping("formData")
@Slf4j
@Api(description = "表单数据", tags = "FormDataController")
public class FormDataController {

    @Autowired
    private FormDataService formDataService;
    @Autowired
    private ObjectDescriptionService objectDescriptionService;

    @CheckIdentityTrigger
    @RequestMapping(value = "listFormDatas", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询表单数据列表分页", notes = "查询表单数据列表分页", tags = "1.7.1")
    public Result<PageResult<FormDataListResult>> listFormDatas(@RequestBody ListFormDataResultArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        ListFormDataArg vo = BeanUtil.copy(arg, ListFormDataArg.class);
        vo.setBeginTime(arg.getBeginTime() == null ? null : new Date(arg.getBeginTime()));
        vo.setEndTime(arg.getEndTime() == null ? null : new Date(arg.getEndTime()));
        return formDataService.listFormData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getFormDataCount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询产品报名总数", notes = "查询产品报名总数", tags = "1.3")
    public Result<QueryFormDataDetailResult> getFormDataCount(@RequestBody QueryFormDataDetailArg arg) {
        QueryCountFormDataArg vo = BeanUtil.copy(arg, QueryCountFormDataArg.class);
        return formDataService.getFormDataCount(vo.getObjectId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "/exportFormData", method = RequestMethod.POST)
    @ApiOperation(value = "导出表单数据", notes = "导出表单数据", tags = "1.3")
    public void exportFormData(ListFormDataResultArg arg, HttpServletResponse httpServletResponse) throws Exception {
        ListFormDataArg vo = BeanUtil.copy(arg, ListFormDataArg.class);
        vo.setObjectType(ObjectTypeEnum.PRODUCT.getType());//产品类型 默认值为4
        vo.setPageNum(0);
        vo.setPageSize(100000);

        List<FormDataListResult> formDataListResults = formDataService.listFormData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo).getData().getResult();
        QueryObjectDescriptionDetailArg arg1 = new QueryObjectDescriptionDetailArg();
        arg1.setEa(UserInfoKeeper.getEa());
        arg1.setApiName("try_out_product_form");
        QueryObjectDescriptionDetailResult queryObjectDescriptionDetailResult = objectDescriptionService.getObjectDescriptionDetail(arg1).getData();
        StringBuilder title = new StringBuilder(queryObjectDescriptionDetailResult.getDisplayName());
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        String EXCEL_TITLE = I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_FORMDATACONTROLLER_95) + ".xlsx";
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, EXCEL_TITLE);
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_FORMDATACONTROLLER_94));
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        FieldInfoList fieldInfos = queryObjectDescriptionDetailResult.getFieldDescribes();
        List<String> titleList = generateExcelTitleList(fieldInfos);
        List<List<Object>> formDataList = generateExcelFormDataList(formDataListResults, fieldInfos);
        ExcelUtil.fillContent(xssfSheet, titleList, formDataList);
        httpServletResponse.setContentType(excelConfigMap.get(ExcelConfigEnum.CONTENT_TYPE).toString());
        httpServletResponse.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(EXCEL_TITLE, "UTF-8"));
        OutputStream bos = httpServletResponse.getOutputStream();
        xssfWorkbook.write(bos);
        httpServletResponse.flushBuffer();
        bos.flush();
        bos.close();
    }

    private List<String> generateExcelTitleList(FieldInfoList fieldInfos) {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_FORMDATACONTROLLER_114));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1750));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1740));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_FORMDATACONTROLLER_117));
        for (FieldInfo fieldInfo : fieldInfos) {
            titleList.add(fieldInfo.getLabel());
        }
        return titleList;
    }

    private List<List<Object>> generateExcelFormDataList(List<FormDataListResult> formDataListResults, FieldInfoList fieldInfos) throws Exception {
        List<List<Object>> datasList = Lists.newArrayList();
        for (FormDataListResult formDataListResult : formDataListResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(formDataListResult.getSpreadName());
            objList.add(formDataListResult.getCreateTime());
            objList.add(formDataListResult.getObjectName());
            objList.add(formDataListResult.getNickName());
            for (FieldInfo fieldInfo : fieldInfos) {
                Object result = ReflectionUtil.getFieldValue(fieldInfo.getApiName(), formDataListResult);
                result = fieldInfo.formatObjectData(fieldInfo.getType(), result);
                objList.add(result);
            }
            datasList.add(objList);
        }
        return datasList;
    }
}