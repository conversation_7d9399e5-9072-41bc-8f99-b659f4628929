package com.facishare.marketing.web.controller.cta;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.cta.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.cta.*;
import com.facishare.marketing.api.service.cta.CtaService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("cta")
@Slf4j
public class CtaController {
    @Autowired
    private CtaService ctaService;

    /**
     * 创建/更新CTA
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "addCta", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<AddCtaResult> addCta(@RequestBody com.facishare.marketing.web.arg.cta.AddCtaArg arg) {
        Preconditions.checkNotNull(arg, "CtaController.addCta failed arg is null");
        AddCtaArg vo = BeanUtil.copy(arg, AddCtaArg.class);
        return ctaService.addCta(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateCta", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> updateCta(@RequestBody com.facishare.marketing.web.arg.cta.AddCtaArg arg) {
        Preconditions.checkNotNull(arg, "CtaController.updateCta failed arg is null");
        AddCtaArg vo = BeanUtil.copy(arg, AddCtaArg.class);
        return ctaService.updateCta(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "deleteCta", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> deleteCta(@RequestBody com.facishare.marketing.web.arg.cta.AddCtaArg arg) {
        Preconditions.checkNotNull(arg, "CtaController.deleteCta failed arg is null");
        DeleteCtaArg vo = BeanUtil.copy(arg, DeleteCtaArg.class);
        return ctaService.deleteCta(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA详情
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "queryCtaDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryCtaDetailResult> queryCtaDetail(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaDetailArg arg){
        QueryCtaDetailArg vo = BeanUtil.copy(arg, QueryCtaDetailArg.class);
        return ctaService.queryCtaDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA详情关联内容列表
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "queryCtaRelationList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CtaRelationListResult> queryCtaRelationList(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaDetailArg arg){
        QueryCtaDetailArg vo = BeanUtil.copy(arg, QueryCtaDetailArg.class);
        return ctaService.queryCtaRelationList(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA详情
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "queryCtaSimpleDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryCtaSimpleDetailResult> queryCtaSimpleDetail(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaSimpleDetailArg arg){
        QueryCtaSimpleDetailArg vo = BeanUtil.copy(arg, QueryCtaSimpleDetailArg.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return ctaService.queryCtaSimpleDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 创建公众号关注二维码
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "createWxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateCtaWxQrCodeResult> createWxQrCode(@RequestBody com.facishare.marketing.web.arg.cta.CreateCtaWxQrCodeArg arg){
        CreateCtaWxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaWxQrCodeArg.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return ctaService.createWxQrCode(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 创建添加企微好友二维码
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "createQywxQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CreateCtaWxQrCodeResult> createQywxQrCode(@RequestBody com.facishare.marketing.web.arg.cta.CreateCtaQywxQrCodeArg arg){
        CreateCtaQywxQrCodeArg vo = BeanUtil.copy(arg, CreateCtaQywxQrCodeArg.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return ctaService.createQywxQrCode(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 轮询关注公众号状态
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "pollingWxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> pollingWxQrCodeStatus(@RequestBody com.facishare.marketing.web.arg.cta.PollingQrCodeStatusArg arg){
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingWxQrCodeStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 轮询企微添加好友状态
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "pollingQywxQrCodeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> createQywxQrCode(@RequestBody com.facishare.marketing.web.arg.cta.PollingQrCodeStatusArg arg){
        PollingQrCodeStatusArg vo = BeanUtil.copy(arg, PollingQrCodeStatusArg.class);
        return ctaService.pollingQywxQrCodeStatus(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA列表
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "listCta", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<CtaListResult> listCta(@RequestBody com.facishare.marketing.web.arg.cta.ListCtaArg arg){
        ListCtaArg vo = BeanUtil.copy(arg, ListCtaArg.class);
        return ctaService.listCta(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA名称
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "queryCtaName", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryCtaNameResult> queryCtaName(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaNameArg arg){
        QueryCtaNameArg vo = BeanUtil.copy(arg, QueryCtaNameArg.class);
        return ctaService.queryCtaName(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    /**
     * 查看CTA列表
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "queryCtaRelationCount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryCtaRelationCountResult> queryCtaRelationCount(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaRelationCountArg arg){
        QueryCtaRelationCountArg vo = BeanUtil.copy(arg, QueryCtaRelationCountArg.class);
        return ctaService.queryCtaRelationCount(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @ApiOperation(value = "获取CTA分组列表")
    @RequestMapping(value = "/listCtaGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<ObjectGroupListResult> listCtaGroup(@RequestBody ListGroupArg arg){
        return ctaService.listCtaGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取CTA分组适用角色")
    @RequestMapping(value = "/getGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> getGroupRole(@RequestBody JSONObject arg){
        String groupId = arg.getString("groupId");
        return ctaService.getGroupRole(groupId);
    }

    @ApiOperation(value = "CTA分组增加适用角色")
    @RequestMapping(value = "/addCtaGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> addCtaGroupRole(@RequestBody SaveObjectGroupVisibleArg arg){
        if (StringUtils.isBlank(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getRoleIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return ctaService.addCtaGroupRole(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "创建&编辑CTA分组")
    @RequestMapping(value = "/editCtaGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EditObjectGroupResult> editCtaGroup(@RequestBody EditObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return ctaService.editCtaGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除CTA分组")
    @RequestMapping(value = "/deleteCtaGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteCtaGroup(@RequestBody DeleteObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return ctaService.deleteCtaGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "CTA设置分组")
    @RequestMapping(value = "/setCtaGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> setCtaGroup(@RequestBody SetObjectGroupArg arg){
        if (org.apache.commons.lang3.StringUtils.isEmpty(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getObjectIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return ctaService.setCtaGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getCtaSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result getCtaSetting(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaDetailArg arg) {
        QueryCtaDetailArg vo = BeanUtil.copy(arg, QueryCtaDetailArg.class);
        return ctaService.getCtaSetting(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @RequestMapping(value = "saveCtaSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存Cta设置")
    public Result saveCtaSetting(@RequestBody com.facishare.marketing.web.arg.cta.AddCtaSettingArg arg) {
        AddCtaSettingArg vo = BeanUtil.copy(arg, AddCtaSettingArg.class);
        return ctaService.saveCtaSetting(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @RequestMapping(value = "queryCtaStaticData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取Cta行为数据")
    public Result<QueryCtaRelationCountResult> queryCtaStaticData(@RequestBody com.facishare.marketing.web.arg.cta.QueryCtaStaticDataArg arg) {
        QueryCtaStaticDataArg vo = BeanUtil.copy(arg, QueryCtaStaticDataArg.class);
        return ctaService.queryCtaStaticData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }
}