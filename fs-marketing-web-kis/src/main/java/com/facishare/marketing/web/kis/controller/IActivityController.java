package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.marketingactivity.ActivityListArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityVO;
import com.facishare.marketing.api.result.ActivityResult;
import com.facishare.marketing.api.result.conference.CheckSignInStatusResult;
import com.facishare.marketing.api.result.conference.GetEnrollTimeResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.arg.GetActivityInfoByIdArg;
import com.facishare.marketing.api.arg.conference.GetEnrollTimeArg;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * Created  By zhoux 2019/02/25
 **/
@FcpService("activity")
@RequestMapping("/web/activity")
public interface IActivityController {

    /**
     * 获取活动列表
     * @param arg
     * @return
     */
    @FcpMethod("listActivities")
    @ResponseBody
    @RequestMapping(value = "/listActivities", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result listActivities(@RequestBody com.facishare.marketing.web.kis.arg.ListActivityArg arg);

    /**
     * 获取活动详情
     * @param arg
     * @return
     */
    @FcpMethod("queryActivityInfoById")
    @ResponseBody
    @RequestMapping(value = "/queryActivityInfoById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<ActivityResult> queryActivityInfoById(@RequestBody GetActivityInfoByIdArg arg);

    /**
     * 活动签到
     * @return
     */
    @FcpMethod("signIn")
    @ResponseBody
    @RequestMapping(value = "/signIn", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result signIn(@RequestBody com.facishare.marketing.web.kis.arg.conference.SignInArg arg);

    /**
     * 判断用户是否签到
     * @param arg
     * @return
     */
    @FcpMethod("checkSignInStatus")
    @ResponseBody
    @RequestMapping(value = "/checkSignInStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckSignInStatusResult> checkSignInStatus(@RequestBody com.facishare.marketing.web.kis.arg.conference.CheckSignInStatusArg arg);

    @FcpMethod("list")
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<ActivityVO>> list(@RequestBody ActivityListArg arg);

    @FcpMethod(value = "queryActivityEnrollEndTime")
    @RequestMapping(value = "queryActivityEnrollEndTime", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询报名截止时间及提示信息", tags = "f-10.2")
    @ResponseBody
    Result<GetEnrollTimeResult> queryActivityEnrollEndTime(@RequestBody GetEnrollTimeArg arg);
}
