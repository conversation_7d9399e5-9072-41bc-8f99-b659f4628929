<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-marketing</artifactId>
    <groupId>com.facishare.marketing</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-marketing-api</artifactId>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>


  <dependencies>

    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-common</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.training</groupId>
      <artifactId>fs-training-outer-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>validation-api</artifactId>
          <groupId>javax.validation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-storage</artifactId>
      <version>0.0.5</version>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.14</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-util</artifactId>
    </dependency>
      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-crm-rest-api</artifactId>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml</artifactId>
          <version>3.17</version>
      </dependency>
      <dependency>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
          <version>3.5.13</version>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-paas-ai-api</artifactId>
          <version>1.0.1-SNAPSHOT</version>
          <scope>compile</scope>
      </dependency>
  </dependencies>
</project>