package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.common.result.*;

import java.util.List;

public interface StatisticService {
    Result<GetAllEmployeeSpreadStatisticDataResult> getAllEmployeeSpreadStatisticData(String ea, Integer fsUserId, GetAllEmployeeSpreadStatisticDataArg arg);

    Result<List<EmployeeRankingDataResult>> getAllEmployeeSpreadEmployeeRankingData(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    Result<PageResult<ObjectStatisticResult>> listObjectStatisticData(String ea, Integer fsUserId, ListObjectStatisticDataArg arg);

    Result<ObjectStatisticResult> getObjectStatisticDetail(String ea, Integer fsUserId, GetObjectStatisticDetailArg arg);

    /**
     * 下属推广数据简介
     * @param arg
     * @return
     */
//    Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> getEmployeeSpreadStatisticDataWithSubordinate(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg);
 /**
     * app 伙伴推广数据概览
     * @param arg
     * @return
     */
    Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> getEmployeeSpreadStatisticDataForPartner(String upstreamEa, String outTenantId,String outUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg);
    Result<GetEmployeeSpreadStatisticDataWithSubordinateResult> getEmployeeSpreadStatisticDataWithSubordinate(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDataWithSubordinateArg arg);

    /**
     * 下属推广排行榜
     * @param arg
     * @return
     */
    Result<com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult>> getEmployeeSpreadRankWithSubordinate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    /**
     * huoban app伙伴推广排行榜
     * @return
     */
    Result<com.facishare.marketing.api.result.PageResult<EmployeeRankingDataResult>> getEmployeeSpreadRankWithSubordinateForPartner(String upstreamEa, String outTenantId,String outUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    /**
     * 显示所有推广排行，包含未推广员工
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> getAllEmployeeMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    Result<Void> exportAllEmployeeMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    List<Integer> getStaticsticsVisitUserRange(String ea, Integer userId, List<Integer> userRange, List<Integer> departmentRanges);
    List<Integer> getStaticsticsVisitUserRange(String ea, Integer userId, List<Integer> userRange, List<Integer> departmentRange, boolean isStatistic);

    Result<com.facishare.marketing.common.result.PageResult<EmployeeRankingDataResult>> getPartnerMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    Result<Void> exportPartnerMarketingActivityStatisticsRankingDate(String ea, Integer fsUserId, GetAllEmployeeSpreadEmployeeRankingDataArg arg);

    /**
     * 记录员工的动作行为
     * @param recordEmployeeActionArg
     * @return
     */
    Result<Void> recordEmployeeAction(RecordEmployeeActionArg recordEmployeeActionArg);
    
    /**
     * 记录员工的动作行为
     * @param recordEmployeeActionArg
     * @return
     */
    Result<Void> recordVisitorAction(RecordVisitorActionArg recordEmployeeActionArg);

    /**
     * 增加员工可以查看全员推广效果的范围
     * @param ea
     * @param userId
     * @param arg
     * @return
     */
    Result addStatisticsVisitRange(String ea, Integer userId, AddStatisticsVisitRangeArg arg);

    /**
     * 删除员工可以查看全员推广效果的范围
     * @param ea
     * @param userId
     * @param arg
     * @return
     */
    Result deleteStatisticsVisitRange(String ea, Integer userId,DeleteStatisticsVisitRangeArg arg);

    /**
     * 查询员工可以查看全员推广效果的范围
     * @param arg
     * @return
     */
    Result<QueryStatisticsVisitRangeResult> queryEmployeeStatisticsVisitRange(String ea, Integer userId, QueryStatisticsVisitRangeArg arg);

    /**
     * 查询企业所有员工可以查看全员推广效果的范围
     * @param arg
     * @return
     */
    Result<List<QueryStatisticsVisitRangeResult>>  queryEnterpriseStatisticsVisitRange(String ea, Integer userId, QueryEnterpriseStatisticsVisitRangeArg arg);

   /**
    * 查询员工可以查看全员推广效果的范围
     * @param ea
    * @param userId
    * @return
    */
    Result updateEmployeeStatisticsVisitRange(String ea, Integer userId, AddStatisticsVisitRangeArg arg);

    /**
     * 用户推广概览与趋势
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result marketingUserSpreadOverviewAndTrend(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg);


    /**
     * 用户推广排行
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result marketingUserSpreadRanking(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg);

    /**
     * 导出用户推广排行
     * @param ea
     * @param fsUserId
     * @param arg
     */
    Result exportMarketingUserSpreadRanking(String ea, Integer fsUserId, MarketingUserSpreadStatisticsArg arg);

    /**
     * 统计物料-营销活动的数据
     */
    Result<ObjectUserMarketingStatisticsResult> getObjectMarketingActivityStatistics(ObjectUserMarketingStatisticsArg arg);

    //雷达全员推广+个人推广
    Result<ObjectUserMarketingStatisticsResult> getRadarObjectMarketingActivityStatistics(ObjectUserMarketingStatisticsArg arg);

    /**
     * 统计物料-营销活动的数据
     */
    Result<List<ObjectUserMarketingStatisticsResult>> batchGetObjectMarketingActivityStatistics(BatchObjectUserMarketingStatisticsArg arg);

    //雷达全员推广+个人推广
    Result<List<ObjectUserMarketingStatisticsResult>> batchGetRadarObjectMarketingActivityStatistics(BatchObjectUserMarketingStatisticsArg arg);

    /**
     * 统计物料-营销用户的数据明细
     */
    Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> getObjectUserMarketingList(ObjectUserMarketingListArg arg);

    Result<List<ObjectUserMarketingStatisticsResult>> getObjectMarketingActivityStatisticsBySpreadUserIdList(ObjectUserMarketingStatisticsArg arg);

    Result<com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult>> enterpriseSpreadUserMarketingList(EnterpriseSpreadUserMarketingListArg arg);

    Result<List<ObjectStatisticResult>> listObjectStatisticTop(String ea, Integer fsUserId, Integer recentDay, Integer top);

    Result<List<StatisticSpreadRankingResult>> StatisticSpreadRanking(StatisticSpreadRankingArg vo);

    Result<PageResult<pageUserMarketingActionCountResult>> pageFromUserMarketingActionStatisticSpreadRanking(StatisticSpreadRankingArg vo);

    /**
     * 推广简报 获取访问数/转发数明细列表
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<com.facishare.marketing.common.result.PageResult<ObjectUserMarketingListResult>> getEmployeeSpreadStatisticDetailsData(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDetailsDataArg arg);

    /**
     * 推广简报 获取线索明细列表
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result<com.facishare.marketing.common.result.PageResult<QueryFormUserDataResult>> getEmployeeSpreadClueDetailsData(String ea, Integer fsUserId, GetEmployeeSpreadStatisticDetailsDataArg arg);

    Result<QywxGroupSendStatisticDataResult> getQywxGroupSendStatisticData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> getQywxGroupSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    void exportQywxGroupSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    Result<QywxGroupSendStatisticDataResult> getQywxMomentSendStatisticData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    Result<com.facishare.marketing.common.result.PageResult<QywxGroupSendEmployeeRankingDataResult>> getQywxMomentSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    void exportQywxMomentSendEmployeeRankingData(String ea, Integer fsUserId, GetQywxStatisticDataArg arg);

    void officialWebsiteStatisticJob();
}
