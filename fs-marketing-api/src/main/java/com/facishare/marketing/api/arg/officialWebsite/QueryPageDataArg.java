package com.facishare.marketing.api.arg.officialWebsite;

import com.facishare.marketing.api.arg.BasePageArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created  By zhoux 2019/11/26
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryPageDataArg extends BasePageArg {

    private String name;

    private Integer status;

    private String ea;

    private Integer fsUserId;

}
