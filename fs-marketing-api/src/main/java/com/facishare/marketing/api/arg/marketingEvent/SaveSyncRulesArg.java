package com.facishare.marketing.api.arg.marketingEvent;

import com.facishare.marketing.common.enums.I18nKeyEnumV3;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @IgnoreI18nFile
 */
@Data
public class SaveSyncRulesArg implements Serializable {

    private String marketingEventId;
    private List<SyncRule> rules;

    public Result validateParam() {
        if (Objects.isNull(this.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_MARKETINGEVENT_SAVESYNCRULESARG_23));
        }

        if (CollectionUtils.isEmpty(rules) || rules.get(0) == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_MARKETINGEVENT_SAVESYNCRULESARG_27));
        }

        for (SyncRule rule : rules) {
            if (CollectionUtils.isEmpty(rule.getTargetMarketingEvents())) {
                return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_MARKETINGEVENT_SAVESYNCRULESARG_32));
            }
            for (TargetMarketingEvent targetMarketingEvent : rule.getTargetMarketingEvents()) {
                if (!targetMarketingEvent.validateParam()) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), I18nUtil.get(I18nKeyEnumV3.MARK_MARKETINGEVENT_SAVESYNCRULESARG_36));
                }

            }
        }

        return Result.newSuccess();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SyncRule implements Serializable {
        private List<TargetMarketingEvent> targetMarketingEvents;
        private RuleGroupList ruleGroupJson;
    }

    @Data
    public static class TargetMarketingEvent implements Serializable {
        private String marketingEventId;
        private String name;

        public boolean validateParam() {
            if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(name)) {
                return false;
            }

            return true;
        }
    }

}