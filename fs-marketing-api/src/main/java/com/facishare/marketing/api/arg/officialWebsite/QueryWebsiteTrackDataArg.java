package com.facishare.marketing.api.arg.officialWebsite;

import com.facishare.marketing.api.arg.BasePageArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created  By zhoux 2019/11/29
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryWebsiteTrackDataArg extends BasePageArg {

    private String ea;

    private Integer fsUserId;

    private Integer orderType;

    private String keyword;

    private String websiteId;

    private Long startTime;

    private Long endTime;
}
