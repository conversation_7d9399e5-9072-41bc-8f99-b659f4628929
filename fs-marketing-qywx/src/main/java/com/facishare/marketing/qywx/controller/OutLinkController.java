package com.facishare.marketing.qywx.controller;

import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.service.OutLinkService;
import com.facishare.marketing.api.vo.OutLinkVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("outLink")
@Slf4j
@Api(value = "外部链接", tags = "outLink")
public class OutLinkController {

    @Autowired
    private OutLinkService outLinkService;

    /**
     * 根据ID获取外部链接详情
     */
    @TokenCheckTrigger
    @RequestMapping(value = "getById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("根据ID获取外部链接详情")
    public Result<OutLinkVO> getById(@RequestBody IdArg arg) {
        if (arg == null || arg.getId() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return outLinkService.getById(arg.getId());
    }

}
